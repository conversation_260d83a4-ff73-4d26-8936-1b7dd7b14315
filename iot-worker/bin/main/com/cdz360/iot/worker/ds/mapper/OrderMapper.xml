<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.worker.ds.mapper.OrderMapper">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.iot.worker.model.order.OrderPo">
        insert into
        t_order(gwno, orderNo, evseId, plugId, `status`,
        `startTime`,  createTime, updateTime
        )
        values(#{gwno}, #{orderNo}, #{evseId}, #{plugId}, #{status},
        #{startTime},
        now(), now()
        )
    </insert>


    <select id="getByOrderNo" resultType="com.cdz360.iot.worker.model.order.OrderPo">
        select o.* from t_order o
        where o.orderNo=#{orderNo}
        <if test="lock == true">
            for update
        </if>
    </select>

    <update id="update" parameterType="com.cdz360.iot.worker.model.order.OrderPo">
        update t_order set
        plugId=#{plugId},
        `status`=#{status},
        startTime=#{startTime},
        stopTime=#{stopTime},
        <if test="@com.cdz360.base.utils.StringUtils@isEmpty( carNo ) == false">
            carNo=#{carNo},
        </if>
        updateTime=now()
        where orderNo=#{orderNo}
    </update>
</mapper>