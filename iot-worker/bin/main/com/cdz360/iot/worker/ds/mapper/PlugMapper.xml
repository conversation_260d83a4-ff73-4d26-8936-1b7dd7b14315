<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.worker.ds.mapper.PlugMapper">

    <insert id="batchInsert">
        insert into
        t_plug(evseId, plugId, plugStatus, `name`, createTime,
        updateTime,voltageMin,voltageMax,currentMin,currentMax,power
        )
        <foreach collection="plugs" open="values" close=""
                 separator="," item="plug">
            (#{plug.evseId}, #{plug.plugId}, #{plug.plugStatus},
            #{plug.name},
            now(), now(),#{plug.voltageMin},#{plug.voltageMax},#{plug.currentMin},#{plug.currentMax},#{plug.power}
            )
        </foreach>
    </insert>

    <update id="update" parameterType="com.cdz360.iot.model.evse.po.PlugPo">
        update t_plug set plugStatus = #{plugStatus},
        <if test="name != null">
            `name` = #{name},
        </if>
        updateTime=now()
        where id=#{id}
    </update>

    <update id="batchUpdate" parameterType="com.cdz360.iot.model.evse.po.PlugPo">
        <foreach collection="list" item="p" open="" close="" separator=";">
            update t_plug set plugStatus = #{p.plugStatus},
            <if test="p.name != null">
                `name` = #{p.name},
            </if>
            <if test="p.voltageMin != null">
                `voltageMin` = #{p.voltageMin},
            </if>
            <if test="p.voltageMax != null">
                `voltageMax` = #{p.voltageMax},
            </if>
            <if test="p.currentMin != null">
                `currentMin` = #{p.currentMin},
            </if>
            <if test="p.currentMax != null">
                `currentMax` = #{p.currentMax},
            </if>
            <if test="p.power != null">
                `power` = #{p.power},
            </if>
            updateTime=now()
            where id=#{p.id}
        </foreach>
    </update>

    <update id="updateOrderNo" parameterType="com.cdz360.iot.model.evse.po.PlugPo">
        update t_plug set orderNo = #{orderNo}, updateTime=now()
        where evseId=#{evseId} and plugId = #{plugId}
    </update>

    <update id="batchUpdateOrderNo" parameterType="com.cdz360.iot.model.evse.po.PlugPo">
        update t_plug set orderNo = #{orderNo}, updateTime=now()
        where id in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getPlug" resultType="com.cdz360.iot.model.evse.po.PlugPo">
        select * from t_plug where evseId=#{evseId}
        and plugId=#{plugId}
        <if test="lock == true">
            for update
        </if>
    </select>

    <select id="getPlugByEvseId" resultType="com.cdz360.iot.model.evse.po.PlugPo">
        select * from t_plug where evseId=#{evseId}
        <if test="lock == true">
            for update
        </if>
    </select>

    <select id="listPlug" resultType="com.cdz360.iot.model.evse.po.PlugPo">
        select * from t_plug where 1=1
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( evseId )">
            and evseId=#{evseId}
        </if>
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( statusList )">
            and plugStatus in
            <foreach collection="statusList" open="(" close=")"
                     separator="," item="status">
                #{status}
            </foreach>
        </if>
        order by id desc
        <choose>
            <when test="start != null and size != null">
                limit #{start},#{size}
            </when>
            <when test="size != null">
                limit #{size}
            </when>
        </choose>
        <if test="lock == true">
            for update
        </if>
    </select>

    <delete id="removeByEvseId">
        delete
        from t_plug
        where evseId = #{evseId}
    </delete>

<!--    <select id="getPlugList" parameterType="com.cdz360.iot.model.evse.ListPlugParam"-->
<!--            resultType="com.cdz360.base.model.iot.vo.PlugVo">-->
<!--        SELECT-->
<!--        e.gwno,e.evseId as evseNo,CONCAT(e.evseId,0,p.plugId) as plugNo,s.commId as siteCommId,s.dzId as siteId,s.`name`-->
<!--        as siteName,p.plugId as idx,p.`name`,p.plugStatus as `status`,-->
<!--        e.supply,p.orderNo,p.updateTime,p.voltageMin as minVoltage,p.voltageMax as maxVoltage,p.currentMin as-->
<!--        minCurrent,-->
<!--        p.currentMax as maxCurrent-->
<!--        FROM-->
<!--        t_plug p-->
<!--        INNER JOIN t_evse e ON p.evseId = e.evseId-->
<!--        INNER JOIN t_site s ON e.siteId = s.dzId-->
<!--        <where>-->
<!--            <if test="evseNoList != null and evseNoList.size() >0 ">-->
<!--                e.evseId IN-->
<!--                <foreach collection="evseNoList" index="index" item="item"-->
<!--                         open="(" close=")" separator=",">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="siteIdList != null and siteIdList.size() > 0">-->
<!--                AND s.dzId IN-->
<!--                <foreach collection="siteIdList" index="index" item="item"-->
<!--                         open="(" close=")" separator=",">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
<!--        </where>-->
<!--        <if test="start != null and size != null">-->
<!--            limit #{start},#{size}-->
<!--        </if>-->
<!--    </select>-->

</mapper>