<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.worker.ds.mapper.GwLogTmpMapper">

    <!--insert id="batchInsert" >
        insert into
        t_gw_log_tmp(channelId, clientId, clientIp, eventIndex, eventType, time, gwno, createTime, updateTime
        )
        <foreach collection="logs" open="values" close=""
                 separator="," item="log">
            (#{log.channelId}, #{log.clientId}, #{log.clientIp}, #{log.eventIndex}, #{log.eventType}, #{log.time}, #{log.gwno},
            now(), now()
            )
        </foreach>
    </insert!-->

    <select id="getGroupGwno" resultType="java.lang.String">
        select gwno from t_gw_log_tmp group by gwno
    </select>

    <select id="getLastTmpLog" resultType="com.cdz360.iot.worker.model.iot.po.GwLogPo"
            parameterType="java.lang.String">
        select * from t_gw_log_tmp where eventIndex=(select max(eventIndex) from t_gw_log_tmp where gwno=#{gwno})
    </select>

    <delete id="deleteBefore">
        delete from t_gw_log_tmp where gwno=#{gwno} and #{beforeTime}>=time
    </delete>

    <delete id="copyOut">
        insert into t_gw_log (gwno, channelId, clientId, clientIp, eventIndex, eventType, time, createTime, updateTime)
        select gwno, channelId, clientId, clientIp, eventIndex, eventType, time, now(), now() from t_gw_log_tmp where
        gwno=#{gwno} and #{beforeTime}>=time
    </delete>
</mapper>