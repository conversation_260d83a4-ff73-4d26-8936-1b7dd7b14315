package com.cdz360.iot.worker.model.order;

import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.iot.model.base.DbObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

@Schema(description = "充电订单信息")
public class OrderPo extends DbObject {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "充电桩编号")
    private String evseId;

    @Schema(description = "充电枪编号")
    private int plugId = -1;

    @Schema(description = "订单状态")
    private ChargeOrderStatus status;

    @Schema(description = "充电开始时间")
    private Date startTime;

    @Schema(description = "充电结束时间")
    private Date stopTime;

    @Schema(description = "车牌号", required = false)
    private String carNo;

    public String getOrderNo() {
        return orderNo;
    }

    public OrderPo setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public String getEvseId() {
        return evseId;
    }

    public OrderPo setEvseId(String evseId) {
        this.evseId = evseId;
        return this;
    }

    public int getPlugId() {
        return plugId;
    }

    public OrderPo setPlugId(int plugId) {
        this.plugId = plugId;
        return this;
    }

    public ChargeOrderStatus getStatus() {
        return status;
    }

    public OrderPo setStatus(ChargeOrderStatus status) {
        this.status = status;
        return this;
    }

    public Date getStartTime() {
        return startTime;
    }

    public OrderPo setStartTime(Date startTime) {
        this.startTime = startTime;
        return this;
    }

    public Date getStopTime() {
        return stopTime;
    }

    public OrderPo setStopTime(Date stopTime) {
        this.stopTime = stopTime;
        return this;
    }

    public String getCarNo() {
        return carNo;
    }

    public OrderPo setCarNo(String carNo) {
        this.carNo = carNo;
        return this;
    }
}
