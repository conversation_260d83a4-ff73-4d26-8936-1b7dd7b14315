package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.worker.ds.service.EvseService;
import com.cdz360.iot.worker.ds.service.PlugService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class EvseRefreshService {

    @Autowired
    private EvseService evseService;
    @Autowired
    private PlugService plugService;
    @Autowired
    private RedisIotReadService redisIotReadService;

    public void refreshEvseStatus() {
        long start = 0;
        int size = 100;

        List<EvseStatus> statusList = List.of(EvseStatus.IDLE, EvseStatus.BUSY, EvseStatus.CONNECT,
                EvseStatus.ERROR);
        List<EvsePo> evseList;
        do {
            evseList = this.evseService.listEvse(statusList, start, size);
            start = start + evseList.size();
            this.refreshEvses(evseList);
        } while (CollectionUtils.isNotEmpty(evseList));
    }

    public void refreshPlugStatus() {
        long start = 0;
        int size = 100;

        List<PlugStatus> statusList = List.of(PlugStatus.IDLE, PlugStatus.BUSY, PlugStatus.CONNECT,
                PlugStatus.ERROR, PlugStatus.RECHARGE_END);
        List<PlugPo> plugList;
        do {
            plugList = this.plugService.listPlug(null, statusList, start, size, false);
            start = start + plugList.size();
            this.refreshPlugs(plugList);
        } while (CollectionUtils.isNotEmpty(plugList));
    }


    public void refreshEvses(List<EvsePo> evseList) {
        log.info("evseList.size = {}", evseList.size());
        evseList.stream().forEach(this::refreshEvse);
    }

    public void refreshPlugs(List<PlugPo> plugList) {
        log.info("plugList.size = {}", plugList.size());
        plugList.stream().forEach(this::refreshPlug);
    }

    private void refreshEvse(EvsePo evse) {
        EvseVo cache = this.redisIotReadService.getEvseRedisCache(evse.getEvseId());
        boolean changed = false;
        if (cache == null) {
            log.warn("缓存中没有桩信息, 将桩设置为离线. evse = {}", evse);
            evse.setEvseStatus(EvseStatus.OFFLINE);
            changed = true;
        } else if (evse.getEvseStatus() != cache.getStatus()) {
            log.warn("缓存中桩状态有变更 数据库状态: {}, 缓存状态: {}", evse.getEvseStatus(), cache.getStatus());
            evse.setEvseStatus(cache.getStatus());
            changed = true;
        }
        if (changed) {
            this.evseService.update(evse);
        }
    }

    private void refreshPlug(PlugPo plug) {
        PlugVo cache = this.redisIotReadService.getPlugRedisCache(plug.getEvseId(), plug.getPlugId());
        boolean changed = false;
        if (cache == null) {
            log.warn("缓存中没有枪头信息, 将枪头设置为离线. plug = {}", plug);
            plug.setPlugStatus(PlugStatus.OFFLINE);
            changed = true;
        } else if (plug.getPlugStatus() != cache.getStatus()) {
            log.warn("缓存中枪头状态有变更 数据库状态: {}, 缓存状态: {}", plug.getPlugStatus(), cache.getStatus());
            plug.setPlugStatus(cache.getStatus());
            changed = true;
        }
        if (changed) {
            this.plugService.update(plug);
        }
    }

}
