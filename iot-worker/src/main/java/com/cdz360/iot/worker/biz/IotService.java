package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.model.iot.dto.PlugMqDto;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.PlugNoUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.data.cache.RedisIotRwService;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.biz.IotCacheService;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.common.utils.MqttUtils;
import com.cdz360.iot.common.utils.RedisIdGenerator;
import com.cdz360.iot.ds.EvsePasscodeRoDs;
import com.cdz360.iot.ds.GwInfoService;
import com.cdz360.iot.ds.ro.EvseModelRoDs;
import com.cdz360.iot.ds.ro.SiteRoDs;
import com.cdz360.iot.ds.ro.TransformerRoDs;
import com.cdz360.iot.model.base.EvseCfgResponse;
import com.cdz360.iot.model.base.ListResponse;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.ListEvseParam;
import com.cdz360.iot.model.evse.ListPlugParam;
import com.cdz360.iot.model.evse.cfg.CfgEvseAllV2;
import com.cdz360.iot.model.evse.cfg.CfgEvseV2;
import com.cdz360.iot.model.evse.po.EvsePasscodePo;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.model.evse.upgrade.UpgradeEvseResultEnum;
import com.cdz360.iot.model.evse.upgrade.UpgradeEvseResultRequest;
import com.cdz360.iot.model.gw.GwObjReqMsg;
import com.cdz360.iot.model.gw.GwObjResMsg;
import com.cdz360.iot.model.gw.vo.EvseOpInfoVo;
import com.cdz360.iot.model.register.GwRegisterResult;
import com.cdz360.iot.model.transformer.po.TransformerPo;
import com.cdz360.iot.worker.ds.service.EvseService;
import com.cdz360.iot.worker.ds.service.PlugService;
import com.cdz360.iot.worker.feign.BizDataCoreFeignClient;
import com.cdz360.iot.worker.feign.BizTradingFeignClient;
import com.cdz360.iot.worker.feign.IotDeviceMgmFeignClient;
import com.cdz360.iot.worker.feign.OpenHlhtRestFeignClient;
import com.cdz360.iot.worker.model.gw.CfgEvseReqV2;
import com.cdz360.iot.worker.model.gw.CfgEvseResultReqV2;
import com.cdz360.iot.worker.model.gw.EvsePasscodeReqV2;
import com.cdz360.iot.worker.model.gw.GwRegisterReqV2;
import com.cdz360.iot.worker.model.gw.IotGwCmdResultLimitSoc;
import com.cdz360.iot.worker.model.gw.IotGwCmdResultReqV2;
import com.cdz360.iot.worker.model.gw.IotGwCmdResultStartCharge;
import com.cdz360.iot.worker.model.gw.IotGwCmdResultUpgrade;
import com.cdz360.iot.worker.model.gw.LoginRes;
import com.cdz360.iot.worker.model.gw.RegisterRes;
import com.cdz360.iot.worker.model.gw.UpgradeReq;
import com.cdz360.iot.worker.model.iot.UpdateEvseCacheDto;
import com.cdz360.iot.worker.model.iot.param.GwLoginParam;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.math.BigDecimal;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;


@Slf4j
@Service
public class IotService {


    @Autowired
    private IotCacheService iotCacheService;
    @Autowired
    private EvseService evseService;
    @Autowired
    private RedisIotUpdateWrapper redisIotUpdateWrapper;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private RedisIotRwService redisIotRwService;
    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private PlugService plugService;
    @Autowired
    private DcEventPublisher dcEventPublish;
    @Autowired
    private GwInfoService gwInfoService;
    @Autowired
    private EvseCfgRedisService evseCfgRedisService;
    @Value("${env}")
    private String env;

    @Value("${iot.gw.mqtt.type}")
    private String mqttType;
    @Value("${iot.gw.mqtt.id}")
    private String mqttId;
    @Value("${iot.gw.mqtt.group}")
    private String mqttGroup;
    @Value("${iot.gw.mqtt.clientUrl}")
    private String mqttClientUrl;
    @Value("${iot.gw.mqtt.topicPrefix}")
    private String mqttTopicPrefix;
    @Value("${iot.gw.mqtt.lastWillTopic}")
    private String mqttLastWillTopic;
    @Value("${iot.gw.mqtt.accessKey}")
    private String mqttAccessKey;
    @Value("${iot.gw.mqtt.accessSecret}")
    private String mqttAccessSecret;
    @Value("${iot.gw.mqtt.username}")
    private String mqttUsername;
    @Value("${iot.gw.mqtt.password}")
    private String mqttPassword;
    @Autowired
    private GwRegisterService gwRegisterService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private IotDeviceMgmFeignClient deviceMgMClient;


    @Autowired
    private BusinessService businessService;
//
//    @Autowired
//    private DzBcChargeOrderService dzBcChargeOrderService;

    @Autowired
    private BizTradingFeignClient bizTradingReactiveFeignClient;

    @Autowired
    private BizDataCoreFeignClient bizDataCoreFeignClient;

    @Autowired
    private OpenHlhtRestFeignClient openHlhtRestFeignClient;

    @Autowired
    private EvsePasscodeRoDs evsePasscodeRoDs;

    @Autowired
    private TransformerRoDs transformerRoDs;

    @Autowired
    private EvseModelRoDs evseModelRoDs;

    public IotService() {
    }

    /**
     * 网关初始化
     *
     * @param req
     * @param wanIp
     * @return
     */
    public RegisterRes register(String gwno, GwRegisterReqV2 req, String wanIp, int ver) {

        AtomicReference<RegisterRes> ret = new AtomicReference<>();
        ret.set(new RegisterRes());
        //result.set(new RegisterRes());

        GwRegisterResult result = gwRegisterService.doRegister(gwno, req.getMac(), req.getLanIp(), wanIp, ver, 500);

        ret.get().setGwno(result.getGwno()).setPasscode(result.getPasscode());

        return ret.get();
    }


    public LoginRes login(GwLoginParam param) throws NoSuchAlgorithmException {

        var tokenCache = this.iotCacheService.getToken(param.getGwno());
        if (StringUtils.equalsIgnoreCase(param.getToken(), tokenCache)) {
            // 如果token一致, 不需要更新
            //loginResult.set(new LoginRes());
            this.loginService.updateGwInfo4LoginSuccess(param, null);   // 更新数据库中网关的信息
            return this.buildLoginRes(param.getGwno(), param.getToken(), param.getSup());
        }
        String realm = this.iotCacheService.getOrUpdateRealm(param.getGwno());
        log.info("网关登录请求. gwno = {}, gw_token = {}, wanIp = {}, realm = {}, redis_token = {}",
                param.getGwno(), param.getToken(), param.getWanIp(), realm, tokenCache);

        this.loginService.doLogin(realm, param);

        return this.buildLoginRes(param.getGwno(), param.getToken(), param.getSup());

    }

    private LoginRes buildLoginRes(String gwno, String token, Boolean isSupervisor) {
        LoginRes loginResult = new LoginRes();

        loginResult.setGwno(gwno);

        String clientId = MqttUtils.getMqttClientId(gwno, this.mqttGroup, isSupervisor);

        loginResult.setMqttClientId(clientId).setMqttUrl(this.mqttClientUrl)
                .setMqttTopic(MqttUtils.getMqTopic(gwno, this.env, mqttTopicPrefix, isSupervisor))
                .setMqttLwt(MqttUtils.getMqLastWillTopic(this.env, mqttTopicPrefix, mqttLastWillTopic, isSupervisor));
        if (IotConstants.MQTT_TYPE_MOSQUITTO.equalsIgnoreCase(this.mqttType)) {
            loginResult.setMqttUsername(this.mqttUsername).setMqttPassword(this.mqttPassword);
        } else {
            loginResult.setMqttUsername(MqttUtils.getMqttUsername(this.mqttId, this.mqttAccessKey))
                    .setMqttPassword(MqttUtils.getMqttPasscode(clientId, this.mqttAccessSecret));
        }

        // 更新最后登陆时间
        this.gwInfoService.updateLoginTime(gwno);
        // 更新网关登陆TOKEN缓存
        this.iotCacheService.updateToken(gwno, token);
        return loginResult;
    }


    public Mono<BaseResponse> processCmdResult(IotGwCmdResultReqV2 req, final String gwno) {
        this.redisIotUpdateWrapper.deleteIotGwCmd(gwno, req.getSeq());

        ObjectMapper objectMapper = new ObjectMapper();
        if (IotGwCmdType2.CE_CHARGE_START == req.getCmd()) {

            IotGwCmdResultStartCharge iotGwCmdResultStartCharge = objectMapper.convertValue(req.getDetail(), IotGwCmdResultStartCharge.class);

            String evseNo = iotGwCmdResultStartCharge.getEvseNo();
            Integer plugId = iotGwCmdResultStartCharge.getPlugId();
            String orderNo = iotGwCmdResultStartCharge.getOrderNo();

            IotAssert.isTrue(StringUtils.isNotBlank(gwno), "请求参数Gwno为空");
            IotAssert.isTrue(StringUtils.isNotBlank(orderNo), "请求参数EvseId为空");
            IotAssert.isTrue(plugId != null && plugId >= 0, "请求参数PlugId为空");

            iotGwCmdResultStartCharge.setPlugNo(PlugNoUtils.formatPlugNo(evseNo, plugId));
            if (StringUtils.isBlank(orderNo)) {  // 如果网关没有上报订单号, 从枪头缓存中取
                PlugVo plug = redisIotReadService.getPlugRedisCache(evseNo, plugId);
                orderNo = plug == null ? null : plug.getOrderNo();
//                    orderReport.setOrderNo(orderNo);
                iotGwCmdResultStartCharge.setOrderNo(orderNo);
            }
            IotAssert.isTrue(StringUtils.isNotBlank(orderNo), "请求参数OrderNo为空");
            //BaseResponse entity = this.dcBChargerOrderClient.orderStarting(iotGwCmdResultStartCharge);    // 无视返回的结果
            if (req.getStatus() != 0) {
                log.warn("启动充电失败. evseNo = {}, orderNo = {}", evseNo, orderNo);
                // 清除枪头缓存的订单号
                redisIotUpdateWrapper.unbindOrderNo(evseNo, plugId);
            }
            return this.bizTradingReactiveFeignClient.orderStarting(iotGwCmdResultStartCharge).doOnNext(entity -> {
                log.info("云端开始充电返回结果: {}", entity);
            });

        } else if (IotGwCmdType2.CE_CHARGE_STOP == req.getCmd()) {
            // do nothing
        } else if (IotGwCmdType2.CE_GET_CFG == req.getCmd()) {

            if (req.getStatus() != 0) {
                String cfgEvseNo = String.valueOf(req.getDetail());
                CfgEvseAllV2 cfgEvseAllV2 = new CfgEvseAllV2();
                cfgEvseAllV2.setEvseNo(cfgEvseNo);
                evseCfgRedisService.appendCfgEvseAllV2(cfgEvseNo, cfgEvseAllV2);
            }
        } else if (IotGwCmdType2.CE_MODIFY_CFG == req.getCmd()) {

            if (req.getStatus() != 0) {
                //TODO 暂不调整结果上报 网关1.20文档
                //req.getDetail();
            }
        } else if (IotGwCmdType2.MGC_TUNNEL_START == req.getCmd()) {
            // do nothing
        } else if (IotGwCmdType2.MGC_TUNNEL_STOP == req.getCmd()) {
            // do nothing
        } else if (IotGwCmdType2.CE_UPGRADE == req.getCmd()) {
            IotGwCmdResultUpgrade iotGwCmdResultUpgrade = objectMapper.convertValue(req.getDetail(), IotGwCmdResultUpgrade.class);

            UpgradeEvseResultRequest upgradeEvseResultRequest = new UpgradeEvseResultRequest();
            upgradeEvseResultRequest.setEvseId(iotGwCmdResultUpgrade.getEvseNo());
            upgradeEvseResultRequest.setResult(iotGwCmdResultUpgrade.getResult());
            upgradeEvseResultRequest.setTaskNo(iotGwCmdResultUpgrade.getTaskNo());
            upgradeEvseResultRequest.setMsg(iotGwCmdResultUpgrade.getMsg());
            return deviceMgMClient.upgradeEvseResult(upgradeEvseResultRequest).doOnNext(res -> {
                log.info("更新完成. req = {}, res = {}", upgradeEvseResultRequest, res);
            });
        } else if (IotGwCmdType2.CE_REBOOT == req.getCmd()) {
            // do nothing
        } else if (IotGwCmdType2.CE_DEBUG == req.getCmd()) {
            // do nothing
        } else if (IotGwCmdType2.CE_CHARGE_SOC_CTRL == req.getCmd()) {
            IotGwCmdResultLimitSoc iotGwCmdResultLimitSoc = objectMapper.convertValue(req.getDetail(), IotGwCmdResultLimitSoc.class);

            return this.bizTradingReactiveFeignClient.limitSocFeedback(iotGwCmdResultLimitSoc).doOnNext(entity -> log.info("云端返回结果: {}", entity));
        }
        // TODO: 要处理不同指令的失败场景 ( result != 0 )
        return Mono.just(RestUtils.success());
    }

    public EvsePasscodePo getEvsePasscode(EvsePasscodeReqV2 req) {
        if (EvseProtocolType.OCPP.equals(req.getProtocol())) {
            var po = this.evsePasscodeRoDs.getEvsePasscode(req.getEvseNo());
            if (po == null) {
                // 不校验密钥
                return new EvsePasscodePo().setEvseNo(req.getEvseNo());
            }
            return po;
        }
        return this.evsePasscodeRoDs.getEvsePasscode(req.getEvseNo(), req.getPasscodeVer());
    }

    /**
     * 获取桩价格模板
     */
    public Mono<ObjectResponse<ChargePriceVo>> getPriceSchema(String evseNoIn) {
        return Mono.just(evseNoIn).map(evseNo -> {
            Long priceCode = null;
            EvseVo evse = this.redisIotRwService.getEvseRedisCache(evseNo);
            if (evse == null) {
                EvsePo evsePo = this.evseService.getEvsePo(evseNo, false);
                if (evsePo != null) {
                    priceCode = evsePo.getPriceCode();
                }
            } else {
                priceCode = evse.getPriceCode();
            }
            return Optional.ofNullable(priceCode);
        }).flatMap(priceCode -> {
            if (priceCode.isEmpty()) {
                return Mono.just(new ObjectResponse<ChargePriceVo>());
            } else {
                return this.bizDataCoreFeignClient.getChargePrice(priceCode.get());
            }
        });

    }

    /**
     * 获取桩信息
     */
    public Mono<ObjectResponse<EvseOpInfoVo>> getEvseOpInfo(String evseNoIn) {
        EvseOpInfoVo vo = this.evseModelRoDs.getEvseOpInfoByEvseId(evseNoIn);
        return Mono.just(RestUtils.buildObjectResponse(vo));
    }

    public Mono<GwObjResMsg> cfgEvseResultV2(GwObjReqMsg<CfgEvseResultReqV2> req, String gwno) {

        return businessService.evseCfgResult(req.getData(), gwno).map(res1 -> {
                GwObjResMsg res = new GwObjResMsg(null);
                res.setSeq(req.getSeq());

                return res;
            })
            .doOnNext(x -> log.info("<< cfgEvseResultV2"));
    }

    public Mono<EvseCfgResponse> upgradeResult(UpgradeReq req) {

        return businessService.upgradeResult(req).doOnNext(res1 -> {
            if (UpgradeEvseResultEnum.SUCCESS.equals(req.getResult())) {
                // 升级成功，修改桩状态为离线
                EvsePo evsePo = evseService.getEvsePo(req.getEvseId(), true);
                evsePo.setEvseStatus(EvseStatus.OFF);
                evseService.update(evsePo);

                UpdateEvseCacheDto dto = new UpdateEvseCacheDto();
                dto.setEvent(IotEvent.STATE_CHANGE).setSite(null).setEvse(evsePo).setLinkId(null);
                redisIotUpdateWrapper.updateRedisEvseCache(dto);

                updatePlugsToOffline(evsePo);
            }

        }).map(res2 -> {

            EvseCfgResponse res = new EvseCfgResponse();
            res.setSeq(req.getSeq());

            return res;
        });


    }

    private void updatePlugsToOffline(EvsePo evsePo) {
        List<PlugPo> plugPos = plugService.listPlug(evsePo.getEvseId(), null, null, null, false);
        plugPos.forEach(e -> {
            PlugVo plugVo = redisIotReadService.getPlugRedisCache(evsePo.getEvseId(), e.getPlugId());
            plugVo.setStatus(PlugStatus.OFF);
            e.setPlugStatus(PlugStatus.OFF);
            plugService.update(e);
            redisIotRwService.updatePlugRedisCache(plugVo);
            PlugMqDto plugMqDto = new PlugMqDto();
            if (plugVo != null) {
                BeanUtils.copyProperties(plugVo, plugMqDto);
            }
            plugMqDto.setFirmwareVer(evsePo.getFirmwareVer());
            plugMqDto.setModelName(evsePo.getModelName());
            dcEventPublish.publishPlugInfo(IotEvent.STATE_CHANGE, plugMqDto);// 推送枪头状态变动的消息
        });
    }


    public GwObjResMsg<CfgEvseV2> getCfgEvse(String gwno, GwObjReqMsg<CfgEvseReqV2> req) {

        String redisId = RedisIdGenerator.getCfgId(gwno, req.getData().getEvseNo());
        CfgEvseV2 cfgEvse = evseCfgRedisService.getV2(redisId, req.getData().getCfgVer());
        log.info("云端Redis桩配置信息。redisId: {}, cfgEvse = {}", redisId, cfgEvse);

        if (cfgEvse == null) {
            throw new IllegalArgumentException("未获取到桩配置, 桩号 " + req.getData().getEvseNo());
        }

        GwObjResMsg<CfgEvseV2> ret = new GwObjResMsg<>(cfgEvse);
        BeanUtils.copyProperties(cfgEvse, ret);
        ret.setSeq(req.getSeq());
        log.info("云端获取桩配置信息。ret = {}", ret);
        return ret;
    }


    public Mono<ListResponse<TransformerPo>> getTransformerList(Long transformerId) {
        List<TransformerPo> tfmList = this.transformerRoDs.getTransformerList4SiteDynamicPower(transformerId);
        if (CollectionUtils.isNotEmpty(tfmList)) {
            return Mono.just(new ListResponse<>(tfmList));
        }
        List<TransformerPo> orderlyTfmList = this.transformerRoDs.getTransformerList4SiteOrderly(transformerId);
        if (CollectionUtils.isEmpty(orderlyTfmList)) {
            return Mono.just(new ListResponse<>(orderlyTfmList));
        }
        Map<Long,TransformerPo> transformerPoMap = orderlyTfmList.stream().collect(Collectors.toMap(TransformerPo::getId, e -> e));
        return openHlhtRestFeignClient.queryOrderlyPowerList(orderlyTfmList.stream().map(TransformerPo::getId).collect(Collectors.toList()))
            .map( e -> {
                List<TransformerPo> orderlyTfmList2 = new ArrayList<>();
                e.getData().forEach( f -> {
                    if (f.getSecond().compareTo(BigDecimal.ZERO) >= 0 ) {
                        TransformerPo transformerPo = transformerPoMap.get(f.getFirst());
                        transformerPo.setAssignableCap(f.getSecond());
                        orderlyTfmList2.add(transformerPo);
                    }
                });
                return new ListResponse<>(orderlyTfmList2);
            });
    }

    public Mono<ListResponse<EvseVo>> getEvseList4Transformer(ListEvseParam param) {
        List<EvsePo> list = this.evseService.getEvseList4Transformer(param);
        List<EvseVo> voList = list.stream().map(ev -> {
            EvseVo vo = new EvseVo();
            vo.setEvseNo(ev.getEvseId()).setPower(ev.getPower());
            return vo;
        }).collect(Collectors.toList());
        return Mono.just(new ListResponse<>(voList));
    }

    public Mono<ListResponse<PlugVo>> getPlugList(ListPlugParam param) {
        com.cdz360.base.model.base.dto.ListResponse<PlugVo> plugList = this.plugService.getPlugList(param);
        return Mono.just(new ListResponse<>(plugList.getData()));
    }

}
