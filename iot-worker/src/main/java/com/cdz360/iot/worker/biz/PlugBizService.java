package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.ds.ro.EvseRoDs;
import com.cdz360.iot.ds.ro.PlugRoDs;
import com.cdz360.iot.ds.ro.SiteRoDs;
import com.cdz360.iot.ds.rw.PlugRwDs;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.model.site.po.SitePo;
import com.cdz360.iot.worker.model.iot.UpdatePlugCacheDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PlugBizService {


    @Autowired
    private PlugRwDs plugRwDs;

    @Autowired
    private PlugRoDs plugRoDs;

    @Autowired
    private EvseRoDs evseRoDs;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private RedisIotUpdateWrapper redisIotUpdateWrapper;

    public void modifyPlugName(String evseNo, int plugIdx, String plugName) {
        PlugPo plug = this.plugRoDs.getPlug(evseNo, plugIdx);
        if (plug == null) {
            log.error("无法找到枪头信息. evseNo = {}, idx = {}", evseNo, plugIdx);
            throw new DcArgumentException("枪头不存在");
        }
        if (StringUtils.equals(plugName, plug.getName())) {
            log.info("<<");
            return;
        }
        PlugPo update = new PlugPo();
        update.setEvseId(evseNo).setPlugId(plugIdx).setName(plugName);
        this.plugRwDs.updatePlug(update);
        EvsePo evse = this.evseRoDs.getEvse(evseNo);
        SitePo site = this.siteRoDs.getSite(evse.getSiteId());

        plug.setName(plugName); // 变更名称
        UpdatePlugCacheDto dto = new UpdatePlugCacheDto();
        dto.setEvent(IotEvent.BIND)
                .setPlugReport(null)
                .setSite(site)
                .setEvse(evse)
                .setPlug(plug);
        redisIotUpdateWrapper.updateRedisPlugCache(dto);

    }
}
