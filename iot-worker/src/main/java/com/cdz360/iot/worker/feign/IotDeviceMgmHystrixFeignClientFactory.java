package com.cdz360.iot.worker.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.evse.upgrade.UpgradeEvseResultRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;

@Slf4j
@Component
public class IotDeviceMgmHystrixFeignClientFactory implements FallbackFactory<IotDeviceMgmFeignClient> {

    @Override
    public IotDeviceMgmFeignClient apply(Throwable throwable) {
        //log.error("【服务熔断】。Service = {}, message = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, throwable.getMessage());
        //log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, throwable);
        return new IotDeviceMgmFeignClient() {


            @Override
            public Mono<BaseResponse> upgradeEvseResult(UpgradeEvseResultRequest req) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM);
                return Mono.just(RestUtils.serverBusy());
            }
        };
    }

    @Override
    public <V> Function<V, IotDeviceMgmFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super IotDeviceMgmFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM);
        return null;
    }
}
