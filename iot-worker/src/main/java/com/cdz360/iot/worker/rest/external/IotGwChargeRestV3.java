package com.cdz360.iot.worker.rest.external;


import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.LogHelper;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.IotCacheService;
import com.cdz360.iot.biz.rest.IotRestBase;
import com.cdz360.iot.biz.utils.GwRestUtils;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.model.auth.CusAuthReqEx;
import com.cdz360.iot.model.auth.CusAuthReqV2;
import com.cdz360.iot.model.auth.CusAuthResBaseV2;
import com.cdz360.iot.model.base.BaseGwResponse;
import com.cdz360.iot.model.base.CommonResponse;
import com.cdz360.iot.model.base.EvseCfgResponse;
import com.cdz360.iot.model.base.ListResponse;
import com.cdz360.iot.model.base.OrderCreateResponseV2;
import com.cdz360.iot.model.evse.ChgEvseRequestV2;
import com.cdz360.iot.model.evse.DebugRequest;
import com.cdz360.iot.model.evse.EvseReportRequestV2;
import com.cdz360.iot.model.evse.ListEvseParam;
import com.cdz360.iot.model.evse.ListPlugParam;
import com.cdz360.iot.model.evse.OfflineCardLogReq;
import com.cdz360.iot.model.evse.OrderFeeRefreshRequestV2;
import com.cdz360.iot.model.evse.OrderFeeRefreshResponseV2;
import com.cdz360.iot.model.evse.OrderStartRequestV2;
import com.cdz360.iot.model.evse.OrderStartingRequest;
import com.cdz360.iot.model.evse.OrderStopRequestV3;
import com.cdz360.iot.model.evse.OrderUpdateRequestV2;
import com.cdz360.iot.model.evse.cfg.CfgEvseAllV2;
import com.cdz360.iot.model.evse.cfg.CfgEvseV2;
import com.cdz360.iot.model.evse.param.ReportModuleParam;
import com.cdz360.iot.model.evse.po.EvsePasscodePo;
import com.cdz360.iot.model.gw.GwAuthReqMsg;
import com.cdz360.iot.model.gw.GwObjReqMsg;
import com.cdz360.iot.model.gw.GwObjResMsg;
import com.cdz360.iot.model.gw.GwResMsg;
import com.cdz360.iot.model.gw.vo.EvseOpInfoVo;
import com.cdz360.iot.model.transformer.po.TransformerPo;
import com.cdz360.iot.worker.biz.BusinessService;
import com.cdz360.iot.worker.biz.EvseProcessor;
import com.cdz360.iot.worker.biz.IotService;
import com.cdz360.iot.worker.biz.south.IotSouthBizService;
import com.cdz360.iot.worker.biz.south.OrderSouthBizService;
import com.cdz360.iot.worker.model.gw.CfgEvseReqV2;
import com.cdz360.iot.worker.model.gw.CfgEvseResultReqV2;
import com.cdz360.iot.worker.model.gw.EvseInfoReq;
import com.cdz360.iot.worker.model.gw.EvsePasscodeReqV2;
import com.cdz360.iot.worker.model.gw.EvsePriceReq;
import com.cdz360.iot.worker.model.gw.EvseResiterReqV2;
import com.cdz360.iot.worker.model.gw.IotGwCmdResultReqV2;
import com.cdz360.iot.worker.model.gw.MgcUpgradeResultReq;
import com.cdz360.iot.worker.model.gw.UpgradeReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "iot网关接入API (v3)", description = "南向-网关接口")
@RequestMapping("/iot/")
public class IotGwChargeRestV3 extends IotRestBase {

    @Autowired
    private IotService iotService;

    @Autowired
    private IotCacheService iotCacheService;

    @Autowired
    private IotSouthBizService iotService2;

    @Autowired
    private OrderSouthBizService orderService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private EvseProcessor evseProcessor;


    @Override
    protected IotCacheService getCacheService() {
        return this.iotCacheService;
    }


    @Operation(summary = "升级状态上报")
    @PostMapping(value = "/gw/upgrade/status", params = {"v=3"})
    public Mono<BaseResponse> mgcUpgradeStatus(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "v") int ver,
        @RequestParam(value = "n") String gwno,
        @RequestBody MgcUpgradeResultReq req) {
        log.info("微网控制器升级状态上报: req = {}", JsonUtils.toJsonString(req));
        return this.iotService2.mgcUpgradeStatus(req, gwno)
            .map(r -> RestUtils.success());
    }


    @Operation(summary = "桩注册")
    @PostMapping(value = {"/evse/register", "/ce/evse/register"}, params = {"v=3"})
    public BaseResponse evseRegister(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "v") int ver,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<EvseResiterReqV2> req) {
        log.info(">> evseRegister. authHd = {}, gwno = {},req = {}", authHd, gwno,
            JsonUtils.toJsonString(req));
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        iotService2.evseRegister(req.getData(), gwno);
        LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
            "evseRegister", "桩注册", startTime);
        //debugPerformance("桩注册", startTime);
        return GwRestUtils.buildResponse(req.getSeq());
    }

    @Operation(summary = "获取桩密钥")
    @PostMapping(value = {"/evse/passcode", "/ce/evse/passcode"}, params = {"v=3"})
    public GwObjResMsg<EvsePasscodePo> getEvsePasscode(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "v") int ver,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<EvsePasscodeReqV2> req) {
        log.info(">> getEvsePasscode = {}, gwno = {},req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        req.setGwno(gwno);
        EvsePasscodePo evsePasscodePo = this.iotService.getEvsePasscode(req.getData());
        LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
            "getEvsePasscode", "获取桩密钥", startTime);
        //debugPerformance("获取桩密钥", startTime);
        return GwRestUtils.buildObjResponse(req.getSeq(), evsePasscodePo);
    }

    @Operation(summary = "获取桩价格配置")
    @PostMapping(value = {"/evse/price", "/ce/evse/price"}, params = {"v=3"})
    public Mono<GwObjResMsg<ChargePriceVo>> getEvsePrice(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "v") int ver,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<EvsePriceReq> req) {
        log.info(">> 获取桩价格配置 = {}, gwno = {},req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        req.setGwno(gwno);
        return this.iotService.getPriceSchema(req.getData().getEvseNo())
            .doOnNext(res1 -> {
                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "getEvsePrice", "获取桩价格配置", startTime);
            })
            .map(res2 -> GwRestUtils.buildObjResponse(req.getSeq(), res2.getData()));

    }

    @Operation(summary = "下行指令执行反馈")
    @PostMapping(value = {"/cmd/result", "/ce/cmd/result"}, params = {"v=3"})
    public Mono<GwResMsg> cmdResult(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<IotGwCmdResultReqV2> req) {

        log.info(">> cmdResult. authHd = {}, gwno = {},req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题

//        checkToken(authHd, gwno);
        req.setGwno(gwno);
        return this.iotService.processCmdResult(req.getData(), gwno)
            .doOnNext(res1 -> {

                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "cmdResult", "下行指令执行反馈", startTime);
            })
            .map(res2 -> GwRestUtils.buildResponse(req.getSeq()));
        //debugPerformance("下行指令执行反馈", startTime);
        //return ;
    }


    @Operation(summary = "获取桩配置")
    @PostMapping(value = {"/cfg/evse", "/ce/cfg/evse"}, params = {"v=3"})
    public GwObjResMsg<CfgEvseV2> cfgEvse(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<CfgEvseReqV2> req) throws IOException {

        log.info("获取桩配置。>> authHd = {}, gwno = {},req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);

        GwObjResMsg<CfgEvseV2> ret = this.iotService.getCfgEvse(gwno, req);

        LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
            "cfgEvse", "获取桩配置", startTime);
        //debugPerformance("获取桩配置", startTime);
        return ret;
    }

    @Operation(summary = "桩配置更新结果上报")
    @PostMapping(value = {"/cfg/evse/result", "/ce/cfg/evse/result"}, params = {"v=3"})
    public Mono<GwObjResMsg> cfgEvseResult(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<CfgEvseResultReqV2> req) throws IOException {
        log.info("桩配置更新结果上报。 authHd = {}, gwno = {},req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        return this.iotService.cfgEvseResultV2(req, gwno)
            .doOnNext(res1 -> {
                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "cfgEvseResult", "桩配置更新结果上报", startTime);
            });
        //debugPerformance("桩配置更新结果上报", startTime);
        //return ret;
    }


    @Operation(summary = "充电桩/枪的状态上报")
    @PostMapping(value = {"/evse/status", "/ce/evse/status"}, params = {"v=3"})
    public Mono<BaseGwResponse> evseStatus(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<EvseReportRequestV2> req
    ) throws IOException {
        log.info("充电桩/枪的状态上报。authHd = {}, gwno = {},req = {}", authHd, gwno,
            JsonUtils.toJsonString(req));
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);
        //Mono<BaseGwResponse> ret =
        req.setGwno(gwno);
        //iotService.processEvseStatusReport(gwno, req);
        return evseProcessor.processEvseNormalStatusV2(req.getData(), gwno)
            .map(res -> {
                log.info("处理完成");
                BaseGwResponse ret = new BaseGwResponse();
                ret.setSeq(req.getSeq());
                return ret;
            })
            .doOnNext(res -> {
                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "evseStatus", "充电桩/枪的状态上报", startTime);
            });
    }


    @Operation(summary = "鉴权")
    @PostMapping(value = {"/cus/auth", "/ce/cus/auth"}, params = {"v=3"})
    public Mono<GwAuthReqMsg<CusAuthResBaseV2>> auth(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<CusAuthReqV2> req) throws IOException {   // TODO: 参数类型要定义

        log.info("鉴权。authHd = {}, gwno = {},req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);

        CusAuthReqEx cusAuthReqEx = new CusAuthReqEx();
        //BeanUtils.copyProperties(req, cusAuthReqEx);
        cusAuthReqEx.setGwno(gwno);
        cusAuthReqEx.setSeq(req.getSeq());
        cusAuthReqEx.setAccountNo(req.getData().getAccountNo());
        cusAuthReqEx.setAuthType(req.getData().getAuthType());
        cusAuthReqEx.setEvseId(req.getData().getEvseNo());
        cusAuthReqEx.setPlugId(req.getData().getPlugId());
        cusAuthReqEx.setSave2Redis(true);   // 兼容旧版行为
//        CusAuthRes result =
        return orderService.auth(cusAuthReqEx)
            .map(res1 -> {
                //丢弃不必要的字段
                CusAuthResBaseV2 cusAuthResBase = new CusAuthResBaseV2();
                cusAuthResBase.setAmount(res1.getBalance())
                    .setPower(res1.getPower())
                    .setCarNo(res1.getCarNo());
                GwAuthReqMsg<CusAuthResBaseV2> res = new GwAuthReqMsg<>(cusAuthResBase);
                res.setSeq(req.getSeq());
                return res;
            })
            .doOnNext(res2 -> {
                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "auth", "鉴权", startTime);

            });
        //debugPerformance("鉴权", startTime);
        //log.info("<<");
        //return res;
    }

    //@Operation(summary = "记录刷卡日志")
    @PostMapping(value = {"/order/cardLog", "/ce/order/cardLog"}, params = {"v=3"})
    public Mono<BaseGwResponse> addCardLog(
        ServerHttpResponse response,
        //@Parameter(name = "鉴权")
        @RequestHeader(value = "Authorization", required = false) String authHd,
        //@Parameter(name = "网关编号")
        @RequestParam(value = "n") String gwno,
        //@Parameter(name = "订单开启请求")
        @RequestBody GwObjReqMsg<OfflineCardLogReq> chgEvseReq
    ) throws IOException {
        long startTime = System.nanoTime();    // debug 性能问题
        return this.orderService.addCardLog(chgEvseReq.getData(), gwno)
            .doOnNext(res2 -> {
                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "addCardLog", "记录刷卡日志", startTime);
            });
    }

    //@Operation(summary = "桩端发起充电请求")
    @PostMapping(value = {"/order/create", "/ce/order/create"}, params = {"v=3"})
    public Mono<CommonResponse<OrderCreateResponseV2>> orderCreate(
        ServerHttpResponse response,
        //@Parameter(name = "鉴权")
        @RequestHeader(value = "Authorization", required = false) String authHd,
        //@Parameter(name = "网关编号")
        @RequestParam(value = "n") String gwno,
        //@Parameter(name = "订单开启请求")
        @RequestBody GwObjReqMsg<ChgEvseRequestV2> chgEvseReq
    ) throws IOException {

        log.info("充电桩/桩端发起充电请求。authHd = {}, gwno = {}, req = {}", authHd, gwno,
            chgEvseReq);
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        if (StringUtils.isBlank(chgEvseReq.getGwno())) {
            chgEvseReq.setGwno(gwno);
        }
        return this.orderService.createOrder(chgEvseReq.getData(), gwno)
            .map(ret -> {
                CommonResponse<OrderCreateResponseV2> res = new CommonResponse<>(ret);
                res.setSeq(chgEvseReq.getSeq());
                response.getHeaders()
                    .put(IotConstants.HTTP_HEADER_SEQ, List.of(chgEvseReq.getSeq()));
                log.info("<< ret = {}", ret);
                //debugPerformance("桩端发起充电请求", startTime);
                return res;
            })
            .doOnNext(res2 -> {
                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "orderCreate", "桩端发起充电请求", startTime);
            });

    }

    @Operation(summary = "充电指令下发结果上报")
    @PostMapping(value = {"/order/starting", "/ce/order/starting"}, params = {"v=3"})
    public Mono<BaseGwResponse> orderStarting(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody OrderStartingRequest orderStartReq
    ) throws IOException {
        log.info("充电指令下发结果上报。authHd = {}, gwno = {}, req = {}", authHd, gwno,
            orderStartReq);
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);
        if (StringUtils.isBlank(orderStartReq.getGwno())) {
            orderStartReq.setGwno(gwno);
        }
        // IOT订单表数据插入
        orderStartReq.setEvseNo(orderStartReq.getEvseNo());
        //BaseGwResponse ret =
        return this.orderService.orderStarting(orderStartReq)
            .doOnNext(res1 -> {

                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "orderStarting", "充电指令下发结果上报", startTime);
            });
        //log.info("<<");
        //debugPerformance("充电指令下发结果上报", startTime);
        //return ret;
    }

    @Operation(summary = "充电开始状态上报")
    @PostMapping(value = {"/order/start", "/ce/order/start"}, params = {"v=3"})
    public Mono<BaseGwResponse> orderStart(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<OrderStartRequestV2> orderStartReq
    ) throws IOException {
        log.info("充电桩/充电开始状态上报。authHd = {}, gwno = {}, req = {}", authHd, gwno,
            orderStartReq);
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);
        if (StringUtils.isBlank(orderStartReq.getGwno())) {
            orderStartReq.setGwno(gwno);
        }
        IotAssert.isNotNull(orderStartReq.getGwno(), "JSON 缺少gwno");
        IotAssert.isNotNull(orderStartReq.getSeq(), "JSON 缺少seq");

        IotAssert.isTrue(orderStartReq.getGwno() != null && orderStartReq.getGwno() != "",
            "请求参数Gwno为空");
        IotAssert.isTrue(StringUtils.isNotBlank(orderStartReq.getData().getEvseNo()),
            "请求参数EvseNo为空");
        IotAssert.isTrue(
            orderStartReq.getData().getPlugId() != null && orderStartReq.getData().getPlugId() >= 0,
            "请求参数PlugId为空");
        IotAssert.isTrue(StringUtils.isNotBlank(orderStartReq.getData().getOrderNo()),
            "请求参数OrderNo为空");

        // IOT订单表数据插入
//        BaseGwResponse ret =
        return this.orderService.orderStarted(orderStartReq)
            .doOnNext(res1 -> {

                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "orderStart", "充电开始状态上报", startTime);
            });
//        log.info("<<");
//        //debugPerformance("充电开始状态上报", startTime);
//        return ret;
    }


    @Operation(summary = "充电中状态更新")
    @PostMapping(value = {"/order/update", "/ce/order/update"}, params = {"v=3"})
    public Mono<BaseGwResponse> orderUpdate(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<OrderUpdateRequestV2> orderUpdateReq
    ) throws IOException {

        log.info("充电中状态更新。authHd = {}, gwno = {}, req = {}", authHd, gwno,
            JsonUtils.toJsonString(orderUpdateReq));
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);
        if (StringUtils.isBlank(orderUpdateReq.getGwno())) {
            orderUpdateReq.setGwno(gwno);
        }
        // Mono<BaseGwResponse> ret = Mono.just(new BaseGwResponse(this.getSeq(jsonBody)));
        //BaseGwResponse ret =
        return this.orderService.orderUpdate(orderUpdateReq.getData())
            .doOnNext(res1 -> {
                res1.setSeq(orderUpdateReq.getSeq());
                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "orderUpdate", "充电中状态更新", startTime);
            });

//        log.info("<<");
//        //debugPerformance("充电中状态更新", startTime);
//        return ret;
    }

    @Operation(summary = "充电完成状态上报")
    @PostMapping(value = {"/order/stop", "/ce/order/stop"}, params = {"v=3"})
    public Mono<BaseGwResponse> orderStop(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<OrderStopRequestV3> orderStopReqV3
    ) throws IOException {

        log.info("充电完成状态上报。authHd = {}, gwno = {}, req = {}", authHd, gwno,
            JsonUtils.toJsonString(orderStopReqV3));
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);
        if (StringUtils.isBlank(orderStopReqV3.getGwno())) {
            orderStopReqV3.setGwno(gwno);
        }
//        BaseGwResponse ret =
        return this.orderService.orderStop(orderStopReqV3)
            .doOnNext(res1 -> {

                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "orderStop", "充电完成状态上报", startTime);
            });
        //log.info("<<");
        //debugPerformance("充电完成状态上报", startTime);
        //return ret;
    }

    @Operation(summary = "充电中订单续费")
    @PostMapping(value = {"/order/fee/refresh", "/ce/order/fee/refresh"}, params = {"v=3"})
    public Mono<GwObjResMsg<OrderFeeRefreshResponseV2>> orderFeeRefresh(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<OrderFeeRefreshRequestV2> req
    ) throws IOException {
        log.info(">> 桩端请求订单续费。authHd = {}, gwno = {}, req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        if (StringUtils.isBlank(req.getGwno())) {
            req.setGwno(gwno);
        }

        // 参数校验
        if (StringUtils.isBlank(req.getData().getOrderNo()) ||
            StringUtils.isBlank(req.getData().getEvseNo()) ||
            null == req.getData().getPlugId()) {
            log.info("<< 桩端请求订单续费参数异常, 订单编号/桩编号/枪编号不存在: req={}", req);
            throw new DcArgumentException("桩端请求订单续费, 订单编号/桩编号/枪编号参数异常");
        }

        // 需要对接具体业务服务

        // GwObjResMsg<OrderFeeRefreshResponseV2> res =
        return this.orderService.refreshFee(req)
            .doOnNext(res -> {
                log.info("<< 桩端请求订单续费结果: res={}", res);
                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "orderFeeRefresh", "充电中订单续费", startTime);
            });

    }

    @Operation(summary = "上传桩端DEBUG数据")
    @PostMapping(value = {"/evse/debug", "/ce/evse/debug"}, params = {"v=3"})
    public BaseGwResponse evseDebug(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<DebugRequest> req
    ) throws IOException {
        log.info(">> 上传桩端DEBUG数据。authHd = {}, gwno = {}, req = {}", authHd, gwno, req);
        //TODO raf V2
        return BaseGwResponse.newInstance();
    }


    @Operation(summary = "上传桩配置信息")
    @PostMapping(value = {"/cfg/evse/info", "/ce/cfg/evse/info"}, params = {"v=3"})
    public BaseGwResponse cfgEvseInfo(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @Parameter(name = "配置内容版本号.") @RequestBody GwObjReqMsg<List<CfgEvseAllV2>> cfgEvseInfoRequest) {
        long startTime = System.nanoTime();    // debug 性能问题
        log.info("上传桩配置信息. gwno = {}, msg = {}", gwno, cfgEvseInfoRequest);
        String seq = cfgEvseInfoRequest.getSeq();
        List<CfgEvseAllV2> cfgList = cfgEvseInfoRequest.getData();
        log.info(">> 桩上报配置 evse cfg info: {}, {}", seq, cfgList);
        checkToken(authHd, gwno);
        var res = new BaseGwResponse(seq);
        if (cfgList.isEmpty()) {
            return res;
        }
        businessService.evseCfgInfoV2(cfgList);
        //iotWorkerFeignClient.cfgEvseInfo(seq, cfgList);
        log.info("<< 桩上报配置 ok: {}", res);
        LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
            "cfgEvseInfo", "上传桩配置信息", startTime);
        //debugPerformance("上传桩配置信息", startTime);
        return res;
    }

    @Operation(summary = "获取桩运转信息")
    @PostMapping(value = {"/evse/getOpInfo", "/ce/evse/getOpInfo"}, params = {"v=3"})
    public Mono<GwObjResMsg<EvseOpInfoVo>> getEvseOpInfo(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "v") int ver,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<EvseInfoReq> req) {
        log.info(">> 获取桩运转信息. authHd: {}, gwno: {}, req: {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        req.setGwno(gwno);
        return this.iotService.getEvseOpInfo(req.getData().getEvseNo())
            .doOnNext(res1 -> {
                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "getEvseOpInfo", "获取桩运转信息", startTime);
            })
            .map(res2 -> GwRestUtils.buildObjResponse(req.getSeq(), res2.getData()));
    }

    @Operation(summary = "桩上报固件升级结果")
    @PostMapping(value = {"/upgrade/evse/result", "/ce/upgrade/evse/result"}, params = {"v=3"})
//TODO:这个路由地址需要确认是否合理
    public Mono<EvseCfgResponse> upgradeResult(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody UpgradeReq req) throws IOException {
        log.info("桩固件升级结果上报。 authHd = {}, gwno = {}, req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        return this.iotService.upgradeResult(req)
            .doOnNext(res1 -> {
                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "upgradeResult", "桩上报固件升级结果", startTime);
            });
    }


    @Operation(summary = "获取变电设施列表(场站功率分配使用)")
    @PostMapping(value = {"/site/getTransformerList", "/ce/site/getTransformerList"}, params = {
        "v=3"})
    public Mono<ListResponse<TransformerPo>> getTransformerList(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<Long> req) throws IOException {
        log.info("获取变电设施列表。 authHd = {}, gwno = {}, req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题
//        checkToken(authHd, gwno);
        return this.iotService.getTransformerList(req.getData())
            .doOnNext(res1 -> {
                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "getTransformerList", "获取变电设施列表", startTime);
            });
    }

    @Operation(summary = "获取桩列表(用于有序充电)")
    @PostMapping(value = {"/site/getEvseList", "/ce/site/getEvseList"}, params = {"v=3"})
    public Mono<ListResponse<EvseVo>> getEvseList(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<ListEvseParam> req) throws IOException {
        log.info("获取桩列表。 authHd = {}, gwno = {}, req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        return this.iotService.getEvseList4Transformer(req.getData())
            .doOnNext(res1 -> {
                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "getEvseList", "获取桩列表", startTime);
            });
    }

    @Operation(summary = "获取枪头列表(用于有序充电)")
    @PostMapping(value = {"/site/getPlugList", "/ce/site/getPlugList"}, params = {"v=3"})
    public Mono<ListResponse<PlugVo>> getPlugList(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<ListPlugParam> req) throws IOException {
        log.info("获取桩列表。 authHd = {}, gwno = {}, req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        return this.iotService.getPlugList(req.getData())
            .doOnNext(res1 -> {
                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "getEvseList", "获取桩列表", startTime);
            });
    }

    @Operation(summary = "桩上报器件信息")
    @PostMapping(value = {"/evse/reportModule", "/ce/evse/reportModule"}, params = {"v=3"})
    public Mono<BaseGwResponse> reportModule(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<ReportModuleParam> req) {
        log.info("桩上报器件信息。authHd = {}, gwno = {},req = {}", authHd, gwno,
            JsonUtils.toJsonString(req));
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);
        req.setGwno(gwno);
        return evseProcessor.reportModule(req.getData())
            .map(res -> {
                log.info("处理完成");
                BaseGwResponse ret = new BaseGwResponse();
                ret.setSeq(req.getSeq());
                return ret;
            })
            .doOnNext(res -> {
                LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
                    "reportModule", "桩上报器件信息", startTime);
            });
    }


}
