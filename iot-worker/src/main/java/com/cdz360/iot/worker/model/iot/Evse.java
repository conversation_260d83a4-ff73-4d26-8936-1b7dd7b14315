package com.cdz360.iot.worker.model.iot;

import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.po.PlugPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;

public class Evse extends EvsePo {

    private List<PlugPo> plugs;

    @Schema(description = "是否支持不拔枪重连")
    private Boolean constantCharge;

    public Evse() {

    }

//    public Evse(EvsePo evse){
//        super.setEvseId(evse.getEvseId()).setEvseStatus(evse.getEvseStatus())
//                .setSupply(evse.getSupply())
//                .setGwno(evse.getGwno()).setNet(evse.getNet())
//                .setPlugNum(evse.getPlugNum()).setId(evse.getId())
//        .setCreateTime(evse.getCreateTime()).setUpdateTime(evse.getUpdateTime());
//    }

    public List<PlugPo> getPlugs() {
        return plugs;
    }

    public Evse setPlugs(List<PlugPo> plugs) {
        this.plugs = plugs;
        return this;
    }

    public Boolean getConstantCharge() {
        return constantCharge;
    }

    public void setConstantCharge(Boolean constantCharge) {
        this.constantCharge = constantCharge;
    }
}
