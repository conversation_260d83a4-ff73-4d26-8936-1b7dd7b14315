package com.cdz360.iot.worker.model.gw;

import com.cdz360.iot.model.base.BaseObject;
import com.cdz360.iot.model.base.IotPackageType;
import com.cdz360.iot.model.evse.upgrade.UpgradeEvseResultEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
// @ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class UpgradeReq extends BaseObject {

    private IotPackageType type = IotPackageType.REQ;

    private String seq;

    private String method = "EVSE_UPGRADE";

    private String evseId;

    private String taskNo;

    private UpgradeEvseResultEnum result;


    @Override
    public String toString() {
        return this.toJsonString();
    }
}
