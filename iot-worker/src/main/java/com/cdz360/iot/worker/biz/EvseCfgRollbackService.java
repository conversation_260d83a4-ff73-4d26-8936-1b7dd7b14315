package com.cdz360.iot.worker.biz;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.iot.ds.rw.EvseCfgRwDs;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EvseCfgRollbackService {

    // 使用ConcurrentHashMap缓存回退请求
    private final Map<String, Long> rollbackQueue = new ConcurrentHashMap<>();

    @Autowired
    private EvseCfgRwDs evseCfgRwDs;

    // 定时任务，批量处理一次
    // 每隔5秒执行一次，间隔时间过长的话，期间若存在新的成功配置下发，结果就错乱了
    @Scheduled(fixedRate = 5000)
    public void batchProcessTimeout() {
        if (rollbackQueue.isEmpty()) {
            return;
        }
        log.info("开始处理回退请求");

        // 获取当前队列中的所有充电桩ID
        List<String> needRollbackEvseNo = new ArrayList<>(rollbackQueue.keySet());
        rollbackQueue.clear(); // 清空队列

        // 批量回退配置
        batchRollbackConfig(needRollbackEvseNo);
    }

    // 处理单个回退请求
    public void handleRollback(String evseNo) {
        // 将充电桩ID加入队列，记录当前时间
        rollbackQueue.put(evseNo, System.currentTimeMillis());
        log.info("加入队列成功. evseNo: {}", evseNo);
    }

    // 处理单个回退请求
    public void handleRollback(List<String> evseNoList) {
        if (CollectionUtils.isEmpty(evseNoList)) {
            log.info("List为空无需处理. evseNoList.size: {}", evseNoList.size());
            return;
        }
        // 将充电桩ID加入队列，记录当前时间
        evseNoList.forEach(evseNo -> {
            rollbackQueue.put(evseNo, System.currentTimeMillis());
        });
        log.info("加入队列成功. evseNoList.size: {}", evseNoList.size());
    }

    // 批量回退配置
    public void batchRollbackConfig(List<String> needRollbackEvseNo) {
        log.info("开始批量回退配置. needRollbackEvseNo.size: {}", needRollbackEvseNo.size());
        List<String> distinct = needRollbackEvseNo.stream().distinct().collect(Collectors.toList());
        evseCfgRwDs.resetEvseCfg(distinct);
    }

}
