package com.cdz360.iot.worker.model.gw;

import com.cdz360.iot.model.base.IotPackageType;
import com.cdz360.iot.model.evse.cfg.CfgEvse;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

public class CfgEvseRes extends CfgEvse {
    @Schema(description = "消息方法, 请求/响应")
    private IotPackageType type = IotPackageType.RES;

    @Schema(description = "对应请求消息的序列号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String seq;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String method = "EVSE_CFG";

    @Schema(description = "状态码, 0表示成功, 其他都为错误码")
    private int status = 0;

    public IotPackageType getType() {
        return type;
    }

    public CfgEvseRes setType(IotPackageType type) {
        this.type = type;
        return this;
    }

    public String getSeq() {
        return seq;
    }

    public CfgEvseRes setSeq(String seq) {
        this.seq = seq;
        return this;
    }

    public String getMethod() {
        return method;
    }

    public CfgEvseRes setMethod(String method) {
        this.method = method;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public CfgEvseRes setStatus(int status) {
        this.status = status;
        return this;
    }
}
