package com.cdz360.iot.worker.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.iot.model.evse.BsBoxPo;
import com.cdz360.iot.model.evse.cfg.ChargeV2;
import com.cdz360.iot.model.evse.param.ModifyEvseCfgParam;
import com.cdz360.iot.model.pv.dto.OssStsDto;
import com.cdz360.iot.worker.model.dongzheng.BsBoxSettingPo;
import com.cdz360.iot.worker.model.iot.dto.EvseCfgResendDto;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
        fallbackFactory = BizDataCoreHystrixFeignClientFactory.class)
public interface BizDataCoreFeignClient {

    // 获取文件上传的STS信息
    @GetMapping(value = "/dataCore/oss/getSts")
    Mono<ObjectResponse<OssStsDto>> getSts();

    // 获取文件上传的STS信息
    @GetMapping(value = "/dataCore/oss/getArchiveSts")
    Mono<ObjectResponse<OssStsDto>> getArchiveSts();

    /**
     * 批量跟新桩配置下发状态
     *
     * @param poList
     * @return
     */
    @PostMapping("/dataCore/boxsetting/batchUpdateBoxSettingStatus")
    Mono<BaseResponse> batchUpdateBoxSettingStatus(
            @RequestBody List<BsBoxSettingPo> poList);

    /**
     * 修改设备信息
     *
     * @param bsBox
     * @return
     */
    @PostMapping("/dataCore/device/evse/updateBsBox")
    Mono<BaseResponse> updateBsBox(@RequestBody BsBoxPo bsBox);


    @PostMapping("/dataCore/priceTemp/getChargePrice")
    Mono<ObjectResponse<ChargePriceVo>> getChargePrice(@RequestParam("priceId") Long priceId);

    // 获取计费模板分时信息
    @PostMapping("/dataCore/priceTemp/getChargeV2List")
    Mono<ListResponse<ChargeV2>> getChargeV2List(@RequestBody List<Long> priceIdList);

    // 单个桩计费模板下发（下发上次计费信息）
    @GetMapping(value = "/dataCore/priceTemp/sendPriceSchema")
    Mono<ObjectResponse<String>> sendPriceSchema(@RequestParam(value = "evseNo") String evseNo);

    // 即时下发场站默认计费模板
    @GetMapping("/dataCore/priceTemp/downDefault")
    Mono<BaseResponse> priceTempDownDefault(@RequestParam("evseNo") String evseNo);

    // 获取桩配置重发参数
    @PostMapping(value = "/dataCore/evseManager/getResendEvseCfgParam")
    Mono<ObjectResponse<ModifyEvseCfgParam>> getResendEvseCfgParam(
        @RequestBody EvseCfgResendDto dto);

    // 按桩重新下发场站默认配置
    @GetMapping(value = "/dataCore/evseManager/downSettingByEvse")
    Mono<BaseResponse> downSettingByEvse(@RequestParam("evseNo") String evseNo);

    // 下发上次桩配置
    @GetMapping(value = "/dataCore/evseManager/downLastSettingByEvse")
    Mono<BaseResponse> downLastSettingByEvse(@RequestParam("evseNo") String evseNo);

    // 桩告警模板消息发送
    @GetMapping("/dataCore/msgSend/sendEvseCfgAlarm")
    Mono<BaseResponse> sendEvseCfgAlarm(@RequestParam(value = "evseNo") String evseNo,
                                        @RequestParam(value = "customWarningDesc", required = false) String customWarningDesc);


    // 记录拔枪时间
    @PostMapping(value = "/dataCore/order/setPlugOutTime")
    Mono<BaseResponse> setPlugOutTime(@RequestParam("orderNo") String orderNo);

}
