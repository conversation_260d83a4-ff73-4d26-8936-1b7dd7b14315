package com.cdz360.iot.worker.model.iot;

import com.cdz360.base.model.iot.dto.EvseFullDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EvseExt extends EvseFullDto {

    @Schema(description = "生产日期(出厂日期)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date produceDate;

    @Schema(description = "运营日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openForBusinessDate;

    @Schema(description = "户号")
    private String accountNumber;

    @Schema(description = "桩出场编号")
    private String productSN;
}
