package com.cdz360.iot.worker.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.iot.model.evse.BsBoxPo;
import com.cdz360.iot.model.pv.dto.OssStsDto;
import com.cdz360.iot.worker.model.dongzheng.BsBoxSettingPo;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.springframework.data.util.Pair;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_OPEN_HLHT_REST,
        fallbackFactory = OpenHlhtRestHystrixFeignClientFactory.class)
public interface OpenHlhtRestFeignClient {

    // 获取文件上传的STS信息
    @PostMapping(value = "/hlhtrest/inner/queryOrderlyPowerList")
    Mono<ListResponse<Pair<Long,BigDecimal>>> queryOrderlyPowerList(List<Long> transformerIds);

}
