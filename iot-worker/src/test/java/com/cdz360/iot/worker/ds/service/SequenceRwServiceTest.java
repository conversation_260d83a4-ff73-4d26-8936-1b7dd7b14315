package com.cdz360.iot.worker.ds.service;

import com.cdz360.iot.ds.rw.SequenceRwService;
import com.cdz360.iot.worker.IotWorkerTestBase;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description
 * @Date 2019/1/7
 **/
public class SequenceRwServiceTest extends IotWorkerTestBase {
    private final Logger logger = LoggerFactory.getLogger(SequenceRwServiceTest.class);

    @Autowired
    private SequenceRwService sequenceRwService;

    @Test
    public void getNextOutRequestSeq() {
        String nextOutRequestSeq = sequenceRwService.getNextOutRequestSeq();
        logger.info("nextOutRequestSeq = {}", nextOutRequestSeq);
    }

    @Test
    public void getNextGwnoIdxTwice() {
        String nextVal = sequenceRwService.getNextGwno();
        String nextVal1 = sequenceRwService.getNextGwno();
        Assertions.assertNotEquals(nextVal, nextVal1);
    }
}