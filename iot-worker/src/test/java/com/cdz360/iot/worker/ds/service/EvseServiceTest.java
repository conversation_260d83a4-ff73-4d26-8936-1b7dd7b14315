package com.cdz360.iot.worker.ds.service;

import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.order.type.EvseDebugMethod;
import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.iot.worker.IotWorkerTestBase;
import com.cdz360.iot.worker.model.iot.Evse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2019/1/7
 **/
public class EvseServiceTest extends IotWorkerTestBase {
    private final Logger logger = LoggerFactory.getLogger(EvseServiceTest.class);

    @Autowired
    private EvseService evseService;

    private  Evse evse;

    @BeforeEach
    public void init() {
        this.evse = new Evse(){{
            setGwno("abc123");
            setEvseId("yh111");
            setProtocolVer(350);
            setEvseStatus(EvseStatus.IDLE)
                    .setSupply(SupplyType.DC).setNet(NetType.WIFI)
                    .setFirmwareVer("007-110-350")
                    .setName("Test");
        }};
    }

    @Test
    public void addEvse() {
        boolean b = evseService.addEvse(evse);
        logger.info("addEvse result = {}", b);
        Assert.isTrue(b, "error");
    }

    @Test
    public void update() {
        evseService.addEvse(evse);

        EvsePo evsePo = evseService.getEvsePo(evse.getEvseId(), false);
        boolean b = evseService.update(evsePo);
        logger.info("update result = {}", b);
        Assert.isTrue(b, "error");
    }

    @Test
    public void getEvsePo() {
        evseService.addEvse(evse);

        EvsePo evsePo = evseService.getEvsePo(evse.getEvseId(), false);
        logger.info("evsePo = {}", evsePo);
        Assert.notNull(evsePo, "error");
    }

    @Test
    public void getEvse() {
        evseService.addEvse(evse);

        EvsePo evse11 = evseService.getEvse(evse.getEvseId(), false);
        logger.info("evse11 = {}", evse11);
        Assert.notNull(evse11, "error");
    }

    @Test
    public void updateDebugTag() {
        this.addEvse();
        int count = evseService.updateDebugTag(evse.getEvseId(), EvseDebugMethod.ON);
        Assert.isTrue(count >= 1, "error");
    }

    @Test
    public void test_batchUpdateEvsePasscode() {
        List<String> evseNoList = List.of("299999999");
        evseService.batchUpdateEvsePasscode(evseNoList, "22222");
        evseService.batchUpdateEvsePasscode(evseNoList, "22222");
    }
}