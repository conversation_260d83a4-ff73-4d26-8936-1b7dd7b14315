package com.cdz360.iot.worker.biz;

import com.cdz360.iot.model.evse.EvseReportRequestV2;
import com.cdz360.iot.worker.IotWorkerTestBase;
import com.cdz360.iot.worker.ds.service.EvseService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description
 * @Date 2019/1/7
 **/
public class EvseProcessorTest extends IotWorkerTestBase {
    private final Logger logger = LoggerFactory.getLogger(EvseProcessorTest.class);

    @InjectMocks
    private EvseProcessor evseProcessor;
    @Autowired
    private EvseReportRequestBuilder evseReportRequestBuilder;
    @Mock
    private EvseService evseService;
//    @Mock
//    private DzDeviceManagerFeignClient deviceManagerFeignClient;

    private EvseReportRequestV2 report;

    @BeforeEach
    public void init() {
        this.report = this.evseReportRequestBuilder.build();
    }

    @Test
    public void processEvseNormalStatus() {
        //report.setEvseVer(1);
//        logger.info("report = {}", report);
//        Mockito.when(evseService.getEvsePo(report.getEvseNo(), true)).thenReturn(new EvsePo());
//        ObjectResponse<DeviceStatusInfo> res = new ObjectResponse<DeviceStatusInfo>();
//        //res.setSuccess(true);
//        //Mockito.when(deviceManagerFeignClient.evseStatus(report, DeviceStatusEnum.STATUS_ONLINE.getCode())).thenReturn(res);
//        if (report.getEvseStatus() == EvseStatus.IDLE
//                || report.getEvseStatus() == EvseStatus.BUSY) {
//            this.evseProcessor.processEvseNormalStatusV2(report);
//        }
    }
}