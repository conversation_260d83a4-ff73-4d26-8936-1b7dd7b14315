package com.cdz360.iot.common.utils;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.common.exception.DcBalanceException;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FeignResponseValidate {

    protected static final Logger logger = LoggerFactory.getLogger(FeignResponseValidate.class);

    public static void check(BaseResponse res) {
        checkIgnoreData(res);

        if (res instanceof ObjectResponse) {
            ObjectResponse objRes = (ObjectResponse) res;
            if (((ObjectResponse) res).getData() == null) {
                logger.error("no data.... {}", JsonUtils.toJsonString(objRes));
                throw new DcServerException("数据为空");
            }
        } else if (res instanceof ListResponse) {
            ListResponse objRes = (ListResponse) res;
            if (((ListResponse) res).getData() == null) {
                logger.error("no data.... {}", JsonUtils.toJsonString(objRes));
                throw new DcServerException("数据为空");
            }
        }
    }

    public static <T> T checkReturn(ObjectResponse<T> res) {
        check(res);
        return res.getData();
    }

    public static <T> List<T> checkReturn(ListResponse<T> res) {
        check(res);
        return res.getData();
    }

    public static void checkIgnoreData(BaseResponse res) {
        if (res == null) {
            logger.warn("Feign 调用失败");
            throw new DcServiceException(DcConstants.KEY_RES_CODE_SERVER_ERROR,
                "系统繁忙，请稍后重试");
        }

        if (res.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
            logger.warn("res = {}", JsonUtils.toJsonString(res));
            if (res.getStatus() == DcConstants.KEY_RES_CODE_BALANCE_ERROR) {
                throw new DcBalanceException(res.getStatus(), res.getError());
            }
            throw new DcServiceException(res.getStatus(), res.getError());
        }
    }
}
