package com.cdz360.iot.common.base;

/**
 * 基础常量
 */
public interface IotConstants {

    Long superTopCommId = 34474L;

    /**
     * IOT端的http头, 用于放请求序列号seq
     */
    String HTTP_HEADER_SEQ = "dc-seq";

    String REDIS_KEY_OUT_REQ = "oq-";

    String MQTT_CLOUD_CLIENT_PREFIX = "cloud";

    int IOT_GW_VER_1 = 1;   // 网关协议版本 1
    int IOT_GW_VER_2 = 2;   // 网关协议版本 2
    int IOT_GW_VER_3 = 3;   // 网关协议版本 3

    // 桩协议版本
    int PROTOCOL_VERSION_200 = 200;
    int PROTOCOL_VERSION_320 = 320;
    int PROTOCOL_VERSION_304 = 304;
    int PROTOCOL_VERSION_345 = 345;
    int PROTOCOL_VERSION_350 = 350;
    int PROTOCOL_VERSION_360 = 360;
    int PROTOCOL_VERSION_370 = 370;


    int SERVFEE_TIME_DIVISION_PROTOVER = 350;

    long MQTT_TMP_LOG_DELAY = 30000;//延迟时间,单位毫秒

    //    String EVSE_REGISTER_NUMBER = "793881964844";
//    //配置更新结果. SUCCESS: 成功; FAIL: 失败; TIMEOUT: 超时
//    String EVSERESULT_SUCCESS = "SUCCESS";
//    String EVSERESULT_FAIL = "FAIL";
//    String EVSERESULT_TIMEOUT = "TIMEOUT";
    //下发结果:1下发成功2失败3下发中
    Integer EVSE_SETTING_STATUS_SUCCESS = 1;
    Integer EVSE_SETTING_STATUS_FAIL = 2;

    String EVSE_CONFIG_LIST = "evseCfg";


    String SITE_SYNC_QUEUE_NAME = "iotSite.iotDeviceMgm";

    String MQ_QUEUE_ESS_GW_PUSH_DATA = "essQueue.essGwPushData";

    String MQ_QUEUE_SITE_GROUP_DEVICE_MGM = "siteGroupQueue.iotDeviceMgm";

    String DM_IOT_QUEUE_NAME = "iotQueue.iotDeviceMgm";

    String MQ_IOT_QUEUE_ESS_INFO_MONITOR = "iotQueue.iotEssInfoMonitor";

    /**
     * iot库同步商户信息的队列名称
     */
    String MQ_QUEUE_COMMERCIAL_DEVICE_MGM = "commercialQueue.iotDeviceMgm";

    String IOT_GW_UP_CMD_QUEUE_NAME = "iotGwUpCmd.iotTask";
    String IOT_GW_DOWN_CMD_QUEUE_NAME = "iotGwDownCmd.iotTask";
    String IOT_GW_CMD_TIMEOUT_QUEUE_NAME = "iotGwCmdTimeout.iotTask";

    String IOT_SYSTEM_TEMP_DIR_KEY = "java.io.tmpdir";
    String IOT_EVSE_BUNDLE_CONTEXT_TXT_NAME = "evseBundleContext.json";
    String IOT_UPGRADE_PG_NAME = "upgrade.json";
    String JAR_BOOT_INF_GIT = "BOOT-INF/classes/git.properties";

    String MQTT_TYPE_MOSQUITTO = "mosquitto";
}
