package com.cdz360.iot.model.base;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

public class BaseGwPackage extends BaseObject {

    @Schema(description = "对应请求消息的序列号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String seq;

    @Schema(description = "消息方法, 请求/响应")
    private IotPackageType type;

    public BaseGwPackage() {

    }

    public BaseGwPackage(IotPackageType type) {
        this.type = type;
    }

    public BaseGwPackage(IotPackageType type, String seq) {
        this.type = type;
        this.seq = seq;
    }

    public String getSeq() {
        return seq;
    }

    public BaseGwPackage setSeq(String seq) {
        this.seq = seq;
        return this;
    }

    public IotPackageType getType() {
        return type;
    }

    public BaseGwPackage setType(IotPackageType type) {
        this.type = type;
        return this;
    }
}
