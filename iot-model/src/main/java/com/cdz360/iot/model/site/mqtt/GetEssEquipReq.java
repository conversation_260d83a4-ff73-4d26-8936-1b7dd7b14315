package com.cdz360.iot.model.site.mqtt;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.RandomStringUtils;

/**
 * 获取ESS设备列表
 */
@Data
@Accessors(chain = true)
public class GetEssEquipReq {

    @Schema(description = "需要获取设备的ESS,为空不处理", example = "[\"aaa\"]")
    private List<String> dnoList;

    public static class REQ extends BaseMqttMsg<GetEssEquipReq> {

        @JsonProperty("c")
        @Override
        public IotGwCmdType2 getCmd() {
            return IotGwCmdType2.GET_ESS_EQUIP;
        }

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }

    public static class builder {
        private REQ req;

        public builder(String gwno) {
            this.req = new REQ();
            this.req.setGwno(gwno);
            this.req.setData(new GetEssEquipReq());
            this.req.setSeq(RandomStringUtils.randomAlphanumeric(16));
        }

        public builder setDnoList(List<String> dnoList) {
            this.req.getData().setDnoList(dnoList);
            return this;
        }

        public REQ build() {
            return this.req;
        }
    }

}
