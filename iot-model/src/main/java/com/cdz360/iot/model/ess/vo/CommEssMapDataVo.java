package com.cdz360.iot.model.ess.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "工商储能地图数据")
@Data
@Accessors(chain = true)
public class CommEssMapDataVo {

    @Schema(description = "站点数")
    private Long siteCnt;

    @Schema(description = "设备装机功率，单位: kW")
    private BigDecimal devicePower;

    @Schema(description = "设备装机容量，单位: Ah")
    private BigDecimal deviceCapacity;

    @Schema(description = "总放电量，单位: kW·h")
    private BigDecimal totalDischargeKwh;

    @Schema(description = "总充电量，单位: kW·h")
    private BigDecimal totalChargeKwh;

    @Schema(description = "总收益")
    private BigDecimal totalProfit;
}
