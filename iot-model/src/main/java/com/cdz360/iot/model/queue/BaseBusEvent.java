package com.cdz360.iot.model.queue;

//public abstract class BaseBusEvent  extends RemoteApplicationEvent {
//
//    protected BaseBusEvent() {
//        // for deserialize
//    }
//
//    public BaseBusEvent(Object source, String originService, String destinationService) {
//        super(source, originService, destinationService);
//    }
//
//    public String toJsonString() {
//        String ret = "";
//        ObjectMapper om = new ObjectMapper();
//        try {
//            ret = om.writeValueAsString(this);
//        } catch (JsonProcessingException e) {
//            LogManager.getLogger(this).trace(e.getMessage(), e);
//
//        }
//        return ret;
//    }
//
//
//    @Override
//    public String toString() {
//        return this.toJsonString();
//    }
//}
