package com.cdz360.iot.model.evse.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OfflineCardLogPo {

    private Long id;

    @Schema(description = "卡号")
    private String cardNo;

    @Schema(description = "刷卡的充电桩号")
    private String evseNo;

    @Schema(description = "设备端上报的卡余额", example = "123.45")
    private BigDecimal amount;

    private Date createTime;
}
