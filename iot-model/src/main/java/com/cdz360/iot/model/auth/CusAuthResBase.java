package com.cdz360.iot.model.auth;

import com.cdz360.iot.model.base.BaseObject;

import java.math.BigDecimal;

/**
 * @Classname CusAuthResThin
 * @Description
 * @Date 2019/5/27 11:19
 * @Created by Rafael
 */
public class CusAuthResBase extends BaseObject {
    private BigDecimal frozenAmount;
    private BigDecimal balance;//金额扣款账户总余额, 单位'元'
    private String carNo;
    private BigDecimal power;//电量扣款账户总余额, 单位'kwh'
    public BigDecimal getBalance() {
        return balance;
    }

    public CusAuthResBase setBalance(BigDecimal balance) {
        this.balance = balance;
        return this;
    }

    public BigDecimal getFrozenAmount() {
        return frozenAmount;
    }

    public CusAuthResBase setFrozenAmount(BigDecimal frozenAmount) {
        this.frozenAmount = frozenAmount;
        return this;
    }

    public String getCarNo() {
        return carNo;
    }

    public CusAuthResBase setCarNo(String carNo) {
        this.carNo = carNo;
        return this;
    }

    public BigDecimal getPower() {
        return power;
    }

    public CusAuthResBase setPower(BigDecimal power) {
        this.power = power;
        return this;
    }
}