package com.cdz360.iot.model.evse.cfg;

import com.cdz360.base.model.base.type.ChargePriceCategory;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ChargeV2 {

    @Schema(description = "主模板ID", example = "456")
    private Long templateId;

    @Schema(description = "价格编码, 取值范围 0 ~ 255, 不能重复")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private int code;//价格编码, 取值范围 0 ~ 255, 不能重复

    @Schema(description = "尖峰平谷标签 0, 未知; 1, 尖; 2, 峰; 3, 平; 4, 谷", example = "2")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private ChargePriceCategory category;

    @Schema(description = "计价开始时间, 格式为 HH:MM, 从 00:00 开始, 开始时间取闭区间.")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String startTime;//计价开始时间, 格式为 HH:MM, 从 00:00 开始, 开始时间取闭区间.

    @Schema(description = "计价结束时间, 格式为 HH:MM, 至 24:00 为止, 结束时间取开区间")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String stopTime;//计价结束时间, 格式为 HH:MM, 至 24:00 为止, 结束时间取开区间

    @Schema(description = "电费单价, 单位'元'")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal elecPrice;//电费单价, 单位'元'

    @Schema(description = "服务费单价, 单位'元'")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal servPrice;//服务费单价, 单位'元'
}
