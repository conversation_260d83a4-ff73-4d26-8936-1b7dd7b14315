package com.cdz360.iot.model.site.ctrl;

import com.cdz360.base.model.base.type.SiteCtrlStatusType;
import com.cdz360.iot.model.evse.EvseReportRequestV2;
import lombok.Data;

import java.util.List;

/**
 * @Classname SiteCtrlStatus
 * @Description
 * @Date 4/21/2020 6:55 PM
 * @Created by Rafael
 */
@Data
public class SiteCtrlStatus {
    private String ctrlNo;
    private String swVer;//控制器软件版本
    private SiteCtrlStatusType ctrlStatus;
    private int loadRatio;//负载率
    private int pwrTemp;//配电柜温度

    /**
     * 告警码
     * 0x00：正常
     * 0x01：负载告警
     * 0x02：配电柜温度告警
     * 0x04：充电桩烟雾告警
     * 0x08：充电桩门禁告警
     * 发生多个告警时将告警码或运算后发送
     */
    private Integer alertCode;

    /**
     * 故障码
     * 0x00：正常
     * 0x01：控制器配置信息异常
     * 0x02：负载异常
     * 0x04：配电柜温度异常
     * 0x08：充电桩功率输出异常
     * 0x10：充电桩离线异常
     * 发生多个异常时将故障码或运算后发送
     */
    private Integer errorCode;

    private List<EvseReportRequestV2> alertEVSEs;//告警充电桩信息数组
    private List<EvseReportRequestV2> errorEVSEs;//异常充电桩信息数组

    private String info;

}