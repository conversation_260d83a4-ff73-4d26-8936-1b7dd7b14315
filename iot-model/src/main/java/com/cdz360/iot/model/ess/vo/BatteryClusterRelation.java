package com.cdz360.iot.model.ess.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "电池堆电池簇电池组关系")
@Data
@Accessors(chain = true)
public class BatteryClusterRelation {
    @Schema(description = "电池簇typeId")
    private Integer batteryClusterTypeId;

    @Schema(description = "电池簇Id")
    private Long batteryClusterId;

    @Schema(description = "电池簇号")
    private Long clusterNo;

    @Schema(description = "电池组集合")
    private List<BatteryPackRelation> batteryPackRelationList;
}
