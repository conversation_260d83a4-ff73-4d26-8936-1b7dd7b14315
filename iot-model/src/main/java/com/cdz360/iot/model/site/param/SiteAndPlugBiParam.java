package com.cdz360.iot.model.site.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SiteAndPlugBiParam {

    private List<Integer> siteStatusList;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "运营类型, 不传查询所有. 0, 未知; 1, 自营; 2, 非自营; 3, 互联互通; 4, 脱机自营; 5, 脱机非自营;")
    private List<Integer> bizTypeList;

    private Long commId;

    private String idChain;

    private List<String> gids;

}
