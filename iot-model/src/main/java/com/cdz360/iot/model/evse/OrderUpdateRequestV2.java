package com.cdz360.iot.model.evse;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OrderUpdateRequestV2 {
    @Schema(description = "唯一的订单号")
    private String orderNo;

    @Schema(description = "桩在云平台的唯一ID")
    private String evseNo;

    @Schema(description = "充电枪编号")
    private String plugNo;

    @Schema(description = "充电枪ID")
    private Integer plugId;

    //分时段详情
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<OrderTimeIntervalDetailV2> details;

    /**
     * 电价模板编号,仅上报分时数据时需要
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long priceCode;

    /**
     * 充电中分时数据
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<OrderDetail> priceItems;

    @Schema(description = "是否需要使用协议价", required = false)
    private Boolean isNeedDiscount;
}
