package com.cdz360.iot.model.ess.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "电池堆/电池簇与BMS设备层级关系")
@Data
@Accessors(chain = true)
public class BmsRelevantVo {

    @Schema(description = "BMS设备编号")
    private String bmsDno;

    @Schema(description = "电池堆设备编号")
    private String stackDno;

    @Schema(description = "电池簇设备编号")
    private String clusterDno;
}
