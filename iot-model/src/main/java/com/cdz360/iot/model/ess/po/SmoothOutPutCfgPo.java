package com.cdz360.iot.model.ess.po;


import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "平滑输出")

public class SmoothOutPutCfgPo {



	@Schema(description = "模板id(t_dev_cfg.id)")

	@NotNull(message = "cfgId 不能为 null")

	private Long cfgId;



	@Schema(description = "平滑输出使能")

	@NotNull(message = "smoothOutputEnable 不能为 null")

	private Boolean smoothOutputEnable;



	@Schema(description = "监测周期")

	private Long monitoringPeriod;



	@Schema(description = "变化幅值")
	private Long amplitude;



	@Schema(description = "目标额定功率")
	private BigDecimal targetPowerRating;


}

