package com.cdz360.iot.model.evse.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.iot.model.evse.type.EvseModelFlag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListEvseModelParam extends BaseListParam {

    private String model;

    private String brand;

    private String series;

    private SupplyType supply;

    private List<EvseModelFlag> flagList;

    private Boolean status;

    private List<Long> modelIdList;

}
