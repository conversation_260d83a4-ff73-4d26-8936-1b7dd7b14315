package com.cdz360.iot.model.pv.param;

import com.cdz360.base.model.iot.type.EssEquipType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "查询控制器列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListCtrlParam extends ListSiteDevParam {

    @Schema(description = "商户Id链")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站ID列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> siteIdList;

    @Schema(description = "控制器编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwno;

    @Schema(description = "储能DNO")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String essDno;

    @Schema(description = "设备编号列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> dnoList;

    @Schema(description = "设备类型列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<EssEquipType> equipTypeList;

}
