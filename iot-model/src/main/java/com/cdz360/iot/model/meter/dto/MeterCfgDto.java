package com.cdz360.iot.model.meter.dto;

import com.cdz360.base.model.meter.vo.MeterTransformationRatio;
import com.cdz360.iot.model.ess.dto.EquipCfgDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class MeterCfgDto extends EquipCfgDto {

    @Schema(title = "电表通信地址")
    @JsonProperty("addr")
    private String deviceAddr;

    @Schema(title = "采样周期", description = "单位秒", example = "60")
    @JsonProperty("st")
    private Integer samplingTime;

    /**
     * 互感器变比
     */
    @JsonProperty("tr")
    private MeterTransformationRatio tr;
}
