package com.cdz360.iot.model.evse;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class OrderDetailV2 {
    @Schema(description = "分段计费开始时间, unix时间戳",example = "1557111811947")
    private Long startTime;
    @Schema(description = "分段计费结束时间, unix时间戳",example = "1557111811947")
    private Long stopTime;
    @Schema(description = "价格分段编号")
    private Integer priceCode;
    @Schema(description = "当前累计电量, 单位'kwh'",example = "34567")
    private BigDecimal kwh;
    @Schema(description = "当前累计电费金额, 单位'元'")
    private BigDecimal elecFee;
    @Schema(description = "当前累计电费金额, 单位'元'")
    private BigDecimal servFee;
}
