package com.cdz360.iot.model.evse.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class EvseModuleDetailPo {

    private Long id;

    private Long moduleId;

    private Integer idx;

    private String deviceNo;

    private List<OldDevicePo> oldDeviceNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;

    @Data
    @Accessors(chain = true)
    public static class OldDevicePo {
        private String no;

        @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
        private Date date;

        private String ywOrderNo;
    }
}
