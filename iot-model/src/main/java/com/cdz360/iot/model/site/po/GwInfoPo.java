package com.cdz360.iot.model.site.po;

import com.cdz360.iot.model.base.DbObject;
import com.cdz360.iot.model.site.type.GwMqType;
import com.cdz360.iot.model.type.GwStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class GwInfoPo extends DbObject {

    @JsonView(WithoutPasscodeView.class)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "网关")
    private String gwno;

    @Schema(description = "网关名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @Schema(description = "网关状态")
    private GwStatus status;

    @Schema(description = "消息队列类型")
    private GwMqType mqType;

    @JsonView(WithoutPasscodeView.class)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "网关协议版本号")
    private int ver;
    @JsonView(WithPasscodeView.class)
    private String passcode;
    @JsonView(WithoutPasscodeView.class)
    @Schema(description = "网关IP")
    private String ip;
//    @JsonView(WithoutPasscodeView.class)
//    private String siteId;
    @JsonView(WithoutPasscodeView.class)
    @Schema(description = "所在城市编码")
    private String cityCode;
    @JsonView(WithoutPasscodeView.class)
    private boolean enable;
    private String lanIp;
    private String mac;
    private Double lon;
    private Double lat;

    @Schema(description = "设备启动时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bootTime;

    @Schema(description = "软件版本号")
    private String swVer;

    @Schema(description = "软件版本编号")
    private Integer swVerCode;

    @Schema(description = "代码版本号")
    private String sourceCodeVer;

    @Schema(description = "期望升级记录ID")
    private Long expectUpgradeLogId;

    @Schema(description = "当前生效升级记录ID")
    private Long actualUpgradeLogId;

    public interface WithoutPasscodeView {}

    public interface WithPasscodeView extends WithoutPasscodeView {}
}
