package com.cdz360.iot.model.ess.po;


import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@Schema(description = "用户设备关系表")
public class UserDeviceRefPo {

    @Schema(description = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "归属用户ID(t_user.id)")
    private Long uid;

    @Schema(description = "设备编号")
    @NotNull(message = "dno 不能为 null")
    @Size(max = 16, message = "dno 长度不能超过 16")
    private String dno;

    @Schema(description = "国家地区代码(Alpha-3 code)",
        externalDocs = @ExternalDocumentation(
            description = "iso-3166 Country Codes",
            url = "https://www.iso.org/obp/ui/#search"), hidden = true)
    private String countryCode;

    @Schema(description = "true(绑定);false(解绑)")
    @NotNull(message = "enable 不能为 null")
    private Boolean enable;

    @Schema(description = "设备从属关系(设备共享使用),一个设置只能有一个为主属关系")
    @NotNull(message = "master 不能为 null")
    private Boolean master;

    @Schema(description = "记录创建时间")
    @NotNull(message = "createTime 不能为 null")
    private Date createTime;

    @Schema(description = "记录更新时间")
    @NotNull(message = "updateTime 不能为 null")
    private Date updateTime;
}

