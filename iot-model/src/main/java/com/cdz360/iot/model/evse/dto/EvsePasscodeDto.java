package com.cdz360.iot.model.evse.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname EvsePasscodeDto
 * @Description
 * @Date 2019/10/22 15:59
 * @Created by tang<PERSON><PERSON>
 */
@Data
@Accessors(chain = true)
@Schema(description = "充电桩密钥回复")
public class EvsePasscodeDto {
    @Schema(description = "HEX编码的密钥")
    private String passcode;
}
