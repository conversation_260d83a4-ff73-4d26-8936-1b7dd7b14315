package com.cdz360.iot.model.srs.dto;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Schema(description = "逆变器运行数据")
@Accessors(chain = true)
public class SrsRtDataDto {

//    @JsonProperty("id")
//    @Schema(description = "设备modbus id", example = "123")
//    private Integer deviceId;

    @JsonProperty("dno")
    @Schema(description = "设备编号", example = "ABC123")
    private String dno;

    // 错误代码
    @JsonProperty("err")
    private List<Long> errorCodeList;

    @JsonProperty("v")
    @Schema(description = "太阳辐射值, 单位: W/㎡", example = "102")
    private Integer value;

    @JsonProperty("dv")
    @Schema(description = "辐射偏差值(0~1800), 单位: W/㎡", example = "102")
    private Integer deviationValue;

    @JsonProperty("da")
    @Schema(description = "设备地址(1~254)", example = "1")
    private Integer deviceAddress;

    @JsonProperty("br")
    @Schema(description = "设备波特率 0代表2400 1代表4800 2代表9600", example = "0")
    private Integer baudRate;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
