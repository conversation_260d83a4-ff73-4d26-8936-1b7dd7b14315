package com.cdz360.iot.model.ess.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "光储ESS PCS信息")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class EssEquipPCSVo extends EssEquipVo{

    @Schema(description = "机柜序列号")
    private String cabinetNo;

    @Schema(description = "开关机状态")
    private Integer dcacSwitchState;

    @Schema(description = "并离网状态")
    private Integer dcacOnGridState;

    @Schema(description = "模块温度")
    private BigDecimal moduleTemp;

    @Schema(description = "环境温度")
    private BigDecimal environmentTemp;

    @Schema(description = "AB交流线电压")
    private BigDecimal acVoltageAB;

    @Schema(description = "BC交流线电压")
    private BigDecimal acVoltageBC;

    @Schema(description = "CA交流线电压")
    private BigDecimal acVoltageCA;

    @Schema(description = "A相交流电流")
    private BigDecimal acCurrentA;

    @Schema(description = "B相交流电流")
    private BigDecimal acCurrentB;

    @Schema(description = "C相交流电流")
    private BigDecimal acCurrentC;

    @Schema(description = "功率因数")
    private Integer acPowerFactor;

    @Schema(description = "有功功率")
    private BigDecimal acActivePower;

    @Schema(description = "无功功率")
    private BigDecimal acReactivePower;

    @Schema(description = "视在功率")
    private BigDecimal acApparentPower;

    @Schema(description = "累积充电电量")
    private Long acTotalChargeKwh;

    @Schema(description = "累积放电电量")
    private Long acTotalDisChargeKwh;

    @Schema(description = "当天充电电量")
    private Integer acTodayChargeKwh;

    @Schema(description = "当天放电电量")
    private Integer acTodayDisChargeKwh;

    @Schema(description = "交流频率")
    private BigDecimal acFrequency;

    @Schema(description = "直流电压")
    private BigDecimal dcVoltage;

    @Schema(description = "直流电流")
    private BigDecimal dcCurrent;

    @Schema(description = "控制模式")
    private Integer controlModel;

    @Schema(description = "直流功率")
    private BigDecimal dcPower;

    @Schema(description = "累积充电电量")
    private Long dcTotalChargeKwh;

    @Schema(description = "累积放电电量")
    private Long dcTotalDisChargeKwh;
}
