package com.cdz360.iot.model.ess.dto;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.util.List;

/**
 * 储能设备 -- 集控器
 */
@Data
@Accessors(chain = true)
public class CentralControl {

    // 有功功率设置
    @JsonProperty("aps")
    private BigDecimal acPowerSetting;
    /**
     * 有功控制模式
     * 0: Fixed active power
     * 1: Volt-Watt
     * 2: Freq-Watt
     * 3: V-W&F-W
     */
    @JsonProperty("apcm")
    private Integer acPowerControlMode;
    // 开关机命令(0关 1开)
    @JsonProperty("sc")
    private Integer switchCommand;
    // 清除报错命令
    @JsonProperty("fcc")
    private Integer failureClearanceCommand;
    // 并离网模式(1离网 2并网)
    @JsonProperty("gim")
    private Integer gridInterconnectionMode;
    // 开机方式(1自动开机 2手动启动)
    @JsonProperty("bm")
    private Integer bootMode;
    // 本地远程模式(0本地 1远程)
    @JsonProperty("lrm")
    private Integer localRemoteMode;
    // 确认紧急关机(0:disable 1:enable)
    @JsonProperty("epo")
    private Integer emergencyPowerOff;

    // 干接点1(true: connect; false: disconnect)
    @JsonProperty("odc1")
    private Boolean outputDryContact1;
    // 干接点2
    @JsonProperty("odc2")
    private Boolean outputDryContact2;
    // 干接点3
    @JsonProperty("odc3")
    private Boolean outputDryContact3;
    // 干接点4
    @JsonProperty("odc4")
    private Boolean outputDryContact4;
    // 干接点5
    @JsonProperty("odc5")
    private Boolean outputDryContact5;
    // 干接点6
    @JsonProperty("odc6")
    private Boolean outputDryContact6;
    // 干接点7
    @JsonProperty("odc7")
    private Boolean outputDryContact7;
    // 干接点8
    @JsonProperty("odc8")
    private Boolean outputDryContact8;
    // 干接点9
    @JsonProperty("odc9")
    private Boolean outputDryContact9;
    // 干接点10
    @JsonProperty("odc10")
    private Boolean outputDryContact10;
    // 干接点11
    @JsonProperty("odc11")
    private Boolean outputDryContact11;
    // 干接点12
    @JsonProperty("odc12")
    private Boolean outputDryContact12;
    // 干接点13
    @JsonProperty("odc13")
    private Boolean outputDryContact13;
    // 干接点14
    @JsonProperty("odc14")
    private Boolean outputDryContact14;
    // 干接点15
    @JsonProperty("odc15")
    private Boolean outputDryContact15;
    // 干接点16
    @JsonProperty("odc16")
    private Boolean outputDryContact16;

    // EMS设备个数
    @JsonProperty("edn")
    private Integer emsDeviceNum;

    // EMS通讯协议
    @JsonProperty("ecp")
    private Integer emsCommunicationProtocol;

    // PCS设备个数
    @JsonProperty("pdn")
    private Integer pcsDeviceNum;

    // PCS通讯协议
    @JsonProperty("pcd")
    private Integer pcsCommunicationProtocol;

    // STS设备个数
    @JsonProperty("sdn")
    private Integer stsDeviceNum;

    // STS通讯协议
    @JsonProperty("scp")
    private Integer stsCommunicationProtocol;

    // 集控设备个数
    @JsonProperty("ccd")
    private Integer centrolControlDeviceNum;

    // 集控通讯协议
    @JsonProperty("cccp")
    private Integer centrolControlCommunicationProtocol;

    // 电池堆个数
    @JsonProperty("bsdn")
    private Integer batStackDeviceNum;

    // 系统簇数量
    @JsonProperty("bcd")
    private Integer batClusterDeviceNum;

    // 簇LMU个数
    @JsonProperty("bpnooc")
    private Integer batPackNumOfOneCluster;

    // BMS通讯协议
    @JsonProperty("bcp")
    private Integer bmsCommunicationProtocol;

    // 光伏逆变器设备个数
    @JsonProperty("pidn")
    private Integer pvInvDeviceNum;

    // 光伏逆变器通讯协议
    @JsonProperty("picp")
    private Integer pvInvCommunicationProtocol;

    // 光伏汇流箱设备个数
    @JsonProperty("pjbdn")
    private Integer pvJunctionBoxDeviceNum;

    // 光伏汇流箱通讯协议
    @JsonProperty("pjbcp")
    private Integer pvJunctionBoxCommunicationProtocol;

    // 直流充电桩设备个数
    @JsonProperty("dcpdn")
    private Integer dcChargingPileDeviceNum;

    // 直流充电桩通讯协议
    @JsonProperty("dcpcp")
    private Integer dcChargingPileCommunicationProtocol;

    // 交流充电桩设备个数
    @JsonProperty("acpdn")
    private Integer acChargingPileDeviceNum;

    // 交流充电桩通讯协议
    @JsonProperty("acpcp")
    private Integer acChargingPileCommunicationProtocol;

    // 总电网关口电表设备个数
    @JsonProperty("gmdn")
    private Integer gridMeterDeviceNum;

    // 总电网关口电表通信协议
    @JsonProperty("gmcp")
    private Integer gridMeterCommunicationProtocol;

    // 总电网关口电表CT 倍率
    @JsonProperty("gmcr")
    private Integer gridMeterCtRatio;

    // 储能并网点电表设备个数
    @JsonProperty("esgmdn")
    private Integer energyStorageGridMeterDeviceNum;

    // 储能并网点电表通讯协议
    @JsonProperty("esgmcp")
    private Integer energyStorageGridMeterCommunicationProtocol;

    // 储能并网点电表CT 倍率
    @JsonProperty("esgmcr")
    private Integer energyStorageGridMeterCtRatio;

    // 光伏并网点电表设备个数
    @JsonProperty("pigmdn")
    private Integer pvInvGridMeterDeviceNum;

    // 光伏并网点电表通讯协议
    @JsonProperty("pigmcp")
    private Integer pvInvGridMeterCommunicationProtocol;

    // 光伏并网点电表CT 倍率
    @JsonProperty("pigmcr")
    private Integer pvInvGridMeterCtRatio;

    // 充电桩计量表设备个数
    @JsonProperty("cpmdn")
    private Integer chargingPileMeterDeviceNum;

    // 充电桩计量表通信协议
    @JsonProperty("cpmcp")
    private Integer chargingPileMeterCommunicationProtocol;

    // 充电桩计量表CT 倍率
    @JsonProperty("cpmcr")
    private Integer chargingPileMeterCtRatio;

    // 风能并网点电表设备个数
    @JsonProperty("wcgmdn")
    private Integer windCombinedGridMeterDeviceNum;

    // 风能并网点电表通讯协议
    @JsonProperty("wcgmcp")
    private Integer windCombinedGridMeterCommunicationProtocol;

    // 风能并网点电表CT 倍率
    @JsonProperty("wcgmcr")
    private Integer windCombinedGridMeterCtRatio;

    // 负载用电电表设备个数
    @JsonProperty("lmdn")
    private Integer loadMeterDeviceNum;

    // 负载用电电表通信协议
    @JsonProperty("lmcp")
    private Integer loadMeterCommunicationProtocol;

    // 负载用电电表CT 倍率
    @JsonProperty("lmcr")
    private Integer loadMeterCtRatio;

    // ACDC模块设备个数
    @JsonProperty("amdn")
    private Integer acdcModuleDeviceNum;

    // ACDC模块通信协议
    @JsonProperty("amcp")
    private Integer acdcModuleCommunicationProtocol;

    // DCDC模块设备个数
    @JsonProperty("dmdn")
    private Integer dcdcModuleDeviceNum;

    // DCDC模块通讯协议
    @JsonProperty("dmcp")
    private Integer dcdcModuleCommunicationProtocol;

    // 空调设备个数
    @JsonProperty("acdn")
    private Integer airConditionDeviceNum;

    // 空调通讯协议
    @JsonProperty("accp")
    private Integer airConditionCommunicationProtocol;

    // 消防设备个数
    @JsonProperty("ffdn")
    private Integer fireFightingDeviceNum;

    // 消防通信协议
    @JsonProperty("ffcp")
    private Integer fireFightingCommunicationProtocol;

    // UPS设备个数
    @JsonProperty("udn")
    private Integer upsDeviceNum;

    // UPS通信协议
    @JsonProperty("ucp")
    private Integer upsCommunicationProtocol;

    // 柴油机设备个数
    @JsonProperty("dedn")
    private Integer dieselEngineDeviceNum;

    // 柴油机通讯协议
    @JsonProperty("decp")
    private Integer dieselEngineCommunicationProtocol;

    // 储能内部电表设备个数
    @JsonProperty("eimdn")
    private Integer essInsideMeterDeviceNum;

    // 储能内部电表通信协议
    @JsonProperty("eicp")
    private Integer essInsideCommunicationProtocol;

    // 储能内部电表CT 倍率
    @JsonProperty("eicr")
    private Integer essInsideCtRatio;

    // 高压侧电表设备个数
    @JsonProperty("hvsmdn")
    private Integer highVoltageSideMeterDeviceNum;

    // 高压侧电表通信协议
    @JsonProperty("hvsmcp")
    private Integer highVoltageSideMeterCommunicationProtocol;

    // 高压侧电表CT 倍率
    @JsonProperty("hvsmcr")
    private Integer highVoltageSideMeterCtRatio;

    // 高压侧电表PT 倍率
    @JsonProperty("hvsmpr")
    private Integer highVoltageSideMeterPtRatio;

    // 时间设置：年
    @JsonProperty("tsy")
    private Integer timeSettingYear;

    // 时间设置：月
    @JsonProperty("tsm")
    private Integer timeSettingMonth;

    // 时间设置：日
    @JsonProperty("tsd")
    private Integer timeSettingDay;

    // 时间设置：小时
    @JsonProperty("tsh")
    private Integer timeSettingHour;

    // 时间设置：分钟
    @JsonProperty("tsmin")
    private Integer timeSettingMinute;

    // 时间设置：秒
    @JsonProperty("tss")
    private Integer timeSettingSecond;

    // 系统安规
    @JsonProperty("syss")
    private Integer systemSafety;

    // 调度协议
    @JsonProperty("sa")
    private Integer schedulingAgreement;

    // 服务端端口
    @JsonProperty("sp")
    private Integer serverPort;

    // 服务端端IP
    @JsonProperty("si")
    private Long serverIp;

    // 主从机ID
    @JsonProperty("msmi")
    private Integer masterSlaveMachineId;

    // 调度使能开关
    @JsonProperty("des")
    private Integer dispatchEnableSwitch;

    // 通讯方式
    @JsonProperty("cm")
    private Integer communicationMethods;

    // 通讯波特率
    @JsonProperty("cbr")
    private Integer communicationBaudRate;

    // 设备地址
    @JsonProperty("da")
    private Integer deviceAddress;

    // EMS通讯超时时间, 单位: s
    @JsonProperty("ectt")
    private Integer emsCommunicationTimeoutTime;

    // 本地远程模式
    @JsonProperty("lrm2")
    private Integer localRemoteMode2;

    // 远程重启EMS指令: 0: false;  1 true
    @JsonProperty("rems")
    private Boolean restartEms;

    // 恢复EMS出厂设置指令: 0: false;  1 true
    @JsonProperty("fr")
    private Boolean factoryReset;

    // soc校准命令: 0: false;  1 true
    @JsonProperty("socc")
    private Boolean socCalibration;

    // 闭合继电器指令: 0: false;  1 true
    @JsonProperty("cr")
    private Boolean closeRelay;

    // 清除能量值: 0: false;  1 true
    @JsonProperty("re")
    private Boolean resetEnergy;

    // 只清除电表能量值: 0: false;  1 true
    @JsonProperty("rme")
    private Boolean resetMeterEnergy;

    // IP获取方式
    @JsonProperty("iam")
    private Integer ipAcquisitionMethod;

    // IP地址
    @JsonProperty("ia")
    private Integer ipAddress;

    // 子网掩码
    @JsonProperty("sm")
    private Integer subnetMask;

    // 网关
    @JsonProperty("gateway")
    private Integer gateway;

    // 电表功率调节偏移, 单位: 0.1kW
    @JsonProperty("mpro")
    private BigDecimal meterPowerRegulationOffset;

    // 最大通过电表功率, 单位: 0.1kW
    @JsonProperty("mptm")
    private BigDecimal maximumPowerThroughMeter;

    // AC接入类型
    @JsonProperty("aat")
    private Integer acAccessType;

    // 并网系数, 单位: %
    @JsonProperty("gc")
    private Integer gridCoefficient;

    // 开机方式
    @JsonProperty("bm2")
    private Integer bootMode2;

    // 是否启用柴油机控制: 0: false;  1 true
    @JsonProperty("gooc")
    private Boolean generatorControl;

    // 启停模式: 1:SOC, 2:time, 4:manual
    @JsonProperty("rsm")
    private Integer revStopMode;

    // 开启soc, 单位: %
    @JsonProperty("ss")
    private Integer startSoc;

    // 停止soc, 单位: %
    @JsonProperty("ts")
    private Integer terminateSoc;

    // 开启时间(hhmm)
    @JsonProperty("startt")
    private Integer startTime;

    // 停止时间(hhmm)
    @JsonProperty("stopt")
    private Integer stopTime;

    // 输出功率模式: 1:charge power, 2:generator
    @JsonProperty("pom")
    private Integer powerOutputMode;

    // 充电功率, 单位: 0.1kW
    @JsonProperty("cp")
    private BigDecimal chargePower;

    // 柴油机额定功率, 单位: 0.1kW
    @JsonProperty("rpfg")
    private BigDecimal ratedPowerForGenerator;

    // 油机额定输出百分比
    @JsonProperty("omrop")
    private Integer oilMachineRatedOutputPercentage;

    // 并网充电使能: 0: disable,  1 enble
    @JsonProperty("gcce")
    private Boolean gridConnectedChargingEnable;

    // 并网放电使能: 0: disable,  1 enble
    @JsonProperty("gcde")
    private Boolean gridConnectedDischargeEnable;

    // 充电时间段1起始时间(hhmm)
    @JsonProperty("cp1st")
    private Integer chargingPeriod1StartTime;

    // 充电时间段1结束时间(hhmm)
    @JsonProperty("cp1tt")
    private Integer chargingPeriod1TerminateTime;

    // 充电时间段2起始时间(hhmm)
    @JsonProperty("cp2st")
    private Integer chargingPeriod2StartTime;

    // 充电时间段2结束时间(hhmm)
    @JsonProperty("cp2tt")
    private Integer chargingPeriod2TerminateTime;

    // 放电时间段1起始时间(hhmm)
    @JsonProperty("dp1st")
    private Integer dischargePeriod1StartTime;

    // 放电时间段1结束时间(hhmm)
    @JsonProperty("dp1tt")
    private Integer dischargePeriod1TerminateTime;

    // 放电时间段2起始时间(hhmm)
    @JsonProperty("dp2st")
    private Integer dischargePeriod2StartTime;

    // 放电时间段2结束时间(hhmm)
    @JsonProperty("dp2tt")
    private Integer dischargePeriod2TerminateTime;

    // 充电截止 SOC, 单位: %
    @JsonProperty("css")
    private Integer chargingStopSoc;

    // 放电截止 SOC, 单位: %
    @JsonProperty("dss")
    private Integer dischargingStopSoc;

    // 充电时间段充电功率, 单位: 0.1kW
    @JsonProperty("cpdcp")
    private BigDecimal chargingPowerDuringChargingPeriod;

    // AC侧光伏装机容量, 单位: 0.1kW
    @JsonProperty("aspic")
    private BigDecimal acSidePhotovoltaicInstalledCapacity;

    // 储能PCS最大功率, 单位: 0.1kW
    @JsonProperty("espmp")
    private BigDecimal energyStoragePcsMaximumPower;

    // ACDC耦合模式: 1:AC, 2:DC, 3:hybrid
    @JsonProperty("acm")
    private Integer acdcCoupleMode;

    // 电池一个PACK容量, 单位: 0.01kWh
    @JsonProperty("bopc")
    private BigDecimal batteryOnePackCapacity;

    // DC侧光伏装机容量, 单位: 0.1kW
    @JsonProperty("dspic")
    private BigDecimal dcSidePhotovoltaicInstalledCapacity;

    // 干接点1打开关闭
    @JsonProperty("ds1oo")
    private Integer drySpot1;

    // 干接点1控制模式On/Off/Auto
    @JsonProperty("ds1cma")
    private Integer drySpot1ControlMode;

    // 干接点1起始时间1
    @JsonProperty("ds1startt1")
    private Integer drySpot1StartTime1;

    // 干接点1结束时间1
    @JsonProperty("ds1stopt1")
    private Integer drySpot1StopTime1;

    // 干接点1起始时间2
    @JsonProperty("ds1startt2")
    private Integer drySpot1StartTime2;

    // 干接点1结束时间2
    @JsonProperty("ds1stopt2")
    private Integer drySpot1StopTime2;

    // 干接点1一周每天使能位: bit0~6 represent from Monday to Sunday
    @JsonProperty("ds1dep")
    private List<DayOfWeek> drySpot1DailyEnablePosition;

    // 干接点1电池SOC设定阈值: 单位: %
    @JsonProperty("ds1bsst")
    private Integer drySpot1BatterySocSettingThreshold;

    // pack 数据使能: 0:disable 1:enable
    @JsonProperty("pde")
    private Boolean packDataEnable;

    // 设置电池簇ID
    @JsonProperty("stbci")
    private Integer setTheBatteryClusterId;

    // 负载接入SOC: 单位: %
    @JsonProperty("las")
    private Integer loadAccessSoc;

    // 负载切除SOC: 单位: %
    @JsonProperty("lrs")
    private Integer loadRemovalSoc;

    // 云平台型号
    // 1：ALPHA_CLOUD_E,
    // 2：HTESS_CLOUD_E,
    // 3：TGOOD_CLOUD_E,
    // 4：DL_CLOUD_E,
    // 5：TGOOD_EMS_E
    @JsonProperty("cloudm")
    private Integer cloudModel;

    // 系统型号
    // 1：Storion-T30
    // 2：Storion-T50
    // 3：Storion-T100
    // 4：Storion-T150
    // 5：Storion-TB250
    // 6：Storion-TB500
    // 7：Storion-TB100
    // 8：Storion-TB50
    @JsonProperty("sysm")
    private Integer systemModel;

    // 电池型号
    // 14：LMU-M48112-S
    // 25：LMU-M4856-S
    // 37：LMU-M38210-S
    @JsonProperty("btm")
    private Integer batteryModel;

    // PCS型号
    // 0：PWS2_30K_E,
    // 1：PWG2_50K_E,
    // 2：PWG2_100K_E,
    // 3：PWS1_50K_E,
    // 4：PWS1_100K_E,
    // 5：PWS1_150K_E,
    // 6：PWS2_50K_E,
    // 7：PWS2_100K_E,
    // 8：PWS1_250K_E,
    // 9：PWS1_250K_4H_E,
    // 10：PWS1_500K_E,
    // AC on-grid series:
    // 11.BCS50K-A
    // 12.BCS100K-A
    // 13.BCS250K-A
    // 14.BCS500K-A
    // 15.BCS50K-B
    // 16.BCS250K-B
    // 17.BCS500K-B
    // 18.BCS630K-B
    // 19.BCS1250K-B-H
    // 20.BCS1500K-B-H
    // DC on-grid series:
    // 21.SPT250K-H
    // 22.SPT500K-H
    // Off-grid Switchgear Cabinet:
    // //23.BTS-100K-K/S
    // //24.BTS-200K-K/S
    // //25.BTS-500K-K/S
    // //26.BTS-1000K-K/S
    @JsonProperty("pm")
    private Integer pcsModel;

    // 控制策略
    @JsonProperty("cs")
    private Integer controlStrategy;

    // 制冷停止点(制冷开启点＝制冷停止点+制冷回差), 单位: ℃
    @JsonProperty("rsp")
    private BigDecimal refrigerationStoppingPoint;

    // 制冷回差, 单位: ℃
    @JsonProperty("rrd")
    private BigDecimal refrigerationReturnDifference;

    // 加热停止点(加热停止点＝加热开启点+加热回差), 单位: ℃
    @JsonProperty("hsp")
    private BigDecimal heatingStopPoint;

    // 加热回差, 单位: ℃
    @JsonProperty("hrd")
    private BigDecimal heatingReturnDifference;

    // 需量控制使能
    @JsonProperty("dce")
    private Integer demandControlEnable;

    // 最大需量阈值, 单位: kW
    @JsonProperty("demand")
    private Long demand;

    // 变压器容量, 单位: kVA
    @JsonProperty("tc")
    private Long transformerCapacity;

    // EMS默认调节参数使能: 0:disable 1:enable
    @JsonProperty("edape")
    private Boolean emsDefaultAdjustmentParameterEnable;

    // EMS调节步长
    @JsonProperty("eass")
    private Integer emsAdjustmentStepSize;

    // PCS最小调节量, 单位: 0.1kW
    @JsonProperty("maop")
    private BigDecimal minimumAdjustmentOfPcs;

    // 预留开机电流, 单位: 0.1A
    @JsonProperty("rsc")
    private BigDecimal reservedStartingCurrent;

    // PCS系统自耗, 单位: 0.1kW
    @JsonProperty("tpsci")
    private BigDecimal thePcsSystemConsumesItself;

    // 充电时间段3起始时间(hhmm)
    @JsonProperty("cp3st")
    private Integer chargingPeriod3StartTime;

    // 充电时间段3结束时间(hhmm)
    @JsonProperty("cp3tt")
    private Integer chargingPeriod3TerminateTime;

    // 放电时间段3起始时间(hhmm)
    @JsonProperty("dp3st")
    private Integer dischargePeriod3StartTime;

    // 放电时间段3结束时间(hhmm)
    @JsonProperty("dp3tt")
    private Integer dischargePeriod3TerminateTime;

    // 削峰填谷使能
    @JsonProperty("pfe")
    private Integer peakFillEnable;

    // 峰时间段起始时间1(hhmm)
    @JsonProperty("tbotpp1")
    private Integer theBeginningOfThePeakPeriod1;

    // 峰时间段结束时间1(hhmm)
    @JsonProperty("teotpp1")
    private Integer theEndOfThePeakPeriod1;

    // 峰时间段起始时间2(hhmm)
    @JsonProperty("tbotpp2")
    private Integer theBeginningOfThePeakPeriod2;

    // 峰时间段结束时间2(hhmm)
    @JsonProperty("teotpp2")
    private Integer theEndOfThePeakPeriod2;

    // 谷时间段起始时间1(hhmm)
    @JsonProperty("stovp1")
    private Integer startTimeOfValleyPeriod1;

    // 谷时间段结束时间1(hhmm)
    @JsonProperty("eovtp1")
    private Integer endOfValleyTimePeriod1;

    // 谷时间段起始时间2(hhmm)
    @JsonProperty("stovp2")
    private Integer startTimeOfValleyPeriod2;

    // 谷时间段结束时间2(hhmm)
    @JsonProperty("eovtp2")
    private Integer endOfValleyTimePeriod2;

    // 峰时间段功率
    @JsonProperty("ppp")
    private Integer peakPeriodPower;

    // 谷时间段功率
    @JsonProperty("vtp")
    private Integer valleyTimePower;

    // 平滑输出使能
    @JsonProperty("soe")
    private Integer smoothOutputEnable;

    // 监测周期
    @JsonProperty("mp")
    private Integer monitoringPeriod;

    // 变化幅值 在监测周期内的功率最大变化值，或叫波动率
    @JsonProperty("cita")
    private Integer changesInTheAmplitude;

    // 目标的额定功率
    @JsonProperty("tpr")
    private Integer targetPowerRating;

    // 调频控制使能
    @JsonProperty("fce")
    private Integer fmControlEnable;

    // 频率启动偏差
    @JsonProperty("fsd")
    private Integer frequencyStartingDeviation;

    // 调节范围
    @JsonProperty("tso")
    private Integer theScopeOf;

    // 调节步长
    @JsonProperty("sl")
    private Integer stepLength;

    // 充放电功率
    @JsonProperty("cdp")
    private Integer chargeDischargePower;

    // 爬坡时间
    @JsonProperty("ct")
    private Integer climbingTime;

    // 下垂
    @JsonProperty("droop")
    private Integer droop;

    // 死区, 单位: Mhz
    @JsonProperty("dz")
    private Integer deadZone;

    // 负频率响应错误
    @JsonProperty("nfre")
    private Integer negativeFrequencyResponseError;

    // 正频率响应错误
    @JsonProperty("pfre")
    private Integer positiveFrequencyResponseError;

    // 内在的力量死区
    @JsonProperty("isdz")
    private Integer innerStrengthDeadZone;

    // 积累时间, 单位: s
    @JsonProperty("taot")
    private Integer theAccumulationOfTime;

    // 积累门槛, 单位: pu
    @JsonProperty("tat")
    private Integer theAccumulationThreshold;

    // 沉降时间, 单位: s
    @JsonProperty("tst")
    private Integer theSettlingTime;

    // 等待时间, 单位: min
    @JsonProperty("wt")
    private Integer waitingTime;

    // 电源复位坡度, 单位: pu/min
    @JsonProperty("prs")
    private Integer powerResetSlope;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
