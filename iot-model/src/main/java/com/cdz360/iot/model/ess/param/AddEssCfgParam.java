package com.cdz360.iot.model.ess.param;

import com.cdz360.base.model.es.type.EssCfgStrategy;
import com.cdz360.iot.model.ess.type.CfgType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "新增光储配置模板")
@Data
@Accessors(chain = true)

public class AddEssCfgParam {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "继承code")
    private String code;

    @Schema(description = "配置模板版本号")
    private Long ver;

    @Schema(description = "创建者所属商户ID")
    private Long commId;

    @Schema(description = "策略类型: SELF_USE(自发自用); PEAK_VALLEY_ARBITRAGE(峰谷套利); "
        + "PEAK_SHARE(削峰填谷); TIMING_CHARGING_DISCHARGING(定时充放电);"
        + "TIME_SHARING_ELECTRICITY_PRICE(分时电价); DISASTER_SPARE(灾备)")
    private EssCfgStrategy strategy;

    @Schema(description = "采样时间间隔，单位'秒'")
    private Integer samplingTime;

    @Schema(description = "场站ID")
    private String siteId;

    @Schema(description = "模板名称")
    private String name;

    @Schema(description = "类型")
    private CfgType type;

    @Schema(description = "操作人ID")
    private Long opUid;

    @Schema(description = "操作人姓名")
    private String opName;

    @Schema(description = "是否有效 ，true-有效")
    private Boolean enable;

    @Schema(description = "充放电时段参数")
    private EssInOutCfgParam inOutCfg;

    @Schema(description = "削峰填谷")
    private PeakShareCfgParam peakShareCfg;

    @Schema(description = "平滑输出")
    private SmoothOutPutCfgParam smoothOutCfg;

    @Schema(description = "调频控制")
    private EssFmCtrlCfgParam fmCtrlCfg;

    @Schema(description = "储能开关机设置")
    private EssCfgParam essCfg;

    @Schema(description = "柴油机控制")
    private EssGeneratorCfgParam generatorCfg;

    @Schema(description = "干节点设置")
    private EssDrySpotCfgParam drySpotCfg;

    @Schema(description = "商户链")
    private String commIdChain;

}
