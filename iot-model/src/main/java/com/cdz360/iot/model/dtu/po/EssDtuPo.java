package com.cdz360.iot.model.dtu.po;

import com.cdz360.base.model.es.type.hi.EssDtuCommunicationWay;
import com.cdz360.base.model.es.type.hi.EssDtuType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "户储通讯设备信息")
public class EssDtuPo {

    @Schema(description = "设备序列号")
    @NotNull(message = "serialNo 不能为 null")
    @Size(max = 14, message = "serialNo 长度不能超过 14")
    private String serialNo;

    @Schema(description = "网关编号")
    @NotNull(message = "gwno 不能为 null")
    @Size(max = 16, message = "gwno 长度不能超过 16")
    private String gwno;

    @Schema(description = "数据传输单元类型: ETHERNET_GATEWAY(Ethernet Gateway);\r\n  WIFI_GATEWAY(WiFi Gateway);MULTI_GATEWAY(Multi - function Gateway);\r\n  GPRS_GATEWAY(GPRS Gateway);NB_GATEWAY(NB Gateway);\r\n  G3_GATEWAY(3G Gateway);G4_GATEWAY(4G Gateway)")
    private EssDtuType essDtuType;

    @Schema(description = "通讯方式: ETHERNET_COMMUNICATION(Ethernet Communication);\r\n   WIFI_COMMUNICATION(WiFi Communication); GPRS_COMMUNICATION(GPRS Communication); \r\n   SUB_1G(Sub 1G); ZIGBEE(ZigBee); BLUETOOTH(Bluetooth); CAN(CAN); RS485(RS485)")
    private EssDtuCommunicationWay communicationWay;

    @Schema(description = "设备名称")
    @NotNull(message = "deviceName 不能为 null")
    @Size(max = 20, message = "deviceName 长度不能超过 20")
    private String deviceName;

    @Schema(description = "设备型号")
    @NotNull(message = "deviceModel 不能为 null")
    @Size(max = 16, message = "deviceModel 长度不能超过 16")
    private String deviceModel;

    @Schema(description = "硬件版本")
    @NotNull(message = "hardwareVer 不能为 null")
    @Size(max = 16, message = "hardwareVer 长度不能超过 16")
    private String hardwareVer;

    @Schema(description = "软件版本")
    @NotNull(message = "softwareVer 不能为 null")
    @Size(max = 16, message = "softwareVer 长度不能超过 16")
    private String softwareVer;

    @Schema(description = "ICCID")
    @NotNull(message = "iccid 不能为 null")
    @Size(max = 16, message = "iccid 长度不能超过 16")
    private String iccid;

    @Schema(description = "ip地址")
    private String ip;

    @Schema(description = "true有效;false删除")
    private Boolean enable;

    @Schema(description = "创建时间")
    @NotNull(message = "createTime 不能为 null")
    private Date createTime;

    @Schema(description = "更新时间")
    @NotNull(message = "updateTime 不能为 null")
    private Date updateTime;
}

