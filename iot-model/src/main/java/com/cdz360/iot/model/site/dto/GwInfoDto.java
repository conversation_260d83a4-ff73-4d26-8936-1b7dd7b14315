package com.cdz360.iot.model.site.dto;

import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.type.GwStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

@Schema(description = "网关信息")
public class GwInfoDto extends GwInfoPo {
    @Schema(description = "网关唯一ID")
    private Long id;

    @Schema(description = "场站ID")
    private String siteId;

    @Schema(description = "场站所属商户ID")
    private Long siteCommId;

    @Schema(description = "所属场站名字")
    private String siteName;

    @Schema(description = "所在城市名字")
    private String cityName;

    @Schema(description = "经度")
    private Double lon;


    @Schema(description = "纬度")
    private Double lat;

    @Schema(description = "最后更新时间")
    private Date updateTime;

    @Schema(description = "网关状态")
    private GwStatus status;

    public GwStatus getStatus() {
        return status;
    }

    public GwInfoDto setStatus(GwStatus status) {
        this.status = status;
        return this;
    }

    public Long getId() {
        return id;
    }

    public GwInfoDto setId(Long id) {
        this.id = id;
        return this;
    }

    public String getSiteId() {
        return siteId;
    }

    public GwInfoDto setSiteId(String siteId) {
        this.siteId = siteId;
        return this;
    }

    public Long getSiteCommId() {
        return siteCommId;
    }

    public GwInfoDto setSiteCommId(Long siteCommId) {
        this.siteCommId = siteCommId;
        return this;
    }

    public String getSiteName() {
        return siteName;
    }

    public GwInfoDto setSiteName(String siteName) {
        this.siteName = siteName;
        return this;
    }

    public String getCityName() {
        return cityName;
    }

    public GwInfoDto setCityName(String cityName) {
        this.cityName = cityName;
        return this;
    }

    public Double getLon() {
        return lon;
    }

    public GwInfoDto setLon(Double lon) {
        this.lon = lon;
        return this;
    }

    public Double getLat() {
        return lat;
    }

    public GwInfoDto setLat(Double lat) {
        this.lat = lat;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public GwInfoDto setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }
}
