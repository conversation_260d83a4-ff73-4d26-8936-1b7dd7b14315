package com.cdz360.iot.model.meter.vo;

import com.cdz360.base.model.meter.vo.MeterAbcData;
import com.cdz360.base.model.meter.vo.MeterKhwData;
import com.cdz360.base.utils.JsonUtils;
import lombok.Data;

/**
 * 电表完整实时数据
 */
@Data
public class MeterRtData {

    // 电表唯一编号
    private String dno;

    // 电量相关数据
    private MeterKhwData kwh;

    // 上一结算日（上月）电量数据
    private MeterKhwData kwhL1;

    // ABC项电压、电流、功率相关数据
    private MeterAbcData abc;

    // 电压/电流变比
    private MeterTransformationRatio tr;

    // 数据采样时间,unix时间戳
    private Long time;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}