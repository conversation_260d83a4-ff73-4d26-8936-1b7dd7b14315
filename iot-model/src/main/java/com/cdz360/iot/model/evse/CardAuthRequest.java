package com.cdz360.iot.model.evse;

import com.cdz360.iot.model.base.BaseGwRequest;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

public class CardAuthRequest extends BaseGwRequest {


    @Schema(description = "逻辑卡号")
    public String cardNo;

    @Schema(description = "卡密码, 没有密码不传")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public String passcode;

    public String evseId;
    public int plugId;

    public String getCardNo() {
        return cardNo;
    }

    public CardAuthRequest setCardNo(String cardNo) {
        this.cardNo = cardNo;
        return this;
    }

    public String getPasscode() {
        return passcode;
    }

    public CardAuthRequest setPasscode(String passcode) {
        this.passcode = passcode;
        return this;
    }

    public String getEvseId() {
        return evseId;
    }

    public CardAuthRequest setEvseId(String evseId) {
        this.evseId = evseId;
        return this;
    }

    public int getPlugId() {
        return plugId;
    }

    public CardAuthRequest setPlugId(int plugId) {
        this.plugId = plugId;
        return this;
    }
}
