package com.cdz360.iot.model.auth;

import io.swagger.v3.oas.annotations.media.Schema;

public class CusAuthRes extends CusAuthResBase {

    private Integer defaultPayType;
    private Long payAccountId;
    private Long userId;

    @Schema(description = "是否为后付费标识: true/false")
    private Boolean postPaid; // 是否为后付费标识: true/false

    public Integer getDefaultPayType() {
        return defaultPayType;
    }

    public CusAuthRes setDefaultPayType(Integer defaultPayType) {
        this.defaultPayType = defaultPayType;
        return this;
    }

    public Long getPayAccountId() {
        return payAccountId;
    }

    public CusAuthRes setPayAccountId(Long payAccountId) {
        this.payAccountId = payAccountId;
        return this;
    }

    public Long getUserId() {
        return userId;
    }

    public CusAuthRes setUserId(Long userId) {
        this.userId = userId;
        return this;
    }

    public Boolean getPostPaid() {
        return postPaid;
    }

    public CusAuthRes setPostPaid(Boolean postPaid) {
        this.postPaid = postPaid;
        return this;
    }
}
