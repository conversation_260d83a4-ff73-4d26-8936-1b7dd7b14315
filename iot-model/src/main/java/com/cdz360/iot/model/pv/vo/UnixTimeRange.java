package com.cdz360.iot.model.pv.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "UNIX时间范围信息(左闭右闭)")
@Data
@Accessors(chain = true)
public class UnixTimeRange {

    @Schema(description = "开始时间")
    @JsonInclude(Include.NON_NULL)
    private Long from;

    @Schema(description = "结束时间(为null或==from值表示查询指定时间点)")
    @JsonInclude(Include.NON_NULL)
    private Long to;
}
