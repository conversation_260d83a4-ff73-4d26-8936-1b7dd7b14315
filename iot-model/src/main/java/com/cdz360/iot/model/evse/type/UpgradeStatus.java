package com.cdz360.iot.model.evse.type;

import lombok.Getter;

@Getter
public enum UpgradeStatus {

    CMD_SEND(10, "更新指令已发送"),
    CMD_RECEIVE(30, "更新指令已被接收"),
    UPGRADE_SUCCESS(20, "更新成功"),
    UPGRADE_TIMEOUT(40, "更新超时"),
    UPGRADE_FAIL(50, "更新失败")
    ;

    private int code;
    private String desc;

    UpgradeStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
