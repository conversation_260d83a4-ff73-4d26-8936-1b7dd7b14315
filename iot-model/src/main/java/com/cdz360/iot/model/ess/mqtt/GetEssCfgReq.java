package com.cdz360.iot.model.ess.mqtt;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.model.es.type.EssConfigType;
import com.cdz360.base.model.es.vo.RegisterRwValue;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.site.mqtt.BaseMqttMsg;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class GetEssCfgReq {

    @Schema(description = "设备编号")
    private String dno;

    @Schema(description = "需要获取的设备序列号,为空不处理(户储使用)")
    private String serialNo;

    @Schema(description = "获取目标配置")
    private List<EssConfigType> types;

    //    ========读取动态配置字段使用=======
    @Schema(description = "读取配置类别")
    @JsonInclude(Include.NON_NULL)
    private String category;

    @Schema(description = "指定需要读取配置项")
    @JsonInclude(Include.NON_NULL)
    private List<RegisterRwValue> tvs;
    //    ========读取动态配置字段使用=======

    public static class REQ extends BaseMqttMsg<GetEssCfgReq> {

        @JsonProperty("c")
        @Override
        public IotGwCmdType2 getCmd() {
            return IotGwCmdType2.GET_ESS_CFG;
        }

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }

    public static class builder {

        private REQ req;

        public builder(String gwno, String seq) {
            this.req = new REQ();
            this.req.setGwno(gwno);
            this.req.setData(new GetEssCfgReq());
            this.req.setSeq(seq);
        }

        public GetEssCfgReq.builder dno(String dno) {
            this.req.getData().setDno(dno);
            return this;
        }

        public GetEssCfgReq.builder serialNo(String serialNo) {
            this.req.getData().setSerialNo(serialNo);
            return this;
        }

        public GetEssCfgReq.builder types(List<EssConfigType> types) {
            this.req.getData().setTypes(types);
            return this;
        }

        public GetEssCfgReq.builder category(String category) {
            this.req.getData().setCategory(category);
            return this;
        }

        public GetEssCfgReq.builder tvs(List<RegisterRwValue> tvs) {
            this.req.getData().setTvs(tvs);
            return this;
        }

        public REQ build() {
            return this.req;
        }
    }

}
