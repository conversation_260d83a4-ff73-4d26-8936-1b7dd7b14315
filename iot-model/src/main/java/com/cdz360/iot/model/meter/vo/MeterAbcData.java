package com.cdz360.iot.model.meter.vo;

//import lombok.Data;
//
///**
// * 电表ABC相实时数据
// */
//@Data
//public class MeterAbcData {
//    // ABC相电压, 已乘变比
//    private MeterAbcItem voltage;
//
//    // ABC相电流, 已乘变比
//    private MeterAbcItem current;
//
//    // 有功总功率(总,A,B,C), 已乘变比
//    private MeterAbcItem activePower;
//
//    // 无功功率(总,A,B,C), 已乘变比
//    private MeterAbcItem reactivePower;
//
//    // 视在功率(总,A,B,C), 已乘变比
//    private MeterAbcItem apparentPower;
//
//    // 功率因数(总,A,B,C)
//    private MeterAbcItem pf;
//}
