package com.cdz360.iot.model.pv.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "光伏站控制器/逆变器数量信息")
@Data
@Accessors(chain = true)
public class CntCtrlGtiDto {
    @Schema(description = "光伏站控制器数量")
    private int ctrlNum;

    @Schema(description = "逆变器数量")
    private int gtiNum;

    @Schema(description = "光储ESS数量")
    private int essNum;
}
