package com.cdz360.iot.model.park.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "停车系统优惠券-PP停车")
public class ParkCouponPpVo {
    @JsonProperty(value = "app_id")
    private String appId; // 平台分配的接入应用ID

    @JsonProperty(value = "sign")
    private String sign;

    @JsonProperty(value = "merchant")
    private String merchant;

    @JsonProperty(value = "store_code")
    private String storeCode;

    @JsonProperty(value = "coupon_code")
    private String couponCode;

    @JsonProperty(value = "quantity")
    private String quantity;

//    @JsonProperty(value = "parking_serial")
//    private String parkingSerial;

    @JsonProperty(value = "plate")
    private String plate;

    @JsonProperty(value = "reason")
    private String reason;

    @Data
    public static class Response {

        @Schema(description = "返回状态（1001 派发成功，其他失败）")
        private int code;

        @Schema(description = "返回描述")
        private String message;

        @Schema(description = "返回错误说明")
        private String hint;

        @Schema(description = "服务器日志标示")
        private String seqno;
    }
}