package com.cdz360.iot.model.pv.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname PvRtDataPowerProfit
 * @Description
 * @Date 10/28/2021 4:13 PM
 * @Created by Rafael
 */
@Schema(description = "发电数据")
@Data
@Accessors(chain = true)
public class PvRtDataPowerProfit {
    @Schema(description = "日期")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime time;

    @Schema(description = "发电电量（度）")
    private BigDecimal totalKwh;

    @Schema(description = "发电收益（元）")
    private BigDecimal totalProfit;
}