package com.cdz360.iot.model.evse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @Classname SiteAndPlugBiVoEx
 * @Description
 * @Date 6/23/2020 2:12 PM
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
@Schema(description = "首页-充电站和枪头监控信息Ex")
public class SiteAndPlugBiVoEx extends SiteAndPlugBiVo {

    @Schema(description = "充电站数量")
    private Long commId;

}