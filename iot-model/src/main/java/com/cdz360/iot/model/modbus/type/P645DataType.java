package com.cdz360.iot.model.modbus.type;

import com.cdz360.base.model.base.type.DataType;
import com.cdz360.base.utils.ByteUtils;
import java.util.Arrays;
import lombok.Getter;

@Getter
public enum P645DataType {
    UNKNOWN(new byte[]{(byte) 0xFF, (byte) 0xFF, (byte) 0xFF, (byte) 0xFF}, "未知",
        DataType.OTHER),

    MONITOR(new byte[]{0x00, 0x00, 0x00, 0x00}, "报文监听",
        DataType.OTHER),

    COMM_ADDRESS(new byte[]{0x01, (byte) 0x04, 0x00, 0x04}, "通信地址",
        DataType.OTHER),

    METER_NO(new byte[]{0x02, (byte) 0x04, 0x00, 0x04}, "电表表号",
        DataType.OTHER),

    MODEL(new byte[]{0x0B, (byte) 0x04, 0x00, 0x04}, "电表型号（ASCII 码）",
        DataType.OTHER),

    POWER_COMBINED(new byte[]{0x00, (byte) 0x00, 0x00, 0x00}, "(当前)组合有功总电能",
        DataType.KWH),
    POWER_COMBINED_01(new byte[]{0x00, (byte) 0x01, 0x00, 0x00}, "(当前)组合有功费率1电能",
        DataType.KWH),
    POWER_COMBINED_02(new byte[]{0x00, (byte) 0x02, 0x00, 0x00}, "(当前)组合有功费率2电能",
        DataType.KWH),
    POWER_COMBINED_03(new byte[]{0x00, (byte) 0x03, 0x00, 0x00}, "(当前)组合有功费率3电能",
        DataType.KWH),
    POWER_COMBINED_04(new byte[]{0x00, (byte) 0x04, 0x00, 0x00}, "(当前)组合有功费率4电能",
        DataType.KWH),
    POWER_COMBINED_05(new byte[]{0x00, (byte) 0x05, 0x00, 0x00}, "(当前)组合有功费率5电能",
        DataType.KWH),
    POWER_COMBINED_06(new byte[]{0x00, (byte) 0x06, 0x00, 0x00}, "(当前)组合有功费率6电能",
        DataType.KWH),
    POWER_COMBINED_07(new byte[]{0x00, (byte) 0x07, 0x00, 0x00}, "(当前)组合有功费率7电能",
        DataType.KWH),
    POWER_COMBINED_08(new byte[]{0x00, (byte) 0x08, 0x00, 0x00}, "(当前)组合有功费率8电能",
        DataType.KWH),
    POWER_COMBINED_09(new byte[]{0x00, (byte) 0x09, 0x00, 0x00}, "(当前)组合有功费率9电能",
        DataType.KWH),
    POWER_COMBINED_10(new byte[]{0x00, (byte) 0x0A, 0x00, 0x00}, "(当前)组合有功费率10电能",
        DataType.KWH),
    POWER_COMBINED_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x00, 0x00}, "(当前)组合有功电能数据块",
        DataType.KWH),

    POWER_POSITIVE(new byte[]{0x00, (byte) 0x00, 0x01, 0x00}, "(当前)正向有功总电能",
        DataType.KWH),
    POWER_POSITIVE_01(new byte[]{0x00, (byte) 0x01, 0x01, 0x00}, "(当前)正向有功费率1电能",
        DataType.KWH),
    POWER_POSITIVE_02(new byte[]{0x00, (byte) 0x02, 0x01, 0x00}, "(当前)正向有功费率2电能",
        DataType.KWH),
    POWER_POSITIVE_03(new byte[]{0x00, (byte) 0x03, 0x01, 0x00}, "(当前)正向有功费率3电能",
        DataType.KWH),
    POWER_POSITIVE_04(new byte[]{0x00, (byte) 0x04, 0x01, 0x00}, "(当前)正向有功费率4电能",
        DataType.KWH),
    POWER_POSITIVE_05(new byte[]{0x00, (byte) 0x05, 0x01, 0x00}, "(当前)正向有功费率5电能",
        DataType.KWH),
    POWER_POSITIVE_06(new byte[]{0x00, (byte) 0x06, 0x01, 0x00}, "(当前)正向有功费率6电能",
        DataType.KWH),
    POWER_POSITIVE_07(new byte[]{0x00, (byte) 0x07, 0x01, 0x00}, "(当前)正向有功费率7电能",
        DataType.KWH),
    POWER_POSITIVE_08(new byte[]{0x00, (byte) 0x08, 0x01, 0x00}, "(当前)正向有功费率8电能",
        DataType.KWH),
    POWER_POSITIVE_09(new byte[]{0x00, (byte) 0x09, 0x01, 0x00}, "(当前)正向有功费率9电能",
        DataType.KWH),
    POWER_POSITIVE_10(new byte[]{0x00, (byte) 0x0A, 0x01, 0x00}, "(当前)正向有功费率10电能",
        DataType.KWH),
    POWER_POSITIVE_11(new byte[]{0x00, (byte) 0x0B, 0x01, 0x00}, "(当前)正向有功费率11电能",
        DataType.KWH),
    POWER_POSITIVE_12(new byte[]{0x00, (byte) 0x0C, 0x01, 0x00}, "(当前)正向有功费率12电能",
        DataType.KWH),
    POWER_POSITIVE_13(new byte[]{0x00, (byte) 0x0D, 0x01, 0x00}, "(当前)正向有功费率13电能",
        DataType.KWH),
    POWER_POSITIVE_14(new byte[]{0x00, (byte) 0x0E, 0x01, 0x00}, "(当前)正向有功费率14电能",
        DataType.KWH),
    POWER_POSITIVE_15(new byte[]{0x00, (byte) 0x0F, 0x01, 0x00}, "(当前)正向有功费率15电能",
        DataType.KWH),
    POWER_POSITIVE_16(new byte[]{0x00, (byte) 0x10, 0x01, 0x00}, "(当前)正向有功费率16电能",
        DataType.KWH),
    POWER_POSITIVE_17(new byte[]{0x00, (byte) 0x11, 0x01, 0x00}, "(当前)正向有功费率17电能",
        DataType.KWH),
    POWER_POSITIVE_18(new byte[]{0x00, (byte) 0x12, 0x01, 0x00}, "(当前)正向有功费率18电能",
        DataType.KWH),
    POWER_POSITIVE_19(new byte[]{0x00, (byte) 0x13, 0x01, 0x00}, "(当前)正向有功费率19电能",
        DataType.KWH),
    POWER_POSITIVE_20(new byte[]{0x00, (byte) 0x14, 0x01, 0x00}, "(当前)正向有功费率20电能",
        DataType.KWH),
    POWER_POSITIVE_21(new byte[]{0x00, (byte) 0x15, 0x01, 0x00}, "(当前)正向有功费率21电能",
        DataType.KWH),
    POWER_POSITIVE_22(new byte[]{0x00, (byte) 0x16, 0x01, 0x00}, "(当前)正向有功费率22电能",
        DataType.KWH),
    POWER_POSITIVE_23(new byte[]{0x00, (byte) 0x17, 0x01, 0x00}, "(当前)正向有功费率23电能",
        DataType.KWH),
    POWER_POSITIVE_24(new byte[]{0x00, (byte) 0x18, 0x01, 0x00}, "(当前)正向有功费率24电能",
        DataType.KWH),
    POWER_POSITIVE_25(new byte[]{0x00, (byte) 0x19, 0x01, 0x00}, "(当前)正向有功费率25电能",
        DataType.KWH),
    POWER_POSITIVE_26(new byte[]{0x00, (byte) 0x1A, 0x01, 0x00}, "(当前)正向有功费率26电能",
        DataType.KWH),
    POWER_POSITIVE_27(new byte[]{0x00, (byte) 0x1B, 0x01, 0x00}, "(当前)正向有功费率27电能",
        DataType.KWH),
    POWER_POSITIVE_28(new byte[]{0x00, (byte) 0x1C, 0x01, 0x00}, "(当前)正向有功费率28电能",
        DataType.KWH),
    POWER_POSITIVE_29(new byte[]{0x00, (byte) 0x1D, 0x01, 0x00}, "(当前)正向有功费率29电能",
        DataType.KWH),
    POWER_POSITIVE_30(new byte[]{0x00, (byte) 0x1E, 0x01, 0x00}, "(当前)正向有功费率30电能",
        DataType.KWH),
    POWER_POSITIVE_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x01, 0x00}, "(当前)正向有功电能数据块",
        DataType.KWH),

    POWER_NEGATIVE(new byte[]{0x00, (byte) 0x00, 0x02, 0x00}, "(当前)反向有功总电能",
        DataType.KWH),
    POWER_NEGATIVE_01(new byte[]{0x00, (byte) 0x01, 0x02, 0x00}, "(当前)反向有功费率1电能",
        DataType.KWH),
    POWER_NEGATIVE_02(new byte[]{0x00, (byte) 0x02, 0x02, 0x00}, "(当前)反向有功费率2电能",
        DataType.KWH),
    POWER_NEGATIVE_03(new byte[]{0x00, (byte) 0x03, 0x02, 0x00}, "(当前)反向有功费率3电能",
        DataType.KWH),
    POWER_NEGATIVE_04(new byte[]{0x00, (byte) 0x04, 0x02, 0x00}, "(当前)反向有功费率4电能",
        DataType.KWH),
    POWER_NEGATIVE_05(new byte[]{0x00, (byte) 0x05, 0x02, 0x00}, "(当前)反向有功费率5电能",
        DataType.KWH),
    POWER_NEGATIVE_06(new byte[]{0x00, (byte) 0x06, 0x02, 0x00}, "(当前)反向有功费率6电能",
        DataType.KWH),
    POWER_NEGATIVE_07(new byte[]{0x00, (byte) 0x07, 0x02, 0x00}, "(当前)反向有功费率7电能",
        DataType.KWH),
    POWER_NEGATIVE_08(new byte[]{0x00, (byte) 0x08, 0x02, 0x00}, "(当前)反向有功费率8电能",
        DataType.KWH),
    POWER_NEGATIVE_09(new byte[]{0x00, (byte) 0x09, 0x02, 0x00}, "(当前)反向有功费率9电能",
        DataType.KWH),
    POWER_NEGATIVE_10(new byte[]{0x00, (byte) 0x10, 0x02, 0x00}, "(当前)反向有功费率10电能",
        DataType.KWH),
    POWER_NEGATIVE_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x02, 0x00}, "(当前)反向有功电能数据块",
        DataType.KWH),

    POWER_COMBINED_REACTIVE_1(new byte[]{0x00, (byte) 0x00, 0x03, 0x00}, "组合无功1总电能",
        DataType.KWH),

    POWER_COMBINED_REACTIVE_1_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x03, 0x00},
        "组合无功1电能数据块",
        DataType.KWH),

    POWER_COMBINED_REACTIVE_2(new byte[]{0x00, (byte) 0x00, 0x04, 0x00}, "组合无功2总电能",
        DataType.KWH),

    POWER_COMBINED_REACTIVE_2_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x04, 0x00},
        "组合无功2电能数据块",
        DataType.KWH),

    POWER_QUADRANT_REACTIVE_1_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x05, 0x00},
        "第一象限无功电能数据块",
        DataType.KWH),

    POWER_QUADRANT_REACTIVE_2_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x06, 0x00},
        "第二象限无功电能数据块",
        DataType.KWH),

    POWER_QUADRANT_REACTIVE_3_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x07, 0x00},
        "第三象限无功电能数据块",
        DataType.KWH),

    POWER_QUADRANT_REACTIVE_4_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x08, 0x00},
        "第四象限无功电能数据块",
        DataType.KWH),

    POWER_POSITIVE_APPARENT(new byte[]{0x00, (byte) 0x00, 0x09, 0x00}, "正向视在总电能",
        DataType.KWH),

    POWER_POSITIVE_APPARENT_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x09, 0x00}, "正向视在电能数据块",
        DataType.KWH),

    POWER_NEGATIVE_APPARENT(new byte[]{0x00, (byte) 0x00, 0x0A, 0x00}, "反向视在总电能",
        DataType.KWH),

    POWER_NEGATIVE_APPARENT_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x0A, 0x00}, "反向视在电能数据块",
        DataType.KWH),

    POWER_POSITIVE_DAY01(new byte[]{0x01, (byte) 0x00, 0x01, 0x00}, "(上1结算日)正向有功总电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_01(new byte[]{0x01, (byte) 0x01, 0x01, 0x00},
        "(上1结算日)正向有功费率1电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_02(new byte[]{0x01, (byte) 0x02, 0x01, 0x00},
        "(上1结算日)正向有功费率2电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_03(new byte[]{0x01, (byte) 0x03, 0x01, 0x00},
        "(上1结算日)正向有功费率3电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_04(new byte[]{0x01, (byte) 0x04, 0x01, 0x00},
        "(上1结算日)正向有功费率4电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_05(new byte[]{0x01, (byte) 0x05, 0x01, 0x00},
        "(上1结算日)正向有功费率5电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_06(new byte[]{0x01, (byte) 0x06, 0x01, 0x00},
        "(上1结算日)正向有功费率6电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_07(new byte[]{0x01, (byte) 0x07, 0x01, 0x00},
        "(上1结算日)正向有功费率7电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_08(new byte[]{0x01, (byte) 0x08, 0x01, 0x00},
        "(上1结算日)正向有功费率8电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_09(new byte[]{0x01, (byte) 0x09, 0x01, 0x00},
        "(上1结算日)正向有功费率9电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_10(new byte[]{0x01, (byte) 0x0A, 0x01, 0x00},
        "(上1结算日)正向有功费率10电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_11(new byte[]{0x01, (byte) 0x0B, 0x01, 0x00},
        "(上1结算日)正向有功费率11电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_12(new byte[]{0x01, (byte) 0x0C, 0x01, 0x00},
        "(上1结算日)正向有功费率12电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_13(new byte[]{0x01, (byte) 0x0D, 0x01, 0x00},
        "(上1结算日)正向有功费率13电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_14(new byte[]{0x01, (byte) 0x0E, 0x01, 0x00},
        "(上1结算日)正向有功费率14电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_15(new byte[]{0x01, (byte) 0x0F, 0x01, 0x00},
        "(上1结算日)正向有功费率15电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_16(new byte[]{0x01, (byte) 0x10, 0x01, 0x00},
        "(上1结算日)正向有功费率16电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_17(new byte[]{0x01, (byte) 0x11, 0x01, 0x00},
        "(上1结算日)正向有功费率17电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_18(new byte[]{0x01, (byte) 0x12, 0x01, 0x00},
        "(上1结算日)正向有功费率18电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_19(new byte[]{0x01, (byte) 0x13, 0x01, 0x00},
        "(上1结算日)正向有功费率19电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_20(new byte[]{0x01, (byte) 0x14, 0x01, 0x00},
        "(上1结算日)正向有功费率20电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_21(new byte[]{0x01, (byte) 0x15, 0x01, 0x00},
        "(上1结算日)正向有功费率21电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_22(new byte[]{0x01, (byte) 0x16, 0x01, 0x00},
        "(上1结算日)正向有功费率22电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_23(new byte[]{0x01, (byte) 0x17, 0x01, 0x00},
        "(上1结算日)正向有功费率23电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_24(new byte[]{0x01, (byte) 0x18, 0x01, 0x00},
        "(上1结算日)正向有功费率24电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_25(new byte[]{0x01, (byte) 0x19, 0x01, 0x00},
        "(上1结算日)正向有功费率25电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_26(new byte[]{0x01, (byte) 0x1A, 0x01, 0x00},
        "(上1结算日)正向有功费率26电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_27(new byte[]{0x01, (byte) 0x1B, 0x01, 0x00},
        "(上1结算日)正向有功费率27电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_28(new byte[]{0x01, (byte) 0x1C, 0x01, 0x00},
        "(上1结算日)正向有功费率28电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_29(new byte[]{0x01, (byte) 0x1D, 0x01, 0x00},
        "(上1结算日)正向有功费率29电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_30(new byte[]{0x01, (byte) 0x1E, 0x01, 0x00},
        "(上1结算日)正向有功费率30电能",
        DataType.KWH),
    POWER_POSITIVE_DAY01_BLOCK(new byte[]{0x01, (byte) 0xFF, 0x01, 0x00},
        "(上1结算日)正向有功电能数据块",
        DataType.KWH),

    POWER_POSITIVE_DAY02(new byte[]{0x02, (byte) 0x00, 0x01, 0x00}, "(上2结算日)正向有功总电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_01(new byte[]{0x02, (byte) 0x01, 0x01, 0x00},
        "(上2结算日)正向有功费率1电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_02(new byte[]{0x02, (byte) 0x02, 0x01, 0x00},
        "(上2结算日)正向有功费率2电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_03(new byte[]{0x02, (byte) 0x03, 0x01, 0x00},
        "(上2结算日)正向有功费率3电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_04(new byte[]{0x02, (byte) 0x04, 0x01, 0x00},
        "(上2结算日)正向有功费率4电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_05(new byte[]{0x02, (byte) 0x05, 0x01, 0x00},
        "(上2结算日)正向有功费率5电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_06(new byte[]{0x02, (byte) 0x06, 0x01, 0x00},
        "(上2结算日)正向有功费率6电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_07(new byte[]{0x02, (byte) 0x07, 0x01, 0x00},
        "(上2结算日)正向有功费率7电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_08(new byte[]{0x02, (byte) 0x08, 0x01, 0x00},
        "(上2结算日)正向有功费率8电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_09(new byte[]{0x02, (byte) 0x09, 0x01, 0x00},
        "(上2结算日)正向有功费率9电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_10(new byte[]{0x02, (byte) 0x0A, 0x01, 0x00},
        "(上2结算日)正向有功费率10电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_11(new byte[]{0x02, (byte) 0x0B, 0x01, 0x00},
        "(上2结算日)正向有功费率11电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_12(new byte[]{0x02, (byte) 0x0C, 0x01, 0x00},
        "(上2结算日)正向有功费率12电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_13(new byte[]{0x02, (byte) 0x0D, 0x01, 0x00},
        "(上2结算日)正向有功费率13电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_14(new byte[]{0x02, (byte) 0x0E, 0x01, 0x00},
        "(上2结算日)正向有功费率14电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_15(new byte[]{0x02, (byte) 0x0F, 0x01, 0x00},
        "(上2结算日)正向有功费率15电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_16(new byte[]{0x02, (byte) 0x10, 0x01, 0x00},
        "(上2结算日)正向有功费率16电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_17(new byte[]{0x02, (byte) 0x11, 0x01, 0x00},
        "(上2结算日)正向有功费率17电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_18(new byte[]{0x02, (byte) 0x12, 0x01, 0x00},
        "(上2结算日)正向有功费率18电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_19(new byte[]{0x02, (byte) 0x13, 0x01, 0x00},
        "(上2结算日)正向有功费率19电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_20(new byte[]{0x02, (byte) 0x14, 0x01, 0x00},
        "(上2结算日)正向有功费率20电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_21(new byte[]{0x02, (byte) 0x15, 0x01, 0x00},
        "(上2结算日)正向有功费率21电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_22(new byte[]{0x02, (byte) 0x16, 0x01, 0x00},
        "(上2结算日)正向有功费率22电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_23(new byte[]{0x02, (byte) 0x17, 0x01, 0x00},
        "(上2结算日)正向有功费率23电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_24(new byte[]{0x02, (byte) 0x18, 0x01, 0x00},
        "(上2结算日)正向有功费率24电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_25(new byte[]{0x02, (byte) 0x19, 0x01, 0x00},
        "(上2结算日)正向有功费率25电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_26(new byte[]{0x02, (byte) 0x1A, 0x01, 0x00},
        "(上2结算日)正向有功费率26电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_27(new byte[]{0x02, (byte) 0x1B, 0x01, 0x00},
        "(上2结算日)正向有功费率27电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_28(new byte[]{0x02, (byte) 0x1C, 0x01, 0x00},
        "(上2结算日)正向有功费率28电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_29(new byte[]{0x02, (byte) 0x1D, 0x01, 0x00},
        "(上2结算日)正向有功费率29电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_30(new byte[]{0x02, (byte) 0x1E, 0x01, 0x00},
        "(上2结算日)正向有功费率30电能",
        DataType.KWH),
    POWER_POSITIVE_DAY02_BLOCK(new byte[]{0x02, (byte) 0xFF, 0x01, 0x00},
        "(上2结算日)正向有功电能数据块",
        DataType.KWH),

    POWER_POSITIVE_DAY03(new byte[]{0x03, (byte) 0x00, 0x01, 0x00}, "(上3结算日)正向有功总电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_01(new byte[]{0x03, (byte) 0x01, 0x01, 0x00},
        "(上3结算日)正向有功费率1电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_02(new byte[]{0x03, (byte) 0x02, 0x01, 0x00},
        "(上3结算日)正向有功费率2电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_03(new byte[]{0x03, (byte) 0x03, 0x01, 0x00},
        "(上3结算日)正向有功费率3电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_04(new byte[]{0x03, (byte) 0x04, 0x01, 0x00},
        "(上3结算日)正向有功费率4电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_05(new byte[]{0x03, (byte) 0x05, 0x01, 0x00},
        "(上3结算日)正向有功费率5电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_06(new byte[]{0x03, (byte) 0x06, 0x01, 0x00},
        "(上3结算日)正向有功费率6电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_07(new byte[]{0x03, (byte) 0x07, 0x01, 0x00},
        "(上3结算日)正向有功费率7电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_08(new byte[]{0x03, (byte) 0x08, 0x01, 0x00},
        "(上3结算日)正向有功费率8电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_09(new byte[]{0x03, (byte) 0x09, 0x01, 0x00},
        "(上3结算日)正向有功费率9电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_10(new byte[]{0x03, (byte) 0x0A, 0x01, 0x00},
        "(上3结算日)正向有功费率10电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_11(new byte[]{0x03, (byte) 0x0B, 0x01, 0x00},
        "(上3结算日)正向有功费率11电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_12(new byte[]{0x03, (byte) 0x0C, 0x01, 0x00},
        "(上3结算日)正向有功费率12电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_13(new byte[]{0x03, (byte) 0x0D, 0x01, 0x00},
        "(上3结算日)正向有功费率13电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_14(new byte[]{0x03, (byte) 0x0E, 0x01, 0x00},
        "(上3结算日)正向有功费率14电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_15(new byte[]{0x03, (byte) 0x0F, 0x01, 0x00},
        "(上3结算日)正向有功费率15电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_16(new byte[]{0x03, (byte) 0x10, 0x01, 0x00},
        "(上3结算日)正向有功费率16电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_17(new byte[]{0x03, (byte) 0x11, 0x01, 0x00},
        "(上3结算日)正向有功费率17电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_18(new byte[]{0x03, (byte) 0x12, 0x01, 0x00},
        "(上3结算日)正向有功费率18电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_19(new byte[]{0x03, (byte) 0x13, 0x01, 0x00},
        "(上3结算日)正向有功费率19电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_20(new byte[]{0x03, (byte) 0x14, 0x01, 0x00},
        "(上3结算日)正向有功费率20电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_21(new byte[]{0x03, (byte) 0x15, 0x01, 0x00},
        "(上3结算日)正向有功费率21电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_22(new byte[]{0x03, (byte) 0x16, 0x01, 0x00},
        "(上3结算日)正向有功费率22电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_23(new byte[]{0x03, (byte) 0x17, 0x01, 0x00},
        "(上3结算日)正向有功费率23电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_24(new byte[]{0x03, (byte) 0x18, 0x01, 0x00},
        "(上3结算日)正向有功费率24电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_25(new byte[]{0x03, (byte) 0x19, 0x01, 0x00},
        "(上3结算日)正向有功费率25电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_26(new byte[]{0x03, (byte) 0x1A, 0x01, 0x00},
        "(上3结算日)正向有功费率26电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_27(new byte[]{0x03, (byte) 0x1B, 0x01, 0x00},
        "(上3结算日)正向有功费率27电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_28(new byte[]{0x03, (byte) 0x1C, 0x01, 0x00},
        "(上3结算日)正向有功费率28电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_29(new byte[]{0x03, (byte) 0x1D, 0x01, 0x00},
        "(上3结算日)正向有功费率29电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_30(new byte[]{0x03, (byte) 0x1E, 0x01, 0x00},
        "(上3结算日)正向有功费率30电能",
        DataType.KWH),
    POWER_POSITIVE_DAY03_BLOCK(new byte[]{0x03, (byte) 0xFF, 0x01, 0x00},
        "(上3结算日)正向有功电能数据块",
        DataType.KWH),
    POWER_POSITIVE_DAY04(new byte[]{0x04, (byte) 0x00, 0x01, 0x00}, "(上4结算日)正向有功总电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_01(new byte[]{0x04, (byte) 0x01, 0x01, 0x00},
        "(上4结算日)正向有功费率1电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_02(new byte[]{0x04, (byte) 0x02, 0x01, 0x00},
        "(上4结算日)正向有功费率2电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_03(new byte[]{0x04, (byte) 0x03, 0x01, 0x00},
        "(上4结算日)正向有功费率3电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_04(new byte[]{0x04, (byte) 0x04, 0x01, 0x00},
        "(上4结算日)正向有功费率4电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_05(new byte[]{0x04, (byte) 0x05, 0x01, 0x00},
        "(上4结算日)正向有功费率5电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_06(new byte[]{0x04, (byte) 0x06, 0x01, 0x00},
        "(上4结算日)正向有功费率6电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_07(new byte[]{0x04, (byte) 0x07, 0x01, 0x00},
        "(上4结算日)正向有功费率7电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_08(new byte[]{0x04, (byte) 0x08, 0x01, 0x00},
        "(上4结算日)正向有功费率8电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_09(new byte[]{0x04, (byte) 0x09, 0x01, 0x00},
        "(上4结算日)正向有功费率9电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_10(new byte[]{0x04, (byte) 0x0A, 0x01, 0x00},
        "(上4结算日)正向有功费率10电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_11(new byte[]{0x04, (byte) 0x0B, 0x01, 0x00},
        "(上4结算日)正向有功费率11电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_12(new byte[]{0x04, (byte) 0x0C, 0x01, 0x00},
        "(上4结算日)正向有功费率12电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_13(new byte[]{0x04, (byte) 0x0D, 0x01, 0x00},
        "(上4结算日)正向有功费率13电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_14(new byte[]{0x04, (byte) 0x0E, 0x01, 0x00},
        "(上4结算日)正向有功费率14电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_15(new byte[]{0x04, (byte) 0x0F, 0x01, 0x00},
        "(上4结算日)正向有功费率15电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_16(new byte[]{0x04, (byte) 0x10, 0x01, 0x00},
        "(上4结算日)正向有功费率16电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_17(new byte[]{0x04, (byte) 0x11, 0x01, 0x00},
        "(上4结算日)正向有功费率17电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_18(new byte[]{0x04, (byte) 0x12, 0x01, 0x00},
        "(上4结算日)正向有功费率18电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_19(new byte[]{0x04, (byte) 0x13, 0x01, 0x00},
        "(上4结算日)正向有功费率19电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_20(new byte[]{0x04, (byte) 0x14, 0x01, 0x00},
        "(上4结算日)正向有功费率20电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_21(new byte[]{0x04, (byte) 0x15, 0x01, 0x00},
        "(上4结算日)正向有功费率21电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_22(new byte[]{0x04, (byte) 0x16, 0x01, 0x00},
        "(上4结算日)正向有功费率22电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_23(new byte[]{0x04, (byte) 0x17, 0x01, 0x00},
        "(上4结算日)正向有功费率23电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_24(new byte[]{0x04, (byte) 0x18, 0x01, 0x00},
        "(上4结算日)正向有功费率24电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_25(new byte[]{0x04, (byte) 0x19, 0x01, 0x00},
        "(上4结算日)正向有功费率25电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_26(new byte[]{0x04, (byte) 0x1A, 0x01, 0x00},
        "(上4结算日)正向有功费率26电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_27(new byte[]{0x04, (byte) 0x1B, 0x01, 0x00},
        "(上4结算日)正向有功费率27电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_28(new byte[]{0x04, (byte) 0x1C, 0x01, 0x00},
        "(上4结算日)正向有功费率28电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_29(new byte[]{0x04, (byte) 0x1D, 0x01, 0x00},
        "(上4结算日)正向有功费率29电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_30(new byte[]{0x04, (byte) 0x1E, 0x01, 0x00},
        "(上4结算日)正向有功费率30电能",
        DataType.KWH),
    POWER_POSITIVE_DAY04_BLOCK(new byte[]{0x04, (byte) 0xFF, 0x01, 0x00},
        "(上4结算日)正向有功电能数据块",
        DataType.KWH),

    POWER_POSITIVE_DAY05(new byte[]{0x05, (byte) 0x00, 0x01, 0x00}, "(上5结算日)正向有功总电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_01(new byte[]{0x05, (byte) 0x01, 0x01, 0x00},
        "(上5结算日)正向有功费率1电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_02(new byte[]{0x05, (byte) 0x02, 0x01, 0x00},
        "(上5结算日)正向有功费率2电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_03(new byte[]{0x05, (byte) 0x03, 0x01, 0x00},
        "(上5结算日)正向有功费率3电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_04(new byte[]{0x05, (byte) 0x04, 0x01, 0x00},
        "(上5结算日)正向有功费率4电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_05(new byte[]{0x05, (byte) 0x05, 0x01, 0x00},
        "(上5结算日)正向有功费率5电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_06(new byte[]{0x05, (byte) 0x06, 0x01, 0x00},
        "(上5结算日)正向有功费率6电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_07(new byte[]{0x05, (byte) 0x07, 0x01, 0x00},
        "(上5结算日)正向有功费率7电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_08(new byte[]{0x05, (byte) 0x08, 0x01, 0x00},
        "(上5结算日)正向有功费率8电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_09(new byte[]{0x05, (byte) 0x09, 0x01, 0x00},
        "(上5结算日)正向有功费率9电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_10(new byte[]{0x05, (byte) 0x0A, 0x01, 0x00},
        "(上5结算日)正向有功费率10电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_11(new byte[]{0x05, (byte) 0x0B, 0x01, 0x00},
        "(上5结算日)正向有功费率11电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_12(new byte[]{0x05, (byte) 0x0C, 0x01, 0x00},
        "(上5结算日)正向有功费率12电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_13(new byte[]{0x05, (byte) 0x0D, 0x01, 0x00},
        "(上5结算日)正向有功费率13电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_14(new byte[]{0x05, (byte) 0x0E, 0x01, 0x00},
        "(上5结算日)正向有功费率14电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_15(new byte[]{0x05, (byte) 0x0F, 0x01, 0x00},
        "(上5结算日)正向有功费率15电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_16(new byte[]{0x05, (byte) 0x10, 0x01, 0x00},
        "(上5结算日)正向有功费率16电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_17(new byte[]{0x05, (byte) 0x11, 0x01, 0x00},
        "(上5结算日)正向有功费率17电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_18(new byte[]{0x05, (byte) 0x12, 0x01, 0x00},
        "(上5结算日)正向有功费率18电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_19(new byte[]{0x05, (byte) 0x13, 0x01, 0x00},
        "(上5结算日)正向有功费率19电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_20(new byte[]{0x05, (byte) 0x14, 0x01, 0x00},
        "(上5结算日)正向有功费率20电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_21(new byte[]{0x05, (byte) 0x15, 0x01, 0x00},
        "(上5结算日)正向有功费率21电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_22(new byte[]{0x05, (byte) 0x16, 0x01, 0x00},
        "(上5结算日)正向有功费率22电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_23(new byte[]{0x05, (byte) 0x17, 0x01, 0x00},
        "(上5结算日)正向有功费率23电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_24(new byte[]{0x05, (byte) 0x18, 0x01, 0x00},
        "(上5结算日)正向有功费率24电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_25(new byte[]{0x05, (byte) 0x19, 0x01, 0x00},
        "(上5结算日)正向有功费率25电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_26(new byte[]{0x05, (byte) 0x1A, 0x01, 0x00},
        "(上5结算日)正向有功费率26电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_27(new byte[]{0x05, (byte) 0x1B, 0x01, 0x00},
        "(上5结算日)正向有功费率27电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_28(new byte[]{0x05, (byte) 0x1C, 0x01, 0x00},
        "(上5结算日)正向有功费率28电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_29(new byte[]{0x05, (byte) 0x1D, 0x01, 0x00},
        "(上5结算日)正向有功费率29电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_30(new byte[]{0x05, (byte) 0x1E, 0x01, 0x00},
        "(上5结算日)正向有功费率30电能",
        DataType.KWH),
    POWER_POSITIVE_DAY05_BLOCK(new byte[]{0x05, (byte) 0xFF, 0x01, 0x00},
        "(上5结算日)正向有功电能数据块",
        DataType.KWH),

    POWER_POSITIVE_DAY06(new byte[]{0x06, (byte) 0x00, 0x01, 0x00}, "(上6结算日)正向有功总电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_01(new byte[]{0x06, (byte) 0x01, 0x01, 0x00},
        "(上6结算日)正向有功费率1电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_02(new byte[]{0x06, (byte) 0x02, 0x01, 0x00},
        "(上6结算日)正向有功费率2电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_03(new byte[]{0x06, (byte) 0x03, 0x01, 0x00},
        "(上6结算日)正向有功费率3电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_04(new byte[]{0x06, (byte) 0x04, 0x01, 0x00},
        "(上6结算日)正向有功费率4电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_05(new byte[]{0x06, (byte) 0x05, 0x01, 0x00},
        "(上6结算日)正向有功费率5电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_06(new byte[]{0x06, (byte) 0x06, 0x01, 0x00},
        "(上6结算日)正向有功费率6电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_07(new byte[]{0x06, (byte) 0x07, 0x01, 0x00},
        "(上6结算日)正向有功费率7电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_08(new byte[]{0x06, (byte) 0x08, 0x01, 0x00},
        "(上6结算日)正向有功费率8电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_09(new byte[]{0x06, (byte) 0x09, 0x01, 0x00},
        "(上6结算日)正向有功费率9电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_10(new byte[]{0x06, (byte) 0x0A, 0x01, 0x00},
        "(上6结算日)正向有功费率10电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_11(new byte[]{0x06, (byte) 0x0B, 0x01, 0x00},
        "(上6结算日)正向有功费率11电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_12(new byte[]{0x06, (byte) 0x0C, 0x01, 0x00},
        "(上6结算日)正向有功费率12电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_13(new byte[]{0x06, (byte) 0x0D, 0x01, 0x00},
        "(上6结算日)正向有功费率13电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_14(new byte[]{0x06, (byte) 0x0E, 0x01, 0x00},
        "(上6结算日)正向有功费率14电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_15(new byte[]{0x06, (byte) 0x0F, 0x01, 0x00},
        "(上6结算日)正向有功费率15电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_16(new byte[]{0x06, (byte) 0x10, 0x01, 0x00},
        "(上6结算日)正向有功费率16电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_17(new byte[]{0x06, (byte) 0x11, 0x01, 0x00},
        "(上6结算日)正向有功费率17电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_18(new byte[]{0x06, (byte) 0x12, 0x01, 0x00},
        "(上6结算日)正向有功费率18电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_19(new byte[]{0x06, (byte) 0x13, 0x01, 0x00},
        "(上6结算日)正向有功费率19电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_20(new byte[]{0x06, (byte) 0x14, 0x01, 0x00},
        "(上6结算日)正向有功费率20电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_21(new byte[]{0x06, (byte) 0x15, 0x01, 0x00},
        "(上6结算日)正向有功费率21电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_22(new byte[]{0x06, (byte) 0x16, 0x01, 0x00},
        "(上6结算日)正向有功费率22电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_23(new byte[]{0x06, (byte) 0x17, 0x01, 0x00},
        "(上6结算日)正向有功费率23电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_24(new byte[]{0x06, (byte) 0x18, 0x01, 0x00},
        "(上6结算日)正向有功费率24电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_25(new byte[]{0x06, (byte) 0x19, 0x01, 0x00},
        "(上6结算日)正向有功费率25电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_26(new byte[]{0x06, (byte) 0x1A, 0x01, 0x00},
        "(上6结算日)正向有功费率26电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_27(new byte[]{0x06, (byte) 0x1B, 0x01, 0x00},
        "(上6结算日)正向有功费率27电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_28(new byte[]{0x06, (byte) 0x1C, 0x01, 0x00},
        "(上6结算日)正向有功费率28电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_29(new byte[]{0x06, (byte) 0x1D, 0x01, 0x00},
        "(上6结算日)正向有功费率29电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_30(new byte[]{0x06, (byte) 0x1E, 0x01, 0x00},
        "(上6结算日)正向有功费率30电能",
        DataType.KWH),
    POWER_POSITIVE_DAY06_BLOCK(new byte[]{0x06, (byte) 0xFF, 0x01, 0x00},
        "(上6结算日)正向有功电能数据块",
        DataType.KWH),

    POWER_POSITIVE_DAY07(new byte[]{0x07, (byte) 0x00, 0x01, 0x00}, "(上7结算日)正向有功总电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_01(new byte[]{0x07, (byte) 0x01, 0x01, 0x00},
        "(上7结算日)正向有功费率1电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_02(new byte[]{0x07, (byte) 0x02, 0x01, 0x00},
        "(上7结算日)正向有功费率2电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_03(new byte[]{0x07, (byte) 0x03, 0x01, 0x00},
        "(上7结算日)正向有功费率3电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_04(new byte[]{0x07, (byte) 0x04, 0x01, 0x00},
        "(上7结算日)正向有功费率4电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_05(new byte[]{0x07, (byte) 0x05, 0x01, 0x00},
        "(上7结算日)正向有功费率5电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_06(new byte[]{0x07, (byte) 0x06, 0x01, 0x00},
        "(上7结算日)正向有功费率6电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_07(new byte[]{0x07, (byte) 0x07, 0x01, 0x00},
        "(上7结算日)正向有功费率7电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_08(new byte[]{0x07, (byte) 0x08, 0x01, 0x00},
        "(上7结算日)正向有功费率8电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_09(new byte[]{0x07, (byte) 0x09, 0x01, 0x00},
        "(上7结算日)正向有功费率9电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_10(new byte[]{0x07, (byte) 0x0A, 0x01, 0x00},
        "(上7结算日)正向有功费率10电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_11(new byte[]{0x07, (byte) 0x0B, 0x01, 0x00},
        "(上7结算日)正向有功费率11电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_12(new byte[]{0x07, (byte) 0x0C, 0x01, 0x00},
        "(上7结算日)正向有功费率12电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_13(new byte[]{0x07, (byte) 0x0D, 0x01, 0x00},
        "(上7结算日)正向有功费率13电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_14(new byte[]{0x07, (byte) 0x0E, 0x01, 0x00},
        "(上7结算日)正向有功费率14电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_15(new byte[]{0x07, (byte) 0x0F, 0x01, 0x00},
        "(上7结算日)正向有功费率15电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_16(new byte[]{0x07, (byte) 0x10, 0x01, 0x00},
        "(上7结算日)正向有功费率16电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_17(new byte[]{0x07, (byte) 0x11, 0x01, 0x00},
        "(上7结算日)正向有功费率17电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_18(new byte[]{0x07, (byte) 0x12, 0x01, 0x00},
        "(上7结算日)正向有功费率18电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_19(new byte[]{0x07, (byte) 0x13, 0x01, 0x00},
        "(上7结算日)正向有功费率19电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_20(new byte[]{0x07, (byte) 0x14, 0x01, 0x00},
        "(上7结算日)正向有功费率20电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_21(new byte[]{0x07, (byte) 0x15, 0x01, 0x00},
        "(上7结算日)正向有功费率21电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_22(new byte[]{0x07, (byte) 0x16, 0x01, 0x00},
        "(上7结算日)正向有功费率22电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_23(new byte[]{0x07, (byte) 0x17, 0x01, 0x00},
        "(上7结算日)正向有功费率23电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_24(new byte[]{0x07, (byte) 0x18, 0x01, 0x00},
        "(上7结算日)正向有功费率24电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_25(new byte[]{0x07, (byte) 0x19, 0x01, 0x00},
        "(上7结算日)正向有功费率25电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_26(new byte[]{0x07, (byte) 0x1A, 0x01, 0x00},
        "(上7结算日)正向有功费率26电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_27(new byte[]{0x07, (byte) 0x1B, 0x01, 0x00},
        "(上7结算日)正向有功费率27电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_28(new byte[]{0x07, (byte) 0x1C, 0x01, 0x00},
        "(上7结算日)正向有功费率28电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_29(new byte[]{0x07, (byte) 0x1D, 0x01, 0x00},
        "(上7结算日)正向有功费率29电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_30(new byte[]{0x07, (byte) 0x1E, 0x01, 0x00},
        "(上7结算日)正向有功费率30电能",
        DataType.KWH),
    POWER_POSITIVE_DAY07_BLOCK(new byte[]{0x07, (byte) 0xFF, 0x01, 0x00},
        "(上7结算日)正向有功电能数据块",
        DataType.KWH),


    POWER_NEGATIVE_DAY01(new byte[]{0x01, (byte) 0x00, 0x02, 0x00}, "(上1结算日)反向有功总电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_01(new byte[]{0x01, (byte) 0x01, 0x02, 0x00},
        "(上1结算日)反向有功费率1电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_02(new byte[]{0x01, (byte) 0x02, 0x02, 0x00},
        "(上1结算日)反向有功费率2电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_03(new byte[]{0x01, (byte) 0x03, 0x02, 0x00},
        "(上1结算日)反向有功费率3电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_04(new byte[]{0x01, (byte) 0x04, 0x02, 0x00},
        "(上1结算日)反向有功费率4电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_05(new byte[]{0x01, (byte) 0x05, 0x02, 0x00},
        "(上1结算日)反向有功费率5电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_06(new byte[]{0x01, (byte) 0x06, 0x02, 0x00},
        "(上1结算日)反向有功费率6电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_07(new byte[]{0x01, (byte) 0x07, 0x02, 0x00},
        "(上1结算日)反向有功费率7电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_08(new byte[]{0x01, (byte) 0x08, 0x02, 0x00},
        "(上1结算日)反向有功费率8电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_09(new byte[]{0x01, (byte) 0x09, 0x02, 0x00},
        "(上1结算日)反向有功费率9电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_10(new byte[]{0x01, (byte) 0x0A, 0x02, 0x00},
        "(上1结算日)反向有功费率10电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_11(new byte[]{0x01, (byte) 0x0B, 0x02, 0x00},
        "(上1结算日)反向有功费率11电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_12(new byte[]{0x01, (byte) 0x0C, 0x02, 0x00},
        "(上1结算日)反向有功费率12电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_13(new byte[]{0x01, (byte) 0x0D, 0x02, 0x00},
        "(上1结算日)反向有功费率13电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_14(new byte[]{0x01, (byte) 0x0E, 0x02, 0x00},
        "(上1结算日)反向有功费率14电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_15(new byte[]{0x01, (byte) 0x0F, 0x02, 0x00},
        "(上1结算日)反向有功费率15电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_16(new byte[]{0x01, (byte) 0x10, 0x02, 0x00},
        "(上1结算日)反向有功费率16电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_17(new byte[]{0x01, (byte) 0x11, 0x02, 0x00},
        "(上1结算日)反向有功费率17电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_18(new byte[]{0x01, (byte) 0x12, 0x02, 0x00},
        "(上1结算日)反向有功费率18电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_19(new byte[]{0x01, (byte) 0x13, 0x02, 0x00},
        "(上1结算日)反向有功费率19电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_20(new byte[]{0x01, (byte) 0x14, 0x02, 0x00},
        "(上1结算日)反向有功费率20电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_21(new byte[]{0x01, (byte) 0x15, 0x02, 0x00},
        "(上1结算日)反向有功费率21电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_22(new byte[]{0x01, (byte) 0x16, 0x02, 0x00},
        "(上1结算日)反向有功费率22电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_23(new byte[]{0x01, (byte) 0x17, 0x02, 0x00},
        "(上1结算日)反向有功费率23电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_24(new byte[]{0x01, (byte) 0x18, 0x02, 0x00},
        "(上1结算日)反向有功费率24电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_25(new byte[]{0x01, (byte) 0x19, 0x02, 0x00},
        "(上1结算日)反向有功费率25电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_26(new byte[]{0x01, (byte) 0x1A, 0x02, 0x00},
        "(上1结算日)反向有功费率26电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_27(new byte[]{0x01, (byte) 0x1B, 0x02, 0x00},
        "(上1结算日)反向有功费率27电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_28(new byte[]{0x01, (byte) 0x1C, 0x02, 0x00},
        "(上1结算日)反向有功费率28电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_29(new byte[]{0x01, (byte) 0x1D, 0x02, 0x00},
        "(上1结算日)反向有功费率29电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_30(new byte[]{0x01, (byte) 0x1E, 0x02, 0x00},
        "(上1结算日)反向有功费率30电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY01_BLOCK(new byte[]{0x01, (byte) 0xFF, 0x02, 0x00},
        "(上1结算日)反向有功电能数据块",
        DataType.KWH),

    POWER_NEGATIVE_DAY02(new byte[]{0x02, (byte) 0x00, 0x02, 0x00}, "(上2结算日)反向有功总电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_01(new byte[]{0x02, (byte) 0x01, 0x02, 0x00},
        "(上2结算日)反向有功费率1电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_02(new byte[]{0x02, (byte) 0x02, 0x02, 0x00},
        "(上2结算日)反向有功费率2电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_03(new byte[]{0x02, (byte) 0x03, 0x02, 0x00},
        "(上2结算日)反向有功费率3电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_04(new byte[]{0x02, (byte) 0x04, 0x02, 0x00},
        "(上2结算日)反向有功费率4电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_05(new byte[]{0x02, (byte) 0x05, 0x02, 0x00},
        "(上2结算日)反向有功费率5电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_06(new byte[]{0x02, (byte) 0x06, 0x02, 0x00},
        "(上2结算日)反向有功费率6电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_07(new byte[]{0x02, (byte) 0x07, 0x02, 0x00},
        "(上2结算日)反向有功费率7电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_08(new byte[]{0x02, (byte) 0x08, 0x02, 0x00},
        "(上2结算日)反向有功费率8电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_09(new byte[]{0x02, (byte) 0x09, 0x02, 0x00},
        "(上2结算日)反向有功费率9电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_10(new byte[]{0x02, (byte) 0x0A, 0x02, 0x00},
        "(上2结算日)反向有功费率10电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_11(new byte[]{0x02, (byte) 0x0B, 0x02, 0x00},
        "(上2结算日)反向有功费率11电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_12(new byte[]{0x02, (byte) 0x0C, 0x02, 0x00},
        "(上2结算日)反向有功费率12电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_13(new byte[]{0x02, (byte) 0x0D, 0x02, 0x00},
        "(上2结算日)反向有功费率13电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_14(new byte[]{0x02, (byte) 0x0E, 0x02, 0x00},
        "(上2结算日)反向有功费率14电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_15(new byte[]{0x02, (byte) 0x0F, 0x02, 0x00},
        "(上2结算日)反向有功费率15电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_16(new byte[]{0x02, (byte) 0x10, 0x02, 0x00},
        "(上2结算日)反向有功费率16电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_17(new byte[]{0x02, (byte) 0x11, 0x02, 0x00},
        "(上2结算日)反向有功费率17电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_18(new byte[]{0x02, (byte) 0x12, 0x02, 0x00},
        "(上2结算日)反向有功费率18电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_19(new byte[]{0x02, (byte) 0x13, 0x02, 0x00},
        "(上2结算日)反向有功费率19电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_20(new byte[]{0x02, (byte) 0x14, 0x02, 0x00},
        "(上2结算日)反向有功费率20电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_21(new byte[]{0x02, (byte) 0x15, 0x02, 0x00},
        "(上2结算日)反向有功费率21电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_22(new byte[]{0x02, (byte) 0x16, 0x02, 0x00},
        "(上2结算日)反向有功费率22电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_23(new byte[]{0x02, (byte) 0x17, 0x02, 0x00},
        "(上2结算日)反向有功费率23电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_24(new byte[]{0x02, (byte) 0x18, 0x02, 0x00},
        "(上2结算日)反向有功费率24电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_25(new byte[]{0x02, (byte) 0x19, 0x02, 0x00},
        "(上2结算日)反向有功费率25电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_26(new byte[]{0x02, (byte) 0x1A, 0x02, 0x00},
        "(上2结算日)反向有功费率26电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_27(new byte[]{0x02, (byte) 0x1B, 0x02, 0x00},
        "(上2结算日)反向有功费率27电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_28(new byte[]{0x02, (byte) 0x1C, 0x02, 0x00},
        "(上2结算日)反向有功费率28电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_29(new byte[]{0x02, (byte) 0x1D, 0x02, 0x00},
        "(上2结算日)反向有功费率29电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_30(new byte[]{0x02, (byte) 0x1E, 0x02, 0x00},
        "(上2结算日)反向有功费率30电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY02_BLOCK(new byte[]{0x02, (byte) 0xFF, 0x02, 0x00},
        "(上2结算日)反向有功电能数据块",
        DataType.KWH),

    POWER_NEGATIVE_DAY03(new byte[]{0x03, (byte) 0x00, 0x02, 0x00}, "(上3结算日)反向有功总电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_01(new byte[]{0x03, (byte) 0x01, 0x02, 0x00},
        "(上3结算日)反向有功费率1电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_02(new byte[]{0x03, (byte) 0x02, 0x02, 0x00},
        "(上3结算日)反向有功费率2电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_03(new byte[]{0x03, (byte) 0x03, 0x02, 0x00},
        "(上3结算日)反向有功费率3电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_04(new byte[]{0x03, (byte) 0x04, 0x02, 0x00},
        "(上3结算日)反向有功费率4电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_05(new byte[]{0x03, (byte) 0x05, 0x02, 0x00},
        "(上3结算日)反向有功费率5电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_06(new byte[]{0x03, (byte) 0x06, 0x02, 0x00},
        "(上3结算日)反向有功费率6电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_07(new byte[]{0x03, (byte) 0x07, 0x02, 0x00},
        "(上3结算日)反向有功费率7电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_08(new byte[]{0x03, (byte) 0x08, 0x02, 0x00},
        "(上3结算日)反向有功费率8电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_09(new byte[]{0x03, (byte) 0x09, 0x02, 0x00},
        "(上3结算日)反向有功费率9电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_10(new byte[]{0x03, (byte) 0x0A, 0x02, 0x00},
        "(上3结算日)反向有功费率10电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_11(new byte[]{0x03, (byte) 0x0B, 0x02, 0x00},
        "(上3结算日)反向有功费率11电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_12(new byte[]{0x03, (byte) 0x0C, 0x02, 0x00},
        "(上3结算日)反向有功费率12电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_13(new byte[]{0x03, (byte) 0x0D, 0x02, 0x00},
        "(上3结算日)反向有功费率13电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_14(new byte[]{0x03, (byte) 0x0E, 0x02, 0x00},
        "(上3结算日)反向有功费率14电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_15(new byte[]{0x03, (byte) 0x0F, 0x02, 0x00},
        "(上3结算日)反向有功费率15电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_16(new byte[]{0x03, (byte) 0x10, 0x02, 0x00},
        "(上3结算日)反向有功费率16电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_17(new byte[]{0x03, (byte) 0x11, 0x02, 0x00},
        "(上3结算日)反向有功费率17电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_18(new byte[]{0x03, (byte) 0x12, 0x02, 0x00},
        "(上3结算日)反向有功费率18电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_19(new byte[]{0x03, (byte) 0x13, 0x02, 0x00},
        "(上3结算日)反向有功费率19电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_20(new byte[]{0x03, (byte) 0x14, 0x02, 0x00},
        "(上3结算日)反向有功费率20电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_21(new byte[]{0x03, (byte) 0x15, 0x02, 0x00},
        "(上3结算日)反向有功费率21电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_22(new byte[]{0x03, (byte) 0x16, 0x02, 0x00},
        "(上3结算日)反向有功费率22电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_23(new byte[]{0x03, (byte) 0x17, 0x02, 0x00},
        "(上3结算日)反向有功费率23电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_24(new byte[]{0x03, (byte) 0x18, 0x02, 0x00},
        "(上3结算日)反向有功费率24电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_25(new byte[]{0x03, (byte) 0x19, 0x02, 0x00},
        "(上3结算日)反向有功费率25电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_26(new byte[]{0x03, (byte) 0x1A, 0x02, 0x00},
        "(上3结算日)反向有功费率26电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_27(new byte[]{0x03, (byte) 0x1B, 0x02, 0x00},
        "(上3结算日)反向有功费率27电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_28(new byte[]{0x03, (byte) 0x1C, 0x02, 0x00},
        "(上3结算日)反向有功费率28电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_29(new byte[]{0x03, (byte) 0x1D, 0x02, 0x00},
        "(上3结算日)反向有功费率29电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_30(new byte[]{0x03, (byte) 0x1E, 0x02, 0x00},
        "(上3结算日)反向有功费率30电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY03_BLOCK(new byte[]{0x03, (byte) 0xFF, 0x02, 0x00},
        "(上3结算日)反向有功电能数据块",
        DataType.KWH),
    POWER_NEGATIVE_DAY04(new byte[]{0x04, (byte) 0x00, 0x02, 0x00}, "(上4结算日)反向有功总电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_01(new byte[]{0x04, (byte) 0x01, 0x02, 0x00},
        "(上4结算日)反向有功费率1电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_02(new byte[]{0x04, (byte) 0x02, 0x02, 0x00},
        "(上4结算日)反向有功费率2电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_03(new byte[]{0x04, (byte) 0x03, 0x02, 0x00},
        "(上4结算日)反向有功费率3电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_04(new byte[]{0x04, (byte) 0x04, 0x02, 0x00},
        "(上4结算日)反向有功费率4电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_05(new byte[]{0x04, (byte) 0x05, 0x02, 0x00},
        "(上4结算日)反向有功费率5电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_06(new byte[]{0x04, (byte) 0x06, 0x02, 0x00},
        "(上4结算日)反向有功费率6电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_07(new byte[]{0x04, (byte) 0x07, 0x02, 0x00},
        "(上4结算日)反向有功费率7电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_08(new byte[]{0x04, (byte) 0x08, 0x02, 0x00},
        "(上4结算日)反向有功费率8电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_09(new byte[]{0x04, (byte) 0x09, 0x02, 0x00},
        "(上4结算日)反向有功费率9电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_10(new byte[]{0x04, (byte) 0x0A, 0x02, 0x00},
        "(上4结算日)反向有功费率10电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_11(new byte[]{0x04, (byte) 0x0B, 0x02, 0x00},
        "(上4结算日)反向有功费率11电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_12(new byte[]{0x04, (byte) 0x0C, 0x02, 0x00},
        "(上4结算日)反向有功费率12电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_13(new byte[]{0x04, (byte) 0x0D, 0x02, 0x00},
        "(上4结算日)反向有功费率13电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_14(new byte[]{0x04, (byte) 0x0E, 0x02, 0x00},
        "(上4结算日)反向有功费率14电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_15(new byte[]{0x04, (byte) 0x0F, 0x02, 0x00},
        "(上4结算日)反向有功费率15电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_16(new byte[]{0x04, (byte) 0x10, 0x02, 0x00},
        "(上4结算日)反向有功费率16电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_17(new byte[]{0x04, (byte) 0x11, 0x02, 0x00},
        "(上4结算日)反向有功费率17电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_18(new byte[]{0x04, (byte) 0x12, 0x02, 0x00},
        "(上4结算日)反向有功费率18电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_19(new byte[]{0x04, (byte) 0x13, 0x02, 0x00},
        "(上4结算日)反向有功费率19电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_20(new byte[]{0x04, (byte) 0x14, 0x02, 0x00},
        "(上4结算日)反向有功费率20电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_21(new byte[]{0x04, (byte) 0x15, 0x02, 0x00},
        "(上4结算日)反向有功费率21电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_22(new byte[]{0x04, (byte) 0x16, 0x02, 0x00},
        "(上4结算日)反向有功费率22电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_23(new byte[]{0x04, (byte) 0x17, 0x02, 0x00},
        "(上4结算日)反向有功费率23电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_24(new byte[]{0x04, (byte) 0x18, 0x02, 0x00},
        "(上4结算日)反向有功费率24电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_25(new byte[]{0x04, (byte) 0x19, 0x02, 0x00},
        "(上4结算日)反向有功费率25电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_26(new byte[]{0x04, (byte) 0x1A, 0x02, 0x00},
        "(上4结算日)反向有功费率26电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_27(new byte[]{0x04, (byte) 0x1B, 0x02, 0x00},
        "(上4结算日)反向有功费率27电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_28(new byte[]{0x04, (byte) 0x1C, 0x02, 0x00},
        "(上4结算日)反向有功费率28电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_29(new byte[]{0x04, (byte) 0x1D, 0x02, 0x00},
        "(上4结算日)反向有功费率29电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_30(new byte[]{0x04, (byte) 0x1E, 0x02, 0x00},
        "(上4结算日)反向有功费率30电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY04_BLOCK(new byte[]{0x04, (byte) 0xFF, 0x02, 0x00},
        "(上4结算日)反向有功电能数据块",
        DataType.KWH),

    POWER_NEGATIVE_DAY05(new byte[]{0x05, (byte) 0x00, 0x02, 0x00}, "(上5结算日)反向有功总电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_01(new byte[]{0x05, (byte) 0x01, 0x02, 0x00},
        "(上5结算日)反向有功费率1电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_02(new byte[]{0x05, (byte) 0x02, 0x02, 0x00},
        "(上5结算日)反向有功费率2电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_03(new byte[]{0x05, (byte) 0x03, 0x02, 0x00},
        "(上5结算日)反向有功费率3电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_04(new byte[]{0x05, (byte) 0x04, 0x02, 0x00},
        "(上5结算日)反向有功费率4电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_05(new byte[]{0x05, (byte) 0x05, 0x02, 0x00},
        "(上5结算日)反向有功费率5电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_06(new byte[]{0x05, (byte) 0x06, 0x02, 0x00},
        "(上5结算日)反向有功费率6电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_07(new byte[]{0x05, (byte) 0x07, 0x02, 0x00},
        "(上5结算日)反向有功费率7电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_08(new byte[]{0x05, (byte) 0x08, 0x02, 0x00},
        "(上5结算日)反向有功费率8电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_09(new byte[]{0x05, (byte) 0x09, 0x02, 0x00},
        "(上5结算日)反向有功费率9电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_10(new byte[]{0x05, (byte) 0x0A, 0x02, 0x00},
        "(上5结算日)反向有功费率10电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_11(new byte[]{0x05, (byte) 0x0B, 0x02, 0x00},
        "(上5结算日)反向有功费率11电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_12(new byte[]{0x05, (byte) 0x0C, 0x02, 0x00},
        "(上5结算日)反向有功费率12电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_13(new byte[]{0x05, (byte) 0x0D, 0x02, 0x00},
        "(上5结算日)反向有功费率13电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_14(new byte[]{0x05, (byte) 0x0E, 0x02, 0x00},
        "(上5结算日)反向有功费率14电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_15(new byte[]{0x05, (byte) 0x0F, 0x02, 0x00},
        "(上5结算日)反向有功费率15电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_16(new byte[]{0x05, (byte) 0x10, 0x02, 0x00},
        "(上5结算日)反向有功费率16电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_17(new byte[]{0x05, (byte) 0x11, 0x02, 0x00},
        "(上5结算日)反向有功费率17电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_18(new byte[]{0x05, (byte) 0x12, 0x02, 0x00},
        "(上5结算日)反向有功费率18电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_19(new byte[]{0x05, (byte) 0x13, 0x02, 0x00},
        "(上5结算日)反向有功费率19电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_20(new byte[]{0x05, (byte) 0x14, 0x02, 0x00},
        "(上5结算日)反向有功费率20电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_21(new byte[]{0x05, (byte) 0x15, 0x02, 0x00},
        "(上5结算日)反向有功费率21电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_22(new byte[]{0x05, (byte) 0x16, 0x02, 0x00},
        "(上5结算日)反向有功费率22电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_23(new byte[]{0x05, (byte) 0x17, 0x02, 0x00},
        "(上5结算日)反向有功费率23电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_24(new byte[]{0x05, (byte) 0x18, 0x02, 0x00},
        "(上5结算日)反向有功费率24电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_25(new byte[]{0x05, (byte) 0x19, 0x02, 0x00},
        "(上5结算日)反向有功费率25电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_26(new byte[]{0x05, (byte) 0x1A, 0x02, 0x00},
        "(上5结算日)反向有功费率26电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_27(new byte[]{0x05, (byte) 0x1B, 0x02, 0x00},
        "(上5结算日)反向有功费率27电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_28(new byte[]{0x05, (byte) 0x1C, 0x02, 0x00},
        "(上5结算日)反向有功费率28电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_29(new byte[]{0x05, (byte) 0x1D, 0x02, 0x00},
        "(上5结算日)反向有功费率29电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_30(new byte[]{0x05, (byte) 0x1E, 0x02, 0x00},
        "(上5结算日)反向有功费率30电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY05_BLOCK(new byte[]{0x05, (byte) 0xFF, 0x02, 0x00},
        "(上5结算日)反向有功电能数据块",
        DataType.KWH),

    POWER_NEGATIVE_DAY06(new byte[]{0x06, (byte) 0x00, 0x02, 0x00}, "(上6结算日)反向有功总电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_01(new byte[]{0x06, (byte) 0x01, 0x02, 0x00},
        "(上6结算日)反向有功费率1电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_02(new byte[]{0x06, (byte) 0x02, 0x02, 0x00},
        "(上6结算日)反向有功费率2电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_03(new byte[]{0x06, (byte) 0x03, 0x02, 0x00},
        "(上6结算日)反向有功费率3电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_04(new byte[]{0x06, (byte) 0x04, 0x02, 0x00},
        "(上6结算日)反向有功费率4电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_05(new byte[]{0x06, (byte) 0x05, 0x02, 0x00},
        "(上6结算日)反向有功费率5电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_06(new byte[]{0x06, (byte) 0x06, 0x02, 0x00},
        "(上6结算日)反向有功费率6电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_07(new byte[]{0x06, (byte) 0x07, 0x02, 0x00},
        "(上6结算日)反向有功费率7电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_08(new byte[]{0x06, (byte) 0x08, 0x02, 0x00},
        "(上6结算日)反向有功费率8电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_09(new byte[]{0x06, (byte) 0x09, 0x02, 0x00},
        "(上6结算日)反向有功费率9电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_10(new byte[]{0x06, (byte) 0x0A, 0x02, 0x00},
        "(上6结算日)反向有功费率10电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_11(new byte[]{0x06, (byte) 0x0B, 0x02, 0x00},
        "(上6结算日)反向有功费率11电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_12(new byte[]{0x06, (byte) 0x0C, 0x02, 0x00},
        "(上6结算日)反向有功费率12电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_13(new byte[]{0x06, (byte) 0x0D, 0x02, 0x00},
        "(上6结算日)反向有功费率13电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_14(new byte[]{0x06, (byte) 0x0E, 0x02, 0x00},
        "(上6结算日)反向有功费率14电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_15(new byte[]{0x06, (byte) 0x0F, 0x02, 0x00},
        "(上6结算日)反向有功费率15电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_16(new byte[]{0x06, (byte) 0x10, 0x02, 0x00},
        "(上6结算日)反向有功费率16电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_17(new byte[]{0x06, (byte) 0x11, 0x02, 0x00},
        "(上6结算日)反向有功费率17电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_18(new byte[]{0x06, (byte) 0x12, 0x02, 0x00},
        "(上6结算日)反向有功费率18电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_19(new byte[]{0x06, (byte) 0x13, 0x02, 0x00},
        "(上6结算日)反向有功费率19电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_20(new byte[]{0x06, (byte) 0x14, 0x02, 0x00},
        "(上6结算日)反向有功费率20电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_21(new byte[]{0x06, (byte) 0x15, 0x02, 0x00},
        "(上6结算日)反向有功费率21电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_22(new byte[]{0x06, (byte) 0x16, 0x02, 0x00},
        "(上6结算日)反向有功费率22电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_23(new byte[]{0x06, (byte) 0x17, 0x02, 0x00},
        "(上6结算日)反向有功费率23电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_24(new byte[]{0x06, (byte) 0x18, 0x02, 0x00},
        "(上6结算日)反向有功费率24电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_25(new byte[]{0x06, (byte) 0x19, 0x02, 0x00},
        "(上6结算日)反向有功费率25电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_26(new byte[]{0x06, (byte) 0x1A, 0x02, 0x00},
        "(上6结算日)反向有功费率26电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_27(new byte[]{0x06, (byte) 0x1B, 0x02, 0x00},
        "(上6结算日)反向有功费率27电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_28(new byte[]{0x06, (byte) 0x1C, 0x02, 0x00},
        "(上6结算日)反向有功费率28电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_29(new byte[]{0x06, (byte) 0x1D, 0x02, 0x00},
        "(上6结算日)反向有功费率29电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_30(new byte[]{0x06, (byte) 0x1E, 0x02, 0x00},
        "(上6结算日)反向有功费率30电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY06_BLOCK(new byte[]{0x06, (byte) 0xFF, 0x02, 0x00},
        "(上6结算日)反向有功电能数据块",
        DataType.KWH),

    POWER_NEGATIVE_DAY07(new byte[]{0x07, (byte) 0x00, 0x02, 0x00}, "(上7结算日)反向有功总电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_01(new byte[]{0x07, (byte) 0x01, 0x02, 0x00},
        "(上7结算日)反向有功费率1电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_02(new byte[]{0x07, (byte) 0x02, 0x02, 0x00},
        "(上7结算日)反向有功费率2电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_03(new byte[]{0x07, (byte) 0x03, 0x02, 0x00},
        "(上7结算日)反向有功费率3电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_04(new byte[]{0x07, (byte) 0x04, 0x02, 0x00},
        "(上7结算日)反向有功费率4电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_05(new byte[]{0x07, (byte) 0x05, 0x02, 0x00},
        "(上7结算日)反向有功费率5电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_06(new byte[]{0x07, (byte) 0x06, 0x02, 0x00},
        "(上7结算日)反向有功费率6电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_07(new byte[]{0x07, (byte) 0x07, 0x02, 0x00},
        "(上7结算日)反向有功费率7电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_08(new byte[]{0x07, (byte) 0x08, 0x02, 0x00},
        "(上7结算日)反向有功费率8电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_09(new byte[]{0x07, (byte) 0x09, 0x02, 0x00},
        "(上7结算日)反向有功费率9电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_10(new byte[]{0x07, (byte) 0x0A, 0x02, 0x00},
        "(上7结算日)反向有功费率10电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_11(new byte[]{0x07, (byte) 0x0B, 0x02, 0x00},
        "(上7结算日)反向有功费率11电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_12(new byte[]{0x07, (byte) 0x0C, 0x02, 0x00},
        "(上7结算日)反向有功费率12电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_13(new byte[]{0x07, (byte) 0x0D, 0x02, 0x00},
        "(上7结算日)反向有功费率13电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_14(new byte[]{0x07, (byte) 0x0E, 0x02, 0x00},
        "(上7结算日)反向有功费率14电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_15(new byte[]{0x07, (byte) 0x0F, 0x02, 0x00},
        "(上7结算日)反向有功费率15电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_16(new byte[]{0x07, (byte) 0x10, 0x02, 0x00},
        "(上7结算日)反向有功费率16电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_17(new byte[]{0x07, (byte) 0x11, 0x02, 0x00},
        "(上7结算日)反向有功费率17电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_18(new byte[]{0x07, (byte) 0x12, 0x02, 0x00},
        "(上7结算日)反向有功费率18电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_19(new byte[]{0x07, (byte) 0x13, 0x02, 0x00},
        "(上7结算日)反向有功费率19电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_20(new byte[]{0x07, (byte) 0x14, 0x02, 0x00},
        "(上7结算日)反向有功费率20电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_21(new byte[]{0x07, (byte) 0x15, 0x02, 0x00},
        "(上7结算日)反向有功费率21电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_22(new byte[]{0x07, (byte) 0x16, 0x02, 0x00},
        "(上7结算日)反向有功费率22电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_23(new byte[]{0x07, (byte) 0x17, 0x02, 0x00},
        "(上7结算日)反向有功费率23电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_24(new byte[]{0x07, (byte) 0x18, 0x02, 0x00},
        "(上7结算日)反向有功费率24电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_25(new byte[]{0x07, (byte) 0x19, 0x02, 0x00},
        "(上7结算日)反向有功费率25电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_26(new byte[]{0x07, (byte) 0x1A, 0x02, 0x00},
        "(上7结算日)反向有功费率26电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_27(new byte[]{0x07, (byte) 0x1B, 0x02, 0x00},
        "(上7结算日)反向有功费率27电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_28(new byte[]{0x07, (byte) 0x1C, 0x02, 0x00},
        "(上7结算日)反向有功费率28电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_29(new byte[]{0x07, (byte) 0x1D, 0x02, 0x00},
        "(上7结算日)反向有功费率29电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_30(new byte[]{0x07, (byte) 0x1E, 0x02, 0x00},
        "(上7结算日)反向有功费率30电能",
        DataType.KWH),
    POWER_NEGATIVE_DAY07_BLOCK(new byte[]{0x07, (byte) 0xFF, 0x02, 0x00},
        "(上7结算日)反向有功电能数据块",
        DataType.KWH),

    CUR_POSITIVE_POWER_DEMAND(new byte[]{0x00, (byte) 0x00, 0x01, 0x01},
        "（当前）正向有功总最大需量及发生时间",
        DataType.POWER),
    CUR_POSITIVE_POWER_DEMAND_TIME(new byte[]{0x00, (byte) 0xFE, 0x01, 0x01},
        "（当前）正向有功总最大需量发生时间",
        DataType.POWER),    // 因DLT645协议最大需量的值和时间为一个地址，此处使用一个自定义的虚拟地址来存储发生时间

    CUR_ACTIVE_POWER_DEMAND(new byte[]{0x04, 0x00, (byte) 0x80, 0x02},
        "当前有功需量",
        DataType.POWER),


    CUR_VOLTAGE_A(new byte[]{0x00, (byte) 0x01, 0x01, 0x02}, "A相电压数",
        DataType.VOLTAGE),
    CUR_VOLTAGE_B(new byte[]{0x00, (byte) 0x02, 0x01, 0x02}, "B相电压数",
        DataType.VOLTAGE),
    CUR_VOLTAGE_C(new byte[]{0x00, (byte) 0x03, 0x01, 0x02}, "C相电压数",
        DataType.VOLTAGE),
    CUR_VOLTAGE(new byte[]{0x00, (byte) 0xFF, 0x01, 0x02}, "ABC三相电压数据块",
        DataType.VOLTAGE),

    CUR_CURRENT_A(new byte[]{0x00, (byte) 0x01, 0x02, 0x02}, "A相电流",
        DataType.CURRENT),
    CUR_CURRENT_B(new byte[]{0x00, (byte) 0x02, 0x02, 0x02}, "B相电流",
        DataType.CURRENT),
    CUR_CURRENT_C(new byte[]{0x00, (byte) 0x03, 0x02, 0x02}, "C相电流数",
        DataType.CURRENT),
    CUR_CURRENT(new byte[]{0x00, (byte) 0xFF, 0x02, 0x02}, "ABC三相电流数据块",
        DataType.CURRENT),

    CUR_POWER_ACTIVE(new byte[]{0x00, (byte) 0x00, 0x03, 0x02}, "瞬时总有功功率",
        DataType.POWER),
    CUR_POWER_ACTIVE_A(new byte[]{0x00, (byte) 0x01, 0x03, 0x02}, "瞬时A相有功功率",
        DataType.POWER),
    CUR_POWER_ACTIVE_B(new byte[]{0x00, (byte) 0x02, 0x03, 0x02}, "瞬时B相有功功率",
        DataType.POWER),
    CUR_POWER_ACTIVE_C(new byte[]{0x00, (byte) 0x03, 0x03, 0x02}, "瞬时C相有功功率",
        DataType.POWER),
    CUR_POWER_ACTIVE_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x03, 0x02}, "瞬时有功功率数据块",
        DataType.POWER),

    CUR_POWER_REACTIVE(new byte[]{0x00, (byte) 0x00, 0x04, 0x02}, "瞬时总无功功率",
        DataType.POWER),
    CUR_POWER_REACTIVE_A(new byte[]{0x00, (byte) 0x01, 0x04, 0x02}, "瞬时A相无功功率",
        DataType.POWER),
    CUR_POWER_REACTIVE_B(new byte[]{0x00, (byte) 0x02, 0x04, 0x02}, "瞬时B相无功功率",
        DataType.POWER),
    CUR_POWER_REACTIVE_C(new byte[]{0x00, (byte) 0x03, 0x04, 0x02}, "瞬时C相无功功率",
        DataType.POWER),
    CUR_POWER_REACTIVE_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x04, 0x02}, "瞬时无功功率数据块",
        DataType.POWER),

    CUR_POWER_APPARENT(new byte[]{0x00, (byte) 0x00, 0x05, 0x02}, "瞬时总视在功率",
        DataType.POWER),
    CUR_POWER_APPARENT_A(new byte[]{0x00, (byte) 0x01, 0x05, 0x02}, "瞬时A相视在功率",
        DataType.POWER),
    CUR_POWER_APPARENT_B(new byte[]{0x00, (byte) 0x02, 0x05, 0x02}, "瞬时B相视在功率",
        DataType.POWER),
    CUR_POWER_APPARENT_C(new byte[]{0x00, (byte) 0x03, 0x05, 0x02}, "瞬时C相视在功率",
        DataType.POWER),
    CUR_POWER_APPARENT_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x05, 0x02}, "瞬时视在功率数据块",
        DataType.POWER),

    CUR_POWER_FACTOR(new byte[]{0x00, (byte) 0x00, 0x06, 0x02}, "总功率因数",
        DataType.OTHER),
    CUR_POWER_FACTOR_A(new byte[]{0x00, (byte) 0x01, 0x06, 0x02}, "A相功率因数",
        DataType.OTHER),
    CUR_POWER_FACTOR_B(new byte[]{0x00, (byte) 0x02, 0x06, 0x02}, "B相功率因数",
        DataType.OTHER),
    CUR_POWER_FACTOR_C(new byte[]{0x00, (byte) 0x03, 0x06, 0x02}, "C相功率因数",
        DataType.OTHER),
    CUR_POWER_FACTOR_BLOCK(new byte[]{0x00, (byte) 0xFF, 0x06, 0x02}, "功率因数数据块",
        DataType.OTHER),

    DATE_AND_WEEK(new byte[]{0x01, (byte) 0x01, 0x00, 0x04}, "日期及星期(其中0代表星期天)",
        DataType.OTHER),    // YYMMDDWW

    TIME(new byte[]{0x02, (byte) 0x01, 0x00, 0x04}, "时间",
        DataType.OTHER),    // hhmmss

    CFG_TIME_ZONE_SWITCH_TIME(new byte[]{0x06, (byte) 0x01, 0x00, 0x04},
        "两套时区表切换时间", DataType.OTHER),

    CFG_TIME_BUCKET_SWITCH_TIME(new byte[]{0x07, (byte) 0x01, 0x00, 0x04},
        "两套日时段表切换时间", DataType.OTHER),

    /**
     * bit0: 当前运行时段 (0第一套，1第二套)
     */
    CFG_PARAM_3(new byte[]{0x03, (byte) 0x05, 0x00, 0x04},
        "电表运行状态字3", DataType.OTHER),


    CFG_TIME_ZONE_NUM(new byte[]{0x01, (byte) 0x02, 0x00, 0x04},
        "年时区数",
        DataType.OTHER),

    CFG_TIME_BUCKET_TABLE_NUM(new byte[]{0x02, (byte) 0x02, 0x00, 0x04},
        "日时段表数",
        DataType.OTHER),

    CFG_TIME_BUCKET_IN_DAY_NUM(new byte[]{0x03, (byte) 0x02, 0x00, 0x04},
        "日时段数(每日切换数)",
        DataType.OTHER),

    CFG_PRICE_SCHEMA_NUM(new byte[]{0x04, (byte) 0x02, 0x00, 0x04},
        "费率数",
        DataType.OTHER),

    CFG_PAZZWORD_L2(new byte[]{0x03, (byte) 0x0C, 0x00, 0x04},
        "2级密码",
        DataType.OTHER),

    CFG_PAZZWORD_L4(new byte[]{0x05, (byte) 0x0C, 0x00, 0x04},
        "4级密码",
        DataType.OTHER),

    CFG_TIME_ZONE_ONE(new byte[]{0x00, (byte) 0x00, 0x01, 0x04},
        "第一套时区表数据",
        DataType.OTHER),

    CFG_TIME_BUCKET_ONE_D1(new byte[]{0x01, (byte) 0x00, 0x01, 0x04},
        "第一套第1日时段表数据",
        DataType.OTHER),

    CFG_TIME_BUCKET_ONE_D2(new byte[]{0x02, (byte) 0x00, 0x01, 0x04},
        "第一套第2日时段表数据",
        DataType.OTHER),

    CFG_TIME_BUCKET_ONE_D3(new byte[]{0x03, (byte) 0x00, 0x01, 0x04},
        "第一套第3日时段表数据",
        DataType.OTHER),

    CFG_TIME_BUCKET_ONE_D4(new byte[]{0x04, (byte) 0x00, 0x01, 0x04},
        "第一套第4日时段表数据",
        DataType.OTHER),

    CFG_TIME_BUCKET_ONE_D5(new byte[]{0x05, (byte) 0x00, 0x01, 0x04},
        "第一套第5日时段表数据",
        DataType.OTHER),

    CFG_TIME_BUCKET_ONE_D6(new byte[]{0x06, (byte) 0x00, 0x01, 0x04},
        "第一套第6日时段表数据",
        DataType.OTHER),

    CFG_TIME_BUCKET_ONE_D7(new byte[]{0x07, (byte) 0x00, 0x01, 0x04},
        "第一套第7日时段表数据",
        DataType.OTHER),

    CUR_CURRENT_PT(new byte[]{0x06, (byte) 0x03, 0x00, 0x04}, "电流互感器变比",
        DataType.OTHER),

    CUR_VOLTAGE_PT(new byte[]{0x07, (byte) 0x03, 0x00, 0x04}, "电压互感器变比",
        DataType.OTHER),

    ;

    final byte[] dataType;
    final String nameTag;
    final DataType fieldType;


    P645DataType(byte[] dataType, String nameTag, DataType fieldType) {
        this.dataType = dataType;
        this.nameTag = nameTag;
        this.fieldType = fieldType;
    }

    public static P645DataType valueOf(byte[] codeIn) {
        for (P645DataType type : values()) {
            if (Arrays.equals(type.dataType, codeIn)) {
                return type;
            }
        }
        return P645DataType.UNKNOWN;
    }

    public String hex() {
        return ByteUtils.bytesToHex(this.dataType);
    }

    public Long code() {
        return (long) ((this.dataType[0] & 0xFF) << 24)
            + (this.dataType[1] & 0xFF) << 16
            + (this.dataType[2] & 0xFF) << 8
            + (this.dataType[3] & 0xFF);
    }
}
