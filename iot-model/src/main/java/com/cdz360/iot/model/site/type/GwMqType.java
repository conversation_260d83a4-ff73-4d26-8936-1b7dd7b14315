package com.cdz360.iot.model.site.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;


@Getter
public enum GwMqType implements DcEnum {

    UNKNOWN(0, "未知"),
    MQ_TYPE_RABBITMQ(1, "RabbitMq"), // RABBITMQ
    MQ_TYPE_MQTT(2, "mqtt");


    @JsonValue
    private final int code;
    private final String desc;

    GwMqType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static GwMqType valueOf(Object codeIn) {
        if (codeIn == null) {
            return GwMqType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof GwMqType) {
            return (GwMqType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (GwMqType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return GwMqType.UNKNOWN;
    }
}
