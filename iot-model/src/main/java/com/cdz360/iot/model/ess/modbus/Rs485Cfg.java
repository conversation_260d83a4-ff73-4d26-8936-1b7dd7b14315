package com.cdz360.iot.model.ess.modbus;

import com.cdz360.iot.model.modbus.dto.ModbusAddrRange;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class Rs485Cfg {

    private String dno;
    /**
     * 通信设备ID
     */
    private Integer sid;

    private String appName;
    // 端口名称
    private String portName;

    // 波特率
    private Integer baudRate;

    private Integer dataBits = 8;

    private Integer stopBits = 1;

    private String parity = "NONE";


    private List<ModbusAddrRange> addrs;
}
