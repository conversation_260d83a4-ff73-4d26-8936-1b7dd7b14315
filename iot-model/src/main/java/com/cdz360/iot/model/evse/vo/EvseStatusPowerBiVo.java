package com.cdz360.iot.model.evse.vo;

import com.cdz360.base.model.base.type.EvseStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "按状态统计桩功率")
public class EvseStatusPowerBiVo {

    @Schema(description = "桩状态", example = "BUSY")
    private EvseStatus evseStatus;

    @Schema(description = "功率", example = "123")
    private Long power;
}
