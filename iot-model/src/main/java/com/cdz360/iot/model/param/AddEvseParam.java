package com.cdz360.iot.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

@Data
public class AddEvseParam {

    private String evseNo;

    @Schema(description = "桩额定功率", example = "120")
    private Integer power;

    @Schema(description = "桩出厂编号", example = "T17223584")
    private String produceNo;

    @Schema(description = "桩出厂日期", example = "2020-01-01")
    private Date produceDate;

    @Schema(description = "质保到期日", example = "2020-01-01")
    private Date expireDate;

    @Schema(description = "枪头数", example = "2")
    private Integer plugNum;

}
