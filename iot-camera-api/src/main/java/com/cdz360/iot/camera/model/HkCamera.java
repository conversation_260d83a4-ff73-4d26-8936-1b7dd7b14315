package com.cdz360.iot.camera.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "海康云眸摄象机信息")
@Data
@Accessors(chain = true)
public class HkCamera {
    @Schema(description = "设备ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String deviceId;

    @Schema(description = "设备名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String deviceName;

    @Schema(description = "设备型号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String deviceModel;

    @Schema(description = "设备序列号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String deviceSerial;

    @Schema(description = "通道ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String channelId;

    @Schema(description = "通道名")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String channelName;

    @Schema(description = "通道号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer channelNo;

    @Schema(description = "状态，0：离线，1：在线")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer channelStatus;

    @Schema(description = "通道封面图片URL")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String channelPicUrl;

}
