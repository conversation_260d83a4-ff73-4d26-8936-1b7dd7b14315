package com.cdz360.iot.camera.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "通过门店ID查询设备通道列表查询参数")
@EqualsAndHashCode(callSuper = true)
public class CameraListParam extends HkListReqMsg {

    @Schema(description = "门店ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String storeId;

    @Schema(description = "门店编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String storeNo;
}
