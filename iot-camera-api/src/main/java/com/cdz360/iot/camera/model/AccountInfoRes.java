package com.cdz360.iot.camera.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname AccountInfoRes
 * @Description
 * @Date 7/28/2021 1:15 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Schema(description = "通过门店ID查询设备通道列表查询参数")
public class AccountInfoRes {
    private String appKey;
    private String token;
}