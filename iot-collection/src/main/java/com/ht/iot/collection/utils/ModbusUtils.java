package com.ht.iot.collection.utils;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.iot.model.modbus.type.ModbusValueOrder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ModbusUtils {


    /**
     * 调整寄存器位置
     */
    public static byte[] orderBytes(String tid, byte[] bufIn, int idx, ModbusValueOrder orderIn,
        int byteNum) {
        byte[] result = new byte[byteNum];
        if (ModbusValueOrder.AABB == orderIn) {
            result[0] = bufIn[idx + 1];
            result[1] = bufIn[idx + 0];
        } else if (ModbusValueOrder.BBAA == orderIn) {
            result[0] = bufIn[idx + 0];
            result[1] = bufIn[idx + 1];
        } else if (ModbusValueOrder.AABBCC == orderIn) {
            result[0] = bufIn[idx + 2];
            result[1] = bufIn[idx + 1];
            result[2] = bufIn[idx + 0];
        } else if (ModbusValueOrder.CCBBAA == orderIn) {
            result[0] = bufIn[idx + 0];
            result[1] = bufIn[idx + 1];
            result[2] = bufIn[idx + 2];
        } else if (ModbusValueOrder.AABBCCDD == orderIn) {
            result[0] = bufIn[idx + 3];
            result[1] = bufIn[idx + 2];
            result[2] = bufIn[idx + 1];
            result[3] = bufIn[idx + 0];
        } else if (ModbusValueOrder.BBAADDCC == orderIn) {
            result[0] = bufIn[idx + 2];
            result[1] = bufIn[idx + 3];
            result[2] = bufIn[idx + 0];
            result[3] = bufIn[idx + 1];
        } else if (ModbusValueOrder.CCDDAABB == orderIn) {
            result[0] = bufIn[idx + 1];
            result[1] = bufIn[idx + 0];
            result[2] = bufIn[idx + 3];
            result[3] = bufIn[idx + 2];
        } else if (ModbusValueOrder.DDCCBBAA == orderIn) {
            result[0] = bufIn[idx + 0];
            result[1] = bufIn[idx + 1];
            result[2] = bufIn[idx + 2];
            result[3] = bufIn[idx + 3];
        } else if (ModbusValueOrder.AABBCCDDEEFFGGHH == orderIn) {
            result[0] = bufIn[idx + 7];
            result[1] = bufIn[idx + 6];
            result[2] = bufIn[idx + 5];
            result[3] = bufIn[idx + 4];
            result[4] = bufIn[idx + 3];
            result[5] = bufIn[idx + 2];
            result[6] = bufIn[idx + 1];
            result[7] = bufIn[idx + 0];
        } else {
            log.error("[{}] 不支持的数值编码排序类型. orderIn= {}", tid, orderIn);
            throw new DcArgumentException("不支持的数值编码排序类型");
        }
        return result;
    }
}
