package com.ht.iot.collection.north.biz;

import com.ht.iot.collection.biz.HoldingDeviceRepo;
import com.ht.iot.collection.biz.IotDeviceInfoParser;
import com.ht.iot.collection.biz.LaunchingExecutor;
import com.ht.iot.collection.model.HoldingDevice;
import com.ht.iot.collection.model.LaunchedDlt645Device;
import com.ht.iot.collection.model.dto.Dlt645Data;
import com.ht.iot.collection.model.dto.UpdateDeviceInfoDto;
import com.ht.iot.collection.model.dto.UpdateDlt645DeviceInfoDto;
import com.ht.iot.collection.model.dto.UpdateModbusDeviceInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class DeviceMgmBizService {

//    @Autowired
//    private ActiveGwRepo activeGwRepo;

    @Autowired
    private HoldingDeviceRepo deviceRepo;

    @Autowired
    private LaunchingExecutor launchingExecutor;

    @Autowired
    private IotDeviceInfoParser deviceInfoParser;

    public Mono<String> updateModbusDevice(UpdateModbusDeviceInfoDto param) {

        return Mono.just(param)
            .map(p -> deviceInfoParser.parseModbusDeviceInfo(p))  // 解析地址文件和字段配置文件
            .doOnNext(d -> { // 加入到采集执行器
                deviceRepo.updateDevice(d);
            })
            .map(HoldingDevice::getDno);
    }


    public Mono<String> updateDlt645Device(UpdateDlt645DeviceInfoDto param) {

        return Mono.just(param)
            .map(p -> deviceInfoParser.parseDlt645DeviceInfo(p))  // 解析地址文件和字段配置文件
            .doOnNext(d -> { // 加入到采集执行器
                deviceRepo.updateDevice(d);
            })
            .map(HoldingDevice::getDno);
    }

    public Mono<String> addDlt645WriteMsg(String param) {

        return Mono.just(param)
            .map(p -> deviceInfoParser.parseDlt645WriteMsg(p))  // 解析地址文件和字段配置文件
            .doOnNext(d -> this.addDlt645WriteMsg(d))
            .map(LaunchedDlt645Device::getDno);
    }

    private void addDlt645WriteMsg(LaunchedDlt645Device device) {
        this.launchingExecutor.addDlt645WriteMsg(device);
    }

    public void updateDeviceBasicInfo(UpdateDeviceInfoDto param) {
        deviceRepo.updateDeviceBasicInfo(param);
    }

    public void removeDevice(String dno) {
        deviceRepo.removeDevice(dno);
    }
}
