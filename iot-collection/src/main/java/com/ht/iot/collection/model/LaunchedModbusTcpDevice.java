package com.ht.iot.collection.model;

import com.ht.iot.collection.model.type.ModbusTcpSocketType;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.RandomUtils;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class LaunchedModbusTcpDevice extends LaunchedModbusRtuDevice {

//    private ModbusTcpChannel channel;

    private ModbusTcpSocketType socketType;

    private String peerIp;

    private Integer peerPort;


    private AtomicInteger seqNum = new AtomicInteger(RandomUtils.nextInt());
}
