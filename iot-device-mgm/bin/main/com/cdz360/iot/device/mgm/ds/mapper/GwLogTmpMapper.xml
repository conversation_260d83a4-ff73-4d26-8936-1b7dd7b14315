<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.device.mgm.ds.mapper.GwLogTmpMapper">

    <insert id="batchInsert" >
        insert into
        t_gw_log_tmp(channelId, clientId, clientIp, eventIndex, eventType, time, gwno, createTime, updateTime
        )
        <foreach collection="logs" open="values" close=""
                 separator="," item="log">
            (#{log.channelId}, #{log.clientId}, #{log.clientIp}, #{log.eventIndex}, #{log.eventType}, #{log.time}, #{log.gwno},
            now(), now()
            )
        </foreach>
    </insert>

    <select id="getGroupGwno" resultType="java.lang.String">
        select gwno from t_gw_log_tmp group by gwno
    </select>
</mapper>