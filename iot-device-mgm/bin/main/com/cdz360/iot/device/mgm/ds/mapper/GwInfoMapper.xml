<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.device.mgm.ds.mapper.GwInfoMapper">


<!--    <select id="listGwBySiteIds" resultType="com.cdz360.iot.model.site.po.GwInfoPo">-->
<!--        select gw.*-->
<!--        from t_gw_info gw-->
<!--        where gw.enable = true-->
<!--        and gw.siteId in-->
<!--        <foreach collection="siteIds" item="siteId" open="(" close=")" separator=",">-->
<!--            #{siteId}-->
<!--        </foreach>-->
<!--    </select>-->

    <resultMap type="com.cdz360.iot.model.site.dto.GwInfoDto" id="gwInfoDto">
        <id column="id" property="id"/>
        <result column="ip" property="ip"/>
        <result column="lon" property="lon"/>
        <result column="lat" property="lat"/>

        <result column="cityCode" property="cityCode"/>
        <result column="createTime" property="createTime"/>
        <result column="updateTime" property="updateTime"/>
    </resultMap>


    <update id="logicDelete">
        update t_gw_site_ref set `enable`=FALSE,updateTime=now() where gwno=#{oldGwno} and siteId=#{siteId};
    </update>

    <insert id="insertOrUpdate">
        INSERT into t_gw_site_ref(gwno,siteId,`enable`,createTime,updateTime)
        VALUES (#{currGwno},#{siteId},true,now(),now())
        on DUPLICATE key UPDATE `enable`=true, updateTime=now();
    </insert>
    
    <!--<select id="getGwInfoByDzSiteId" resultType="com.cdz360.iot.model.site.dto.SiteDto">-->
        <!--SELECT s.id,i.id gwId,re.gwno,i.`status` gwStatus from t_site s INNER JOIN t_gw_site_ref re on s.id=re.siteId INNER JOIN t_gw_info i on re.gwno=i.gwno AND re.`enable`=TRUE AND s.dzId=#{siteId}-->
    <!--</select>-->

    <select id="getGwInfoByDzSiteId" resultType="com.cdz360.iot.model.site.dto.SiteDto">
        SELECT
            s.id,
            i.id gwId,
            re.gwno,
            re.updateTime gwSiteRefUpdateTime,
            i.`status` gwStatus
        FROM
            (SELECT * from t_site where dzId=#{siteId}) s
        LEFT JOIN (SELECT * from t_gw_site_ref where `enable` = TRUE) re ON s.dzId = re.siteId
        LEFT JOIN t_gw_info i ON re.gwno = i.gwno;
    </select>

    <!--<select id="getNotBoundGwno" resultType="java.lang.String" >-->
        <!--SELECT gwno from t_gw_info where gwno NOT in (SELECT DISTINCT gwno from t_gw_site_ref where `enable`=TRUE) and cityCode=#{cityCode} and `status`='NORMAL' and `enable`=TRUE;-->
    <!--</select>-->

    <select id="getNotBoundGwno" resultType="java.lang.String" >
        select A.gwno from t_gw_info A left join (SELECT DISTINCT gwno from t_gw_site_ref where `enable`=TRUE) B on A.gwno=B.gwno
        where A.cityCode=#{cityCode} and A.`status`='NORMAL' and A.`enable`=TRUE and B.gwno is null;
    </select>

    <select id="getCountBySiteId" resultType="java.lang.Integer">
        SELECT count(*) from t_site where dzId=#{siteId} and status!=0;
    </select>

    <select id="getCountByGwnoAndStatus" resultType="java.lang.Integer">
        SELECT count(*) from t_gw_info where gwno=#{gwno} and `status`='NORMAL' and `enable`=TRUE;
    </select>

    <select id="getCountBySiteIdAndTrue" resultType="java.lang.Integer">
        SELECT count(*) from t_gw_site_ref where siteId=#{siteId} and `enable`=TRUE;
    </select>

    <select id="getCountByGwnoAndTrue" resultType="java.lang.Integer">
        SELECT count(*) from t_gw_site_ref where gwno=#{gwno} and `enable`=TRUE;
    </select>

    <update id="updateFalseBySiteId">
        UPDATE t_gw_site_ref set `enable`=FALSE where siteId=#{siteId};
    </update>

    <select id="findGwnoBySiteIdAndTrue" resultType="java.lang.String">
        SELECT gwno from t_gw_site_ref where siteId=#{siteId} AND `enable`=TRUE;
    </select>
</mapper>