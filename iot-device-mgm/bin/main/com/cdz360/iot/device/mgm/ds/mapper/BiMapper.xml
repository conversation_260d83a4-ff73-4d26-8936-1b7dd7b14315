<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.device.mgm.ds.mapper.BiMapper">

  <select id="getEvseStatusBi" resultType="com.cdz360.iot.device.mgm.model.bi.vo.EvseStatusBi">
    select count(*) as num, evseStatus as status, supply as supplyType from t_evse
    where 1=1
    <if test="null != bizStatus">
      and bizStatus = #{bizStatus.code}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and siteId = #{siteId}
    </if>
    group by evseStatus, supply
  </select>


  <select id="getPlugStatusBi" resultType="com.cdz360.iot.device.mgm.model.bi.vo.PlugStatusBi">
    select count(*) as num, plugStatus as status, t_evse.supply as supplyType from t_plug
    left join t_evse on t_plug.evseId = t_evse.evseId
    where 1=1
    <if test="null != bizStatus">
      and t_evse.bizStatus = #{bizStatus.code}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and t_evse.siteId = #{siteId}
    </if>
    group by t_plug.plugStatus, t_evse.supply
  </select>

  <select id="getEvseSupplyBi" resultType="com.cdz360.iot.device.mgm.model.bi.vo.EvseSupplyBi">
    select count(*) as num, t_evse.supply as supplyType from t_evse
    where 1=1
    <if test="null != bizStatus">
      and bizStatus = #{bizStatus.code}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and siteId = #{siteId}
    </if>
    group by supply
  </select>


  <select id="getPlugSupplyBi" resultType="com.cdz360.iot.device.mgm.model.bi.vo.EvseSupplyBi">
    select count(*) as num, t_evse.supply as supplyType from t_plug
    left join t_evse on t_plug.evseId = t_evse.evseId
    where 1=1
    <if test="null != bizStatus">
      and t_evse.bizStatus = #{bizStatus.code}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and t_evse.siteId = #{siteId}
    </if>
    group by t_evse.supply
  </select>

</mapper>