package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.EmuService;
import com.cdz360.iot.model.gw.param.ListUpgradeLogParam;
import com.cdz360.iot.model.gw.vo.UpgradeLogVo;
import com.cdz360.iot.model.pv.dto.UpdateCtrlDto;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import com.cdz360.iot.model.site.vo.GwInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "Emu相关接口", description = "微网控制器")
@RequestMapping("/device/emu/")
public class EmuRest {

    @Autowired
    private EmuService emuService;

    @Operation(summary = "新增控制器信息")
    @PostMapping(value = "/addEmu")
    public Mono<BaseResponse> addEmu(@RequestBody UpdateCtrlDto param) {
        log.info("新增控制器信息: param = {}", JsonUtils.toJsonString(param));
        return emuService.addEmu(param)
                .map(res -> RestUtils.success());
    }



    @Operation(summary = "更新EMU")
    @PostMapping(value = "/updateEmu")
    public Mono<BaseResponse> updateEmu(@RequestBody UpdateCtrlDto param) {
        log.info("更新EMU: param = {}", JsonUtils.toJsonString(param));
        return emuService.updateEmu(param)
                .map(res -> RestUtils.success());
    }

    @Operation(summary = "获取EMU")
    @GetMapping(value = "/getEmu")
    public Mono<ObjectResponse<GwInfoVo>> getEmu(
            @Parameter(name = "微网控制器编号") @RequestParam(value = "gwno") String gwno) {
        log.info("获取EMU: gwno = {}", gwno);
        return emuService.getEmu(gwno)
                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "删除EMU")
    @PostMapping(value = "/removeEmu")
    public Mono<BaseResponse> removeEmu(
            @Parameter(name = "场站ID", required = true) @RequestParam(value = "siteId") String siteId,
            @Parameter(name = "控制器编号(网关编号)", required = true) @RequestParam(value = "gwno") String gwno) {
        log.info("删除EMU: siteId = {}, gwno = {}", siteId, gwno);
        return emuService.removeEmu(siteId, gwno)
                .map(res -> RestUtils.success());
    }

    @Operation(summary = "获取EMU列表")
    @PostMapping(value = "/findEmuList")
    public Mono<ListResponse<GwInfoVo>> findEmuList(@RequestBody ListCtrlParam param) {
        log.info("获取EMU列表: param = {}", JsonUtils.toJsonString(param));
        return emuService.findEmuList(param);
    }

    @Operation(summary = "EMU升级记录")
    @PostMapping(value = "/upgradeLogList")
    public Mono<ListResponse<UpgradeLogVo>> upgradeLogList(@RequestBody ListUpgradeLogParam param) {
        log.info("EMU升级记录: {}", JsonUtils.toJsonString(param));
        return emuService.upgradeLogList(param);
    }
}
