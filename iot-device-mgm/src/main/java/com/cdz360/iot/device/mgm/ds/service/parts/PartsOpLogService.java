package com.cdz360.iot.device.mgm.ds.service.parts;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.PartsOpLogRoDs;
import com.cdz360.iot.ds.rw.PartsOpLogRwDs;
import com.cdz360.iot.model.parts.dto.PartsImportItem;
import com.cdz360.iot.model.parts.param.PartsBrokenParam;
import com.cdz360.iot.model.parts.param.PartsEditParam;
import com.cdz360.iot.model.parts.param.PartsOpBaseParam;
import com.cdz360.iot.model.parts.param.PartsOpLogParam;
import com.cdz360.iot.model.parts.param.PartsRollbackParam;
import com.cdz360.iot.model.parts.param.PartsTransReviewParam;
import com.cdz360.iot.model.parts.po.PartsOpLogPo;
import com.cdz360.iot.model.parts.po.PartsPo;
import com.cdz360.iot.model.parts.type.PartsOpType;
import com.cdz360.iot.model.parts.type.PartsStatus;
import com.cdz360.iot.model.parts.vo.PartsOpLogVo;
import com.cdz360.iot.model.parts.vo.PartsVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PartsOpLogService {

    @Autowired
    private PartsOpLogRoDs partsOpLogRoDs;

    @Autowired
    private PartsOpLogRwDs partsOpLogRwDs;

    private void addPartsOpLog(PartsOpLogPo logPo, String err) {
        final boolean b = partsOpLogRwDs.insertPartsOpLog(logPo);
        if (!b) {
            log.warn(err + ": {}", JsonUtils.toJsonString(logPo));
        }
    }

    @Async
    public void partsTransport(PartsVo x, PartsImportItem param) {
        PartsOpLogPo logPo = new PartsOpLogPo()
            .setPartsCode(x.getCode())
            .setOpUid(param.getOpUid())
            .setOpType(PartsOpType.STORE_2_SEND)
            .detail(param.getExpressName(), param.getExpressNo());
        this.addPartsOpLog(logPo, "物料发货日志新增失败");
    }

    @Async
    public void partsReceive(PartsVo x, Long opUid) {
        PartsOpLogPo logPo = new PartsOpLogPo()
            .setPartsCode(x.getCode())
            .setOpUid(opUid)
            .setOpType(PartsOpType.RECEIVE_)
            .detailStatus(x.getStatus().name());
        this.addPartsOpLog(logPo, "物料退回签收日志新增失败");
    }

    @Async
    public void partsBroken(List<PartsPo> list, PartsBrokenParam param) {
        list.forEach(x -> {
            PartsOpLogPo logPo = new PartsOpLogPo()
                .setPartsCode(x.getCode())
                .setOpUid(param.getOpUid())
                .setOpType(PartsOpType.STORE_BROKEN)
                .detailStatus(x.getStatus().name(), PartsStatus.DISCARD.name());
            this.addPartsOpLog(logPo, "物料报废日志新增失败");
        });
    }

    @Async
    public void partsRollback(PartsPo x, PartsRollbackParam param) {
        PartsOpLogPo logPo = new PartsOpLogPo()
            .setPartsCode(x.getCode())
            .setOpUid(param.getOpUid())
            .setOpType(PartsOpType.STORE_ROLLBACK)
            .detail(param.getExpressName(), param.getExpressNo());
        this.addPartsOpLog(logPo, "物料报废日志新增失败");
    }

    @Async
    public void partsChangeStatus(List<PartsPo> list, PartsEditParam param) {
        list.forEach(x -> {
            if (!param.getToStatus().equals(x.getStatus())) {
                PartsOpLogPo logPo = new PartsOpLogPo()
                    .setPartsCode(x.getCode())
                    .setOpUid(param.getOpUid())
                    .setOpType(PartsOpType.STATUS_MODIFY)
                    .detailStatus(x.getStatus().name(), param.getToStatus().name());
                this.addPartsOpLog(logPo, "物料状态变更日志新增失败");
            }
        });
    }

    @Async
    public void partsTransReview(PartsVo x, PartsTransReviewParam param) {
        if (Boolean.TRUE.equals(param.getAgree())) {
            PartsOpLogPo logPo = new PartsOpLogPo()
                .setPartsCode(x.getCode())
                .setOpUid(param.getOpUid())
                .setOpType(PartsOpType.APPROVE_AND_SEND)
                .detail(param.getExpressName(), param.getExpressNo());
            this.addPartsOpLog(logPo, "物料同意调拨日志新增失败");
        }
    }

    @Async
    public void partsTransApply(PartsVo x, PartsOpBaseParam param) {
        PartsOpLogPo logPo = new PartsOpLogPo()
            .setPartsCode(x.getCode())
            .setOpUid(param.getOpUid())
            .setOpType(PartsOpType.APPLY_4_STORE)
            .detailStatus(x.getStatus().name());
        this.addPartsOpLog(logPo, "物料申请调拨日志新增失败");
    }

    @Async
    public void partsTransApplyCancel(PartsVo x, PartsOpBaseParam param) {
    }

    @Async
    public void partsYwOrder(PartsOpLogParam param) {
        this.partsYwOrder(param.getCode(), param.getOpUid(),
            param.getYwOrderNo(), param.getInStorage());
    }

    public void partsYwOrder(String partsCode, Long uid, String ywOrderNo, Boolean in) {
        IotAssert.isNotBlank(partsCode, "物料ID不能为空");
        IotAssert.isNotNull(uid, "操作人ID不能为空");
        IotAssert.isNotNull(in, "入库或出库标识不能为空");
        IotAssert.isNotBlank(ywOrderNo, "关联运维工单号不能为空");

        PartsOpLogPo logPo = new PartsOpLogPo()
            .setPartsCode(partsCode)
            .setOpUid(uid)
            .setOpType(in ? PartsOpType.EVSE_2_STORE : PartsOpType.STORE_2_EVSE)
            .detailOrder(ywOrderNo);
        this.addPartsOpLog(logPo, "物料工单关联日志新增失败");
    }

    public List<PartsOpLogVo> getPartsOpLog(String code) {
        IotAssert.isNotBlank(code, "物料ID不能为空");
        return partsOpLogRoDs.getAllByPartsCode(code);
    }
}
