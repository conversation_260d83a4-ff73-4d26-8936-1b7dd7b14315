package com.cdz360.iot.device.mgm.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.model.park.param.PlugStatusEventLogParam;
import com.cdz360.iot.model.park.vo.ParkingLockVo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_IOT_PARK,
    fallbackFactory = ParkFeignHystrix.class)
public interface ParkFeignClient {

    // 向地锁云查询地锁
    @GetMapping("/park/parkingLock/lookForLock")
    Mono<ObjectResponse<ParkingLockVo>> lookForLock(
        @RequestParam("siteId") String siteId,
        @RequestParam("remoteLockId") String remoteLockId);

    // 枪头状态变更事件日志
    @Operation(summary = "枪头状态变更事件日志")
    @PostMapping(value = "/park/parkingLock/eventLog/plugStatus")
    Mono<BaseResponse> plugStatusEventLog(@RequestBody PlugStatusEventLogParam param);
}
