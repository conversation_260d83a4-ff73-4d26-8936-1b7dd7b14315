package com.cdz360.iot.device.mgm.dzds.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Interfacename IotDzCityApiClient
 * @Description TODO
 * @Date 2019/5/27
 * @Created by wangzheng
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_AUTH)
//@RequestMapping("/city")
public interface CityFeignClient {

    @GetMapping(value = "/city/getCityCode", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    ObjectResponse<String> getCityCode(@RequestParam(value = "cityName") String cityName);
}
