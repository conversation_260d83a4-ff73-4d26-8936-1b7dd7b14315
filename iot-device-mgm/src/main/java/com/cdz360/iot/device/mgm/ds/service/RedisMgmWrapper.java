package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.iot.common.base.IotCacheConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;

@Service
@Slf4j
public class RedisMgmWrapper {
    //缓存t_evse_bundle记录上传进度REDIS的KEY
    private static final String PROGRESS_KEY = IotCacheConstants.IOT_EVSE_BUNDLE_PROGRESS_KEY;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * @Description: 缓存对应bundleId的上传进度
     * @Author: JLei
     * @CreateDate: 15:00 2019/10/8
     */
    public void cacheEvseBundleProgress(Long bundleId, Integer progress) {
        if (progress == null) {
            return;
        }
        this.stringRedisTemplate.opsForHash().put(PROGRESS_KEY, String.valueOf(bundleId), String.valueOf(progress));
    }

    /**
     * @Description: 上传失败缓存上传进度为-1
     * @Author: JLei
     * @CreateDate: 15:00 2019/10/8
     */
    public void cacheEvseBundleProgressOnError(Long bundleId) {
        this.cacheEvseBundleProgress(bundleId, -1);
    }

    /**
     * @Description: 删除对应bundleId的上传进度
     * @Author: JLei
     * @CreateDate: 15:01 2019/10/8
     */
    public void delEvseBundleProgress(Long bundleId) {
        Set<String> bundleIdSet = Set.of(String.valueOf(bundleId));
        if (CollectionUtils.isEmpty(bundleIdSet)) {
            return;
        }
        Object[] keys = bundleIdSet.toArray(new Object[bundleIdSet.size()]);
        this.stringRedisTemplate.opsForHash().delete(PROGRESS_KEY, keys);
    }

    /**
     * @Description: 获取对应bundleId的上传进度
     * @Author: JLei
     * @CreateDate: 15:01 2019/10/8
     */
    public Integer getEvseBundleProgress(Long bundleId) {
        Object progress = this.stringRedisTemplate.opsForHash().get(PROGRESS_KEY, String.valueOf(bundleId));
        if (progress == null) {
            return 0;
        }
        return Integer.parseInt(progress.toString());
    }
}
