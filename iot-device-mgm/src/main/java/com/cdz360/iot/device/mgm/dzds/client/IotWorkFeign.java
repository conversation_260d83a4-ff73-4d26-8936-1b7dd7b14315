package com.cdz360.iot.device.mgm.dzds.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.iot.model.param.TransformerUpdateParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(DcConstants.KEY_FEIGN_IOT_WORKER)
public interface IotWorkFeign {
    @PostMapping("/iot/siteCtrl/cfg/get")
    BaseResponse cfgGet(@RequestParam(value = "ctrlNum") String ctrlNum);

    @PostMapping(value = "/iot/biz/transformer/sendUpdateMsg")
    BaseResponse sendUpdateMsg(@RequestBody TransformerUpdateParam req);
}
