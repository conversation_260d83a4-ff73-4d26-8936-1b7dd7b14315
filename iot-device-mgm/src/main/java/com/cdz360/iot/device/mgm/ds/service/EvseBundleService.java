package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.SortParam;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.model.es.type.hi.EssType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.device.mgm.dzds.client.IotDzAuthCoreApiClient;
import com.cdz360.iot.device.mgm.model.basic.param.UploadBundleParam;
import com.cdz360.iot.device.mgm.utils.FileUtils;
import com.cdz360.iot.device.mgm.utils.ZipUtils;
import com.cdz360.iot.ds.ro.EvseBundlePcQueryDs;
import com.cdz360.iot.ds.ro.EvseBundleQueryDs;
import com.cdz360.iot.ds.ro.EvseRoDs;
import com.cdz360.iot.ds.ro.UpgradeTaskDetailQueryDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.rw.EvseBundlePcOriginRwDs;
import com.cdz360.iot.ds.rw.EvseBundlePcRwDs;
import com.cdz360.iot.ds.rw.EvseBundleRwDs;
import com.cdz360.iot.model.base.ListResponse;
import com.cdz360.iot.model.config.UpgDownloadProperties;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.evse.EvseBundle;
import com.cdz360.iot.model.evse.EvseBundleContext;
import com.cdz360.iot.model.evse.EvseBundlePc;
import com.cdz360.iot.model.evse.EvseBundlePcOrigin;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.PC0X;
import com.cdz360.iot.model.evse.UpgradePgContext;
import com.cdz360.iot.model.evse.dto.EvseBundleDto;
import com.cdz360.iot.model.evse.dto.PC0XDto;
import com.cdz360.iot.model.evse.param.EvseBundleParam;
import com.cdz360.iot.model.evse.param.UpgradeStatusParam;
import com.cdz360.iot.model.evse.type.BundleType;
import com.cdz360.iot.model.evse.type.EvseVendor;
import com.cdz360.iot.model.evse.upgrade.UpdateTaskStatusEnum;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;


/**
 * @ClassName： EvseBundleService
 * @Description: 桩升级包信息业务实现层
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/18 16:18
 */
@Service
@Slf4j
@EnableConfigurationProperties(UpgDownloadProperties.class)
public class EvseBundleService {

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private EvseRoDs evseRoDs;
    @Autowired
    private EvseBundleQueryDs evseBundleQueryDs;
    @Autowired
    private EvseBundleRwDs evseBundleRwDs;
    @Autowired
    private EvseBundlePcRwDs evseBundlePcRwDs;
    @Autowired
    private EvseBundlePcQueryDs evseBundlePcQueryDs;
    @Autowired
    private EvseBundlePcOriginRwDs evseBundlePcOriginRwDs;
    @Autowired
    private UpgDownloadProperties upgDownloadProperties;
    @Autowired
    private UpgradeTaskDetailQueryDs upgradeTaskDetailQueryDs;
    @Autowired
    private IotDzAuthCoreApiClient iotDzAuthCoreApiClient;
    @Autowired
    private RedisMgmWrapper redisMgmWrapper;

    /**
     * @Description: 桩升级包插入
     * @Author: JLei
     * @CreateDate: 18:59 2019/9/18
     */
    public long insertEvseBundle(EvseBundleContext evseBundleContext, long bundleSize,
        EvseVendor vendor) {
        log.info("根据自描述文件解析内容插入数据库开始。evseBundleContext = {}", evseBundleContext);
        Integer protocol = evseBundleContext.getProtocol();
        Long version = evseBundleContext.getVer();
        Long opId = evseBundleContext.getOpId();

        Assert.notNull(protocol, "桩升级包协议版本不能为空");
        Assert.notNull(version, "桩升级包版本号不能为空");
        Assert.notNull(opId, "桩升级包操作人ID不能为空");

        // 检查数据库中是否有同版本上传成功的升级包记录
        List<EvseBundle> evseBundles = evseBundleQueryDs.selectByVersion(version,
            BundleType.EVSE_SOFT, 1);
        if (CollectionUtils.isNotEmpty(evseBundles)) {
            Optional<EvseBundle> evseBundleOpt = evseBundles.stream().filter(evseBundle ->
                redisMgmWrapper.getEvseBundleProgress(evseBundle.getId()) == 100).findFirst();
            Assert.isTrue(evseBundleOpt.isEmpty(), "此版本已存在上传成功桩升级包");
        }

        // 升级要点
        EvseBundle evseBundleForUpsert = new EvseBundle()
            .setVersion(version)
            .setType(BundleType.EVSE_SOFT)
            .setVendor(vendor)
            .setBundleSize(bundleSize)
            .setFileName(evseBundleContext.getFileName())
            .setReleaseNote(evseBundleContext.getReleaseNote())
            .setEnable(true)
            .setOpId(opId)
            .setOpName(evseBundleContext.getOpName())
            .setProtocol(protocol)
            .setContext(evseBundleContext.getContext());

        List<EvseBundle> disableEvseBundle = evseBundleQueryDs.selectByVersion(version,
            BundleType.EVSE_SOFT, 0);
        if (CollectionUtils.isNotEmpty(disableEvseBundle)) {
            evseBundleForUpsert.setId(disableEvseBundle.get(0).getId());
            evseBundleRwDs.updateByPrimaryKeySelective(evseBundleForUpsert);
        } else {
            List<EvseBundle> enableEvseBundle = evseBundleQueryDs.selectByVersion(version,
                BundleType.EVSE_SOFT, 1);
            IotAssert.isTrue(CollectionUtils.isEmpty(enableEvseBundle),
                "该升级包已存在，请确认，version: " + version);
            evseBundleRwDs.insertSelective(evseBundleForUpsert);
        }
        // 获取插入之后 bundleId
        Long bundleId = evseBundleForUpsert.getId();

        // 兼容多种协议升级包解析扩展
        switch (evseBundleContext.getProtocol()) {
            case 1:
                List<PC0X> pc0xList = evseBundleContext.getPcList();
                if (CollectionUtils.isEmpty(pc0xList)) {
                    throw new DcServiceException("桩升级包自描述文件内容不能为空");
                }
                // 批量插入EvseBundlePc
                List<EvseBundlePc> evseBundlePcList = this.batchInsertEvseBundlePc(pc0xList,
                    bundleId);
                // 批量插入EvseBundlePcOrigin
                this.batchInsertEvseBundlePcOrigin(evseBundlePcList);
                break;
            case 2:
                // TODO@JLei 待实现 2019/9/19 协议扩展
                break;
        }
        log.info("根据自描述文件内容插入桩升级信息结束");
        return bundleId;
    }

    public long insertUserEssPg(EvseBundleContext pgContext, long pgSize,
        EvseVendor vendor, UpgradePgContext descContext) {
        log.info("根据自描述文件解析内容插入数据库开始: context = {}", pgContext);
        Integer protocol = pgContext.getProtocol();
        Long version = pgContext.getVer();
        Long opId = pgContext.getOpId();

        Assert.notNull(protocol, "升级包协议版本不能为空");
        Assert.notNull(version, "升级包版本号不能为空");
        Assert.notNull(opId, "升级包操作人ID不能为空");

        // 检查数据库中是否有同版本上传成功的升级包记录
        List<EvseBundle> pgList = evseBundleQueryDs.selectByVersion(version,
            BundleType.USER_ESS, 1);
        if (CollectionUtils.isNotEmpty(pgList)) {
            Optional<EvseBundle> pgOp = pgList.stream().filter(pg ->
                redisMgmWrapper.getEvseBundleProgress(pg.getId()) == 100).findFirst();
            Assert.isTrue(pgOp.isEmpty(), "此版本已存在上传成功升级包");
        }

        // 升级要点
        EvseBundle pgUpdate = new EvseBundle()
            .setVersion(version)
            .setType(BundleType.USER_ESS)
            .setVendor(vendor)
            .setBundleSize(pgSize)
            .setFileName(pgContext.getFileName())
            .setReleaseNote(pgContext.getReleaseNote())
            .setEnable(true)
            .setOpId(opId)
            .setOpName(pgContext.getOpName())
            .setProtocol(protocol)
            .setContext(pgContext.getContext());

        List<EvseBundle> disablePgList = evseBundleQueryDs.selectByVersion(
            version, BundleType.USER_ESS, 0);
        if (CollectionUtils.isNotEmpty(disablePgList)) {
            pgUpdate.setId(disablePgList.get(0).getId());
            evseBundleRwDs.updateByPrimaryKeySelective(pgUpdate);
        } else {
            List<EvseBundle> enablePgList = evseBundleQueryDs.selectByVersion(
                version, BundleType.USER_ESS, 1);
            IotAssert.isTrue(CollectionUtils.isEmpty(enablePgList),
                "该升级包已存在，请确认，version: " + version);
            evseBundleRwDs.insertSelective(pgUpdate);
        }

        // 获取插入之后 bundleId
        Long bundleId = pgUpdate.getId();

        // 版本信息
        List<EvseBundlePc> pgPcList = descContext.getSubItems().stream().map(item -> {
            String pc0XPath = String.format("%s/%s", bundleId, item.getBinName());
            BigDecimal ver = new BigDecimal(item.getVer()).multiply(new BigDecimal(100));
            return new EvseBundlePc()
                .setBundleId(bundleId)
                .setPcName(item.getName())
                .setHwVer(ver.intValue())
                .setSwVer(ver.intValue())
                .setVendorCode(ver.intValue())
                .setPath(pc0XPath);
        }).collect(Collectors.toList());
        int count = evseBundlePcRwDs.batchInsert(pgPcList);

        log.info("根据自描述文件解析内容插入数据库开始结束: {}", bundleId);
        return bundleId;
    }

    /**
     * @Description: 主键查询
     * @Author: JLei
     * @CreateDate: 10:27 2019/10/9
     */
    public EvseBundle selectByPrimaryKey(Long id) {
        EvseBundle evseBundle = evseBundleQueryDs.selectByPrimaryKey(id);
        IotAssert.isNotNull(evseBundle, "所选定的升级包有问题，请先确认升级包是否正常，然后再尝试");
        evseBundle.setProgress(redisMgmWrapper.getEvseBundleProgress(evseBundle.getId()));
        return evseBundle;
    }

    /**
     * @Description: 根据bundleId查询EvseBundlePc
     * @Author: JLei
     * @CreateDate: 10:27 2019/10/9
     */
    public List<EvseBundlePc> selectEvseBundlePcList(Long bundleId) {
        return evseBundlePcQueryDs.selectByBundleId(bundleId);
    }

    /**
     * @Description: 桩升级包分页查询
     * @Author: JLei
     * @CreateDate: 18:59 2019/9/18
     */
    public ListResponse<EvseBundleDto> listEvseBundlePage(EvseBundleParam evseBundleParam) {
        log.info("分页查询桩升级信息列表开始");
        if (CollectionUtils.isEmpty(evseBundleParam.getSorts())) {
            SortParam createTimeSort = new SortParam().setColumns(List.of("createTime"))
                .setOrder(OrderType.desc);
            evseBundleParam.setSorts(List.of(createTimeSort));
        }

        if (evseBundleParam.getTypeList() != null && evseBundleParam.getTypeList()
            .contains(BundleType.USER_ESS)) {
            if (StringUtils.isNotBlank(evseBundleParam.getRestraintDno())) {
                EssPo ess = essRoDs.getByDno(evseBundleParam.getRestraintDno());
                if (null == ess) {
                    return new ListResponse<>();
                }

                // 暂时对应
                evseBundleParam.setProtocol(EssType.THREE_PHASE.equals(ess.getType()) ? 3 : 1);
            }
        }

        List<EvseBundle> evseBundles = evseBundleQueryDs.listEvseBundle(evseBundleParam);

        if (CollectionUtils.isNotEmpty(evseBundleParam.getEvseNoList())
            && Boolean.FALSE.equals(evseBundleParam.getPageFlag())) {
            // 查询桩信息，并筛选出不为空的model
            List<EvsePo> evsePoList = evseRoDs.selectByEvseIds(evseBundleParam.getEvseNoList());
            IotAssert.isTrue(CollectionUtils.isNotEmpty(evsePoList), "找不到桩信息");
            long count = evsePoList.stream().map(EvsePo::getSupply).distinct().count();
            if (count > 1) {
                throw new DcServiceException("无可用升级包");
            }

            List<String> evseModelSuffix = evsePoList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getModel()))
                .map(e -> {
                    String[] temp = e.getModel().split("-");
                    return temp[temp.length - 1];
                }).collect(Collectors.toList());

            // 筛选包含model的升级包（通过自描述文件中适配机型字段）
            if (CollectionUtils.isNotEmpty(evseModelSuffix)) {
                evseBundles = evseBundles.stream().filter(evseBundle -> {
                    EvseBundleContext evseBundleContext = JsonUtils.fromJson(
                        evseBundle.getContext(), EvseBundleContext.class);
                    if (evseBundleContext.getOrderMsg() != null
                        && evseBundleContext.getOrderMsg().getChargingPoleType() != null) {
                        return Arrays.asList(
                                evseBundleContext.getOrderMsg().getChargingPoleType().split(","))
                            .containsAll(evseModelSuffix);
                    }
                    return true;
                }).collect(Collectors.toList());
            }
        }

        List<EvseBundleDto> evseBundleDtoResult = evseBundles.stream().map(bundle -> {
            switch (bundle.getType()) {
                case USER_ESS:
                    return this.userEssMap2Dto(bundle);
                case EVSE_SOFT:
                    return this.evseMap2Dto(bundle);
                case MGC_SOFT:
                    return this.mgcMap2Dto(bundle);
                default:
                    log.error("升级包类型值数据异常: {}", JsonUtils.toJsonString(bundle));
                    throw new DcServiceException("升级包类型值数据异常");
            }
        }).collect(Collectors.toList());

        if (Boolean.FALSE.equals(evseBundleParam.getPageFlag())) {
            // 不分页查询上传成功的升级包
            evseBundleDtoResult = evseBundleDtoResult.stream()
                .filter(evseBundleDto -> redisMgmWrapper.getEvseBundleProgress(
                    evseBundleDto.getId()) == 100)
                .collect(Collectors.toList());
        }
        Long total = Boolean.FALSE.equals(evseBundleParam.getPageFlag()) ? null
            : evseBundleQueryDs.getTotals(evseBundleParam);
        log.info("分页查询桩升级信息列表结束");
        return new ListResponse<>(evseBundleDtoResult, total);
    }

    private EvseBundleDto mgcMap2Dto(EvseBundle bundle) {
        List<EvseBundlePc> pcList = evseBundlePcQueryDs.selectByBundleId(bundle.getId());
        List<PC0XDto> pcDtoList = pcList.stream()
            .map(pc -> {
                PC0XDto pc0XDto = new PC0XDto();
                BeanUtils.copyProperties(pc, pc0XDto);
                pc0XDto.setAdaptiveSws(pc.getSwVer().toString())
                    .setAdaptiveHws(pc.getHwVer().toString())
                    .setAdaptiveOders("")
                    .setPath(upgDownloadProperties.getDownloadPath(pc.getPath()));
                return pc0XDto;
            }).collect(Collectors.toList());

        EvseBundleDto evseBundleDto = new EvseBundleDto();
        BeanUtils.copyProperties(bundle, evseBundleDto);
        evseBundleDto.setPc0XList(pcDtoList);
        evseBundleDto.setProgress(redisMgmWrapper.getEvseBundleProgress(bundle.getId()));
        return evseBundleDto;
    }

    private EvseBundleDto userEssMap2Dto(EvseBundle bundle) {
        List<EvseBundlePc> pcList = evseBundlePcQueryDs.selectByBundleId(bundle.getId());
        List<PC0XDto> pcDtoList = pcList.stream()
            .map(pc -> {
                PC0XDto pc0XDto = new PC0XDto();
                pc0XDto.setBinName(pc.getPcName());
                pc0XDto.setSw(pc.getSwVer());
                pc0XDto.setPath(pc.getPath());
                return pc0XDto;
            }).collect(Collectors.toList());

        EvseBundleDto evseBundleDto = new EvseBundleDto();
        BeanUtils.copyProperties(bundle, evseBundleDto);
        evseBundleDto.setPc0XList(pcDtoList);
        evseBundleDto.setProgress(redisMgmWrapper.getEvseBundleProgress(bundle.getId()));
        return evseBundleDto;
    }

    private EvseBundleDto evseMap2Dto(EvseBundle bundle) {
        EvseBundleContext evseBundleContext = JsonUtils.fromJson(bundle.getContext(),
            EvseBundleContext.class);
        List<PC0XDto> pc0XDtoList = evseBundleContext.getPcList().stream().map(pc0X -> {
            PC0X.Adaptive adaptive = pc0X.getAdaptive();
            String adaptiveSws = adaptive.getSw().stream().map(sw -> sw.toString())
                .collect(Collectors.joining(","));
            String adaptiveHws = adaptive.getHw().stream().map(hw -> hw.toString())
                .collect(Collectors.joining(","));
            String adaptiveOders = adaptive.getOrder().stream().map(order -> order.toString())
                .collect(Collectors.joining(","));
            String pc0XPath = String.format("%s/%s", bundle.getId(), pc0X.getBinName());
            PC0XDto pc0XDto = new PC0XDto();
            BeanUtils.copyProperties(pc0X, pc0XDto);
            pc0XDto.setAdaptiveSws(adaptiveSws)
                .setAdaptiveHws(adaptiveHws)
                .setAdaptiveOders(adaptiveOders)
                .setPath(upgDownloadProperties.getDownloadPath(pc0XPath));
            return pc0XDto;
        }).collect(Collectors.toList());
        EvseBundleDto evseBundleDto = new EvseBundleDto();
        BeanUtils.copyProperties(bundle, evseBundleDto);
        evseBundleDto.setPc0XList(pc0XDtoList);
        evseBundleDto.setProgress(redisMgmWrapper.getEvseBundleProgress(bundle.getId()));
        return evseBundleDto;
    }

    /**
     * @Description: 桩升级包删除
     * @Author: JLei
     * @CreateDate: 18:59 2019/9/18
     */
    @Transactional(rollbackFor = Exception.class)
    public EvseBundle deleteEvseBundle(Long id) {
        log.info("桩升级包信息删除开始。id = {}", id);
        EvseBundle evseBundle = evseBundleQueryDs.selectByPrimaryKey(id);
        Assert.notNull(evseBundle, "要删除桩升级包记录不存在，请检查");
        Integer progress = redisMgmWrapper.getEvseBundleProgress(id);
        if (progress < 100 && progress > 0) {
            throw new DcServiceException("桩升级包正在上传中");
        }
        long updatingCount = upgradeTaskDetailQueryDs.countUpdatingEvses(
            UpdateTaskStatusEnum.UPDATING, id);
        if (updatingCount > 0) {
            throw new DcServiceException("此升级包存在升级中的桩，请在升级完成后删除");
        }
        Integer evseBundleProgress = redisMgmWrapper.getEvseBundleProgress(id);
        if (evseBundleProgress == -1) {
            // 上传失败 物理删除
            evseBundleRwDs.deleteById(id);
        } else {
            // 上传成功 逻辑删除
            evseBundleRwDs.updateEnableById(id);
        }
        //物理删除
        evseBundlePcRwDs.deleteByBundleId(id);
        evseBundlePcOriginRwDs.deleteByBundleId(id);
        //清空上传进度
        redisMgmWrapper.delEvseBundleProgress(id);
        log.info("桩升级包信息删除结束");
        return evseBundle;
    }

    public BaseResponse changeStatus(UpgradeStatusParam param) {
        param.entryCheck();
        EvseBundle update = new EvseBundle();
        update.setId(param.getId())
            .setStatus(param.getStatus())
            .setOpId(param.getOpId())
            .setOpName(param.getOpName());
        int i = evseBundleRwDs.updateByPrimaryKeySelective(update);
        return i > 0 ? RestUtils.success()
            : RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "操作失败");
    }


    @Transactional(rollbackFor = Exception.class)
    public void uploadMgcBundle(UploadBundleParam bundleParam, String zipPath, String zipName,
        long bundleSize) {
        log.info("微网控制器升级包上传开始。{}, {}, {}", bundleParam, zipPath, zipName);

        if (null == bundleParam.getSwVerCode()) {
            throw new DcArgumentException("请指定软件包目标版本号");
        }

        List<EvseBundle> bundleList = evseBundleQueryDs.selectByVersion(
            bundleParam.getSwVerCode(), BundleType.MGC_SOFT, 1);
        if (CollectionUtils.isNotEmpty(bundleList)) {
            Optional<EvseBundle> bundleOp = bundleList.stream().filter(bundle ->
                redisMgmWrapper.getEvseBundleProgress(bundle.getId()) == 100).findFirst();
            Assert.isTrue(bundleOp.isEmpty(), "此版本已存在上传成功升级包");
        }

        EvseBundle bundle = new EvseBundle()
            .setType(bundleParam.getType())
            .setVersion(bundleParam.getSwVerCode())
            .setBundleSize(bundleSize)
            .setFileName(zipName)
            .setReleaseNote(null)
            .setEnable(true)
            .setOpId(bundleParam.getOpId())
            .setOpName(bundleParam.getOpName())
            .setProtocol(1);

        // 包版本信息
        this.mgcSwVerInfo(zipPath, bundle);

        List<EvseBundle> disableBundle = evseBundleQueryDs.selectByVersion(
            bundleParam.getSwVerCode(), BundleType.MGC_SOFT, 0);
        if (CollectionUtils.isNotEmpty(disableBundle)) {
            bundle.setId(disableBundle.get(0).getId());
            evseBundleRwDs.updateByPrimaryKeySelective(bundle);
        } else {
            evseBundleRwDs.insertSelective(bundle);
        }
        Long bundleId = bundle.getId();

        String pc0XPath = String.format("%s/%s", bundleId, zipName);
        EvseBundlePc bundlePc = new EvseBundlePc()
            .setBundleId(bundleId)
            .setPcName("微网控制器软件")
            .setHwVer(0)
            .setSwVer(0)
            .setVendorCode(0)
            .setPath(pc0XPath)
            .setAdaptive(null);
        int cnt = evseBundlePcRwDs.batchInsert(List.of(bundlePc));

        this.upload2Ftp(zipPath, bundleId,
            upgDownloadProperties.getBasepath().concat("/" + pc0XPath));
    }

    private void upload2Ftp(String zipPath, Long bundleId, String destPath) {
        try {
            redisMgmWrapper.cacheEvseBundleProgress(bundleId, 50);
            org.apache.commons.io.FileUtils.copyFile(new File(zipPath), new File(destPath));
            redisMgmWrapper.cacheEvseBundleProgress(bundleId, 100);
        } catch (IOException e) {
            log.error("升级包上传异常: err = {}", e.getMessage(), e);
            redisMgmWrapper.cacheEvseBundleProgressOnError(bundleId);
            throw new DcServiceException(e.getMessage());
        }
    }

    private void mgcSwVerInfo(String zipPath, EvseBundle bundle) {
        try {
            InputStreamReader contextReader = ZipUtils.readFromZipFile(
                zipPath, IotConstants.JAR_BOOT_INF_GIT, null);
            StringBuilder contextSb = new StringBuilder("{");

            contextSb.append(String.format("\"ver\" = %s,", bundle.getVersion()));
            IOUtils.readLines(contextReader).forEach(context -> {
                // git.build.version=0.0.1-SNAPSHOT
                // git.commit.id=e2d4dd4c0b93513b15ffd120994c3b22721f6eca
                if (context.contains("git.build.version=")) {
                    log.debug("ver line : {}", context);
                    contextSb.append(String.format("\"swVer\" = %s", context.split("=")[1]));
                } else if (context.contains("git.commit.id=")) {
                    log.debug("git line : {}", context);
                    contextSb.append(",");
                    contextSb.append(String.format("\"commitId\" = %s", context.split("=")[1]));
                }
            });
            contextSb.append("}");
            IOUtils.closeQuietly(contextReader);

            log.info("升级包版本信息: {}", contextSb.toString());
            bundle.setContext(contextSb.toString());
        } catch (Exception e) {
            log.error("读取软件包信息异常: err = {}", e.getMessage(), e);
        }
    }


    /**
     * @Description: 桩升级包上传
     * @Author: JLei
     * @CreateDate: 18:59 2019/9/18
     */
    @Transactional(rollbackFor = Exception.class)
    public void uploadEvseBundle(UploadBundleParam bundleParam, String zipPath,
        String zipName, long bundleSize) throws Exception {
        log.info("桩升级包上传开始。{}, {}, {}", bundleParam, zipPath, zipName);

        // 从升级包压缩文件中加载自描述文件
        InputStreamReader contextReader = ZipUtils.readFromZipFile(
            zipPath, IotConstants.IOT_EVSE_BUNDLE_CONTEXT_TXT_NAME, null);
//        if (contextReader == null) {
//            throw new DcServiceException("桩升级包中获取自描述文件失败");
//        }
        // 解析自描述文件内容
        StringBuilder contextSb = new StringBuilder();
        IOUtils.readLines(contextReader).forEach(context -> {
            contextSb.append(context)
                .append(System.lineSeparator());
        });
        IOUtils.closeQuietly(contextReader);

        //自描述文件内容
        String evseBundleContextJson = contextSb.toString();
        if (StringUtils.isBlank(evseBundleContextJson)) {
            throw new DcServiceException("桩升级包自描述内容不能为空");
        }
        EvseBundleContext evseBundleContext = JsonUtils.fromJson(evseBundleContextJson,
            EvseBundleContext.class);
        Assert.notNull(evseBundleContext, "桩升级包自描述文件空解析");

//        // 获取登陆用户信息
//        ObjectResponse<SysUserVo> res = iotDzAuthCoreApiClient.getLoginUserByToken(new TokenRequest().setToken(token));
//        if (res == null || res.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
//            throw new DcServiceException(Objects.requireNonNull(res).getStatus(), res.getError());
//        }
//        if (res.getData() == null) {
//            throw new DcServiceException("token无效，获取用户信息失败");
//        }
        evseBundleContext.setFileName(zipName)
            .setContext(evseBundleContextJson)
            .setOpId(bundleParam.getOpId())
            .setOpName(bundleParam.getOpName());
        long bundleId = this.insertEvseBundle(evseBundleContext, bundleSize,
            bundleParam.getVendor());

        // 升级包压缩文件解压
        ZipUtils.unZipWithProgress(zipPath,
            upgDownloadProperties.getBasepath(bundleParam.getVendor()).concat("/" + bundleId),
            null,
            new ZipUtils.ProgressListener() {
                @Override
                public void onProgress(Integer progress) {
                    log.info("桩升级包上传中。bundleId = {}，progress = {}", bundleId, progress);
                    redisMgmWrapper.cacheEvseBundleProgress(bundleId, progress);
                }

                @Override
                public void onError(Exception e) {
                    log.error("桩升级包上传失败。errMsg = {}",
                        e.getMessage() == null ? "未知异常" : e.getMessage(), e);
                    redisMgmWrapper.cacheEvseBundleProgressOnError(bundleId);
                }
            });
        log.info("桩升级包上传结束");
    }

    @Transactional(rollbackFor = Exception.class)
    public void uploadUserEssPg(UploadBundleParam bundleParam, String zipPath,
        String zipName, long bundleSize) throws Exception {
        log.info("户储设备升级包上传: {}, {}, {}", bundleParam, zipPath, zipName);

        InputStreamReader contextReader = ZipUtils.readFromZipFile(
            zipPath, IotConstants.IOT_UPGRADE_PG_NAME, null);
        StringBuilder contextSb = new StringBuilder();
        IOUtils.readLines(contextReader).forEach(context -> {
            contextSb.append(context)
                .append(System.lineSeparator());
        });
        IOUtils.closeQuietly(contextReader);

        // 自描述文件内容
        String contextJson = contextSb.toString();
        if (StringUtils.isBlank(contextJson)) {
            throw new DcServiceException("升级包自描述内容不能为空");
        }
        UpgradePgContext descContext = JsonUtils.fromJson(contextJson, UpgradePgContext.class);
        Assert.notNull(descContext, "升级包自描述文件空解析");

        EvseBundleContext pgContext = new EvseBundleContext();
        pgContext.setFileName(zipName)
            .setProtocol(descContext.getProtocol())
            .setVer(descContext.getVer())
            .setContext(contextJson)
            .setOpId(bundleParam.getOpId())
            .setOpName(bundleParam.getOpName());
        long bundleId = this.insertUserEssPg(pgContext, bundleSize,
            bundleParam.getVendor(), descContext);

        // 升级包压缩文件解压
        ZipUtils.unZipWithProgress(zipPath,
            upgDownloadProperties.getBasepath(bundleParam.getVendor()).concat("/" + bundleId),
            null,
            new ZipUtils.ProgressListener() {
                @Override
                public void onProgress(Integer progress) {
                    log.info("升级包上传中。bundleId = {}，progress = {}", bundleId, progress);
                    redisMgmWrapper.cacheEvseBundleProgress(bundleId, progress);
                }

                @Override
                public void onError(Exception e) {
                    log.error("升级包上传失败。errMsg = {}",
                        e.getMessage() == null ? "未知异常" : e.getMessage(), e);
                    redisMgmWrapper.cacheEvseBundleProgressOnError(bundleId);
                }
            });
        log.info("户储设备升级包上传结束");
    }

    @Async
    public void deleteEvseBundleFtpDir(Long bundleId, EvseVendor vendor, String deleteDir) {
        String basepath = upgDownloadProperties.getBasepath(vendor);
        File sourceFile = new File(basepath.concat("/" + bundleId));
        File targetFile = new File(basepath.concat("/" + deleteDir));
        try {
            FileUtils.move(sourceFile, targetFile);
        } catch (IOException e) {
            log.error("deleteEvseBundleFtpDir error: {}", e.getMessage(), e);
            throw new DcServiceException("删除FTP中对应升级包异常");
        }
    }

    /**
     * 批量插入EvseBundlePc
     *
     * @param pc0xList
     * @param bundleId
     * @return
     */
    public List<EvseBundlePc> batchInsertEvseBundlePc(List<PC0X> pc0xList, Long bundleId) {
        List<EvseBundlePc> evseBundlePcList = pc0xList.stream().map(pc0X -> {
            String pc0XPath = String.format("%s/%s", bundleId, pc0X.getBinName());
            EvseBundlePc evseBundlePc = new EvseBundlePc()
                .setBundleId(bundleId)
                .setPcName(pc0X.getType())
                .setHwVer(pc0X.getHw())
                .setSwVer(pc0X.getSw())
                .setVendorCode(pc0X.getOrder())
                .setPath(pc0XPath)
                .setAdaptive(pc0X.getAdaptive());
            return evseBundlePc;
        }).collect(Collectors.toList());

        int evseBundlePcCount = evseBundlePcRwDs.batchInsert(evseBundlePcList);
        log.info("插入升级包上的各个PC板详情。evseBundlePcCount = {}", evseBundlePcCount);
        return evseBundlePcList;
    }

    /**
     * @Description: 批量插入升级包源版本支持
     * @Author: JLei
     * @CreateDate: 10:24 2019/10/9
     */
    public void batchInsertEvseBundlePcOrigin(List<EvseBundlePc> evseBundlePcList) {
        List<EvseBundlePcOrigin> evseBundlePcOriginList = new ArrayList<>();
        evseBundlePcList.forEach(evseBundlePc -> {
            PC0X.Adaptive adaptive = evseBundlePc.getAdaptive();
            adaptive.getHw().stream().forEach(hw -> {
                adaptive.getSw().stream().forEach(sw -> {
                    adaptive.getOrder().stream().forEach(oder -> {
                        List<Integer> originList = List.of(hw, sw, oder);
                        EvseBundlePcOrigin evseBundlePcOrigin = new EvseBundlePcOrigin()
                            .setBundleId(evseBundlePc.getBundleId())
                            .setPcId(evseBundlePc.getId())
                            .setPcName(evseBundlePc.getPcName())
                            .setOrigin(StringUtils.join(originList, "-"));
                        evseBundlePcOriginList.add(evseBundlePcOrigin);
                    });
                });
            });
        });
        int evseBundlePcOriginCount = evseBundlePcOriginRwDs.batchInsert(evseBundlePcOriginList);
        log.info("插入升级包支持的源版本信息。evseBundlePcOriginCount = {}",
            evseBundlePcOriginCount);
    }

}
