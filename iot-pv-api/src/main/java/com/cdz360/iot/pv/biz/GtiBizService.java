package com.cdz360.iot.pv.biz;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.es.type.EquipCfgStatus;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.DevCfgRoDs;
import com.cdz360.iot.ds.ro.GtiCfgRoDs;
import com.cdz360.iot.ds.ro.GtiGridDispatchCfgRoDs;
import com.cdz360.iot.ds.ro.GtiRoDs;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.SrsRoDs;
import com.cdz360.iot.ds.rw.GtiRwDs;
import com.cdz360.iot.model.ess.po.DevCfgPo;
import com.cdz360.iot.model.ess.type.CfgType;
import com.cdz360.iot.model.pv.dto.GtiGridDispatchCfgDto;
import com.cdz360.iot.model.pv.dto.PvCfgDto;
import com.cdz360.iot.model.pv.po.GtiCfgPo;
import com.cdz360.iot.model.pv.po.GtiGridDispatchCfgPo;
import com.cdz360.iot.model.pv.po.GtiPo;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.srs.po.SrsPo;
import com.cdz360.iot.pv.biz.south.GtiMqService;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class GtiBizService {

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private GtiRoDs gtiRoDs;

    @Autowired
    private SrsRoDs srsRoDs;

    @Autowired
    private GtiRwDs gtiRwDs;

    @Autowired
    private DevCfgRoDs devCfgRoDs;

    @Autowired
    private GtiCfgRoDs gtiCfgRoDs;

    @Autowired
    private GtiGridDispatchCfgRoDs gtiGridDispatchCfgRoDs;

    @Autowired
    private GtiMqService gtiMqService;

    /**
     * 下发获取逆变器信息的指令
     *
     * @param siteIdIn
     * @param gtiNos
     * @return
     */
    public Mono<Boolean> getGtiInfo(String siteIdIn, List<String> gtiNos) {
        return Mono.just(siteIdIn)
            .doOnNext(siteId -> {
                GwInfoPo gwInfo = this.getGwInfo(siteId);
                this.gtiMqService.getGtiInfo(gwInfo, gtiNos);
            })
            .map(siteId -> Boolean.TRUE);
    }

    /**
     * 下发获取逆变器信息的指令
     *
     * @param dno 逆变器编号
     * @return
     */
    public Mono<Boolean> getGtiInfo(String dno) {
        if (StringUtils.isBlank(dno)) {
            throw new DcArgumentException("逆变器编号无效");
        }

        return Mono.just(dno)
            .doOnNext(d -> {
                GtiPo gti = gtiRoDs.getByDno(d);
                if (null == gti) {
                    throw new DcArgumentException("逆变器不存在或已失效");
                }

                if (StringUtils.isBlank(gti.getGwno())) {
                    throw new DcArgumentException("逆变器控制器编号无效");
                }

                GwInfoDto gw = gwSiteRefRoDs.getGwInfoByGwno(gti.getGwno(), true);
                if (null == gw) {
                    throw new DcArgumentException("逆变器所属控制器不存在或已失效");
                }

                this.gtiMqService.getGtiInfo(gw, List.of(dno));
            })
            .map(siteId -> Boolean.TRUE);
    }

    /**
     * 修改逆变器配置信息
     *
     * @return
     */
    public Mono<Boolean> modifyGtiCfg(String gwnoIn, String dnoIn, Long cfgId) {
        if (StringUtils.isBlank(gwnoIn)) {
            throw new DcArgumentException("控制器编号无效");
        }
        if (StringUtils.isBlank(dnoIn)) {
            throw new DcArgumentException("逆变器编号无效");
        }
        IotAssert.isNotNull(cfgId, "配置模板ID无效");

        return Mono.just(gwnoIn)
            .doOnNext(gwno -> {
                GwInfoPo gwInfo = gwSiteRefRoDs.getGwInfoByGwno(gwno, true);
                if (null == gwInfo) {
                    throw new DcArgumentException("控制器编号无效");
                }

                GtiPo gti = gtiRoDs.getByGwnoAndDno(gwno, dnoIn);
                if (null == gti) {
                    throw new DcArgumentException("控制器不存在指定的逆变器");
                }

                DevCfgPo devCfgPo = devCfgRoDs.getById(cfgId);
                if (null == devCfgPo) {
                    throw new DcArgumentException("配置ID无效");
                }

                SrsPo srsPo = srsRoDs.getOneByGwno(gwno);

                GtiPo update = new GtiPo();
                update.setDno(gti.getDno())
                    .setCfgId(cfgId)
                    .setCfgStatus(EquipCfgStatus.SEND_2_GW);
                gtiRwDs.updateGtiByDno(update);

                PvCfgDto dto = new PvCfgDto();
                dto.setId(gti.getSid())
                    .setSiteId(gti.getSiteId())
                    .setDeviceNo(gti.getDno());
                if (CfgType.GOOD_WE_GTI.equals(devCfgPo.getType())) {
                    GtiCfgPo cfg = gtiCfgRoDs.getById(cfgId);
                    dto.setSamplingTime(cfg.getSamplingTime())
                        .setBootVoltage(cfg.getBootVoltage())
                        .setMaxVoltage(cfg.getMaxVoltage())
                        .setMinVoltage(cfg.getMinVoltage())
                        .setMaxFrequency(cfg.getMaxFrequency())
                        .setMinFrequency(cfg.getMinFrequency());
                } else if (CfgType.HUAWEI_GTI.equals(devCfgPo.getType())) {
                    GtiGridDispatchCfgPo cfg = gtiGridDispatchCfgRoDs.getById(cfgId);
                    dto.setSamplingTime(cfg.getSamplingTime());

                    GtiGridDispatchCfgDto cfgDto = new GtiGridDispatchCfgDto();
                    BeanUtils.copyProperties(cfg, cfgDto);
                    dto.setGridDispatchCfg(cfgDto);
                } else {
                    throw new DcServiceException("不支持的模板类型");
                }

                this.gtiMqService.modifyGtiCfg(gwInfo,
                    devCfgPo.getVer(),
                    srsPo,
                    false,
                    List.of(dto));
            })
            .map(siteId -> Boolean.TRUE);
    }

    private GwInfoPo getGwInfo(String siteId) {
        GwInfoPo gwInfo = gwSiteRefRoDs.getGwInfo(siteId);
        if (gwInfo == null) {
            log.error("can't find gwno for siteId = {}", siteId);
            throw new DcArgumentException("请配置场站对应的网关");
        }
        return gwInfo;
    }

    public Mono<Boolean> uploadGtiDataFile(String siteIdIn, Date date) {
        return Mono.just(siteIdIn)
            .doOnNext(siteId -> {
                GwInfoPo gwInfo = gwSiteRefRoDs.getGwInfo(siteId);
                if (null == gwInfo) {
                    throw new DcArgumentException("控制器编号无效");
                }
                this.gtiMqService.uploadGtiDataFile(gwInfo, date, null);
            })
            .map(o -> Boolean.TRUE);

    }
}
