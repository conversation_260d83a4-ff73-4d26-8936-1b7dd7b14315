<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.monitor.mapper.AlarmCodeCfgMapper">

  <resultMap id="RESULT_MAP_ALARM_CODE_CFG" type="com.cdz360.iot.monitor.entity.po.AlarmCodeCfgPo">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <id column="equipType" property="equipType" jdbcType="INTEGER"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <id column="code" property="code" jdbcType="INTEGER"/>
    <id column="level" property="level" jdbcType="INTEGER"/>

    <id column="name" property="name" jdbcType="VARCHAR"/>
    <id column="desc" property="desc" jdbcType="VARCHAR"/>
    <id column="proposal" property="proposal" jdbcType="VARCHAR"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>


  <select id="getAlarmCodeCfg"
    resultMap="RESULT_MAP_ALARM_CODE_CFG">
    SELECT *
    FROM t_alarm_code_cfg cfg
    where cfg.equipType = #{equipType.code}
    and cfg.code = #{alarmCode}
  </select>


</mapper>
