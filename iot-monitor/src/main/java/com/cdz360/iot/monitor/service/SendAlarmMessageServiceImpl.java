package com.cdz360.iot.monitor.service;


import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.feign.biz.BizAuthFeignClient;
import com.cdz360.iot.feign.biz.BizMessageFeignClient;
import com.cdz360.iot.model.comm.vo.CommercialManageVo;
import com.cdz360.iot.model.sms.param.SendSmsParam;
import com.cdz360.iot.monitor.config.PropertiesConfig;
import com.cdz360.iot.monitor.entity.request.SysAlarmMsgRequest;
import com.cdz360.iot.monitor.entity.request.TransFinshAlarmRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

//import com.cdz360.base.utils.JsonUtils;
//import com.mamcharge.sdk.sms.enums.SmsPlatformEnum;
//import com.mamcharge.sdk.sms.model.BaseResponse;
//import com.mamcharge.sdk.sms.model.SmsMessage;
//import com.mamcharge.sdk.sms.service.impl.SmsClient;

/**
 * 发送支付宝生活号和短信服务接口
 *
 * <AUTHOR> Create on 2018/10/22 15:02
 */
@Slf4j
@Service
public class SendAlarmMessageServiceImpl implements SendAlarmMessageService {

    @Autowired
    private PropertiesConfig propertiesConfig;
//    @Autowired
//    private YpSmsService ypSmsService;

    @Autowired
    private BizMessageFeignClient bizMessageFeignClient;

    @Autowired
    private BizAuthFeignClient bizAuthFeignClient;
//
//    @Async
//    @Override
//    public void sendAlarmMobileMessage(SendMessageRequest sendMessageRequest) throws DcServiceException {
//        log.info("开始发送短信功能");
//        //单个手机号码发送
//        String phone = sendMessageRequest.getPhone();
//        //多个手机号码发送，单个为空时发送多个
//        List<String> phones = sendMessageRequest.getPhones();
//        //站点
//        String station = sendMessageRequest.getStation();
//        //告警名
//        String warningName = sendMessageRequest.getWarningName();
//        //枪头
//        Integer socket = sendMessageRequest.getSocket();
//        //桩
//        String pile = sendMessageRequest.getPile();
//        if (StringUtils.isBlank(phone) && (phones == null || phones.size() == 0)) {
//            throw new DcServiceException("站点" + station + "桩号" + sendMessageRequest.getPile() + "发送告警信息失败，手机号不能为空");
//        }
//        if (StringUtils.isBlank(pile)) {
//            throw new DcServiceException("站点" + station + "发送告警信息失败，桩号不能为空");
//        }
//        //短信发送key
//        String apiKey = propertiesConfig.getSmsApiKey();
//        //模板id
//        Long smsMouldId = propertiesConfig.getAlarmSmsModuleId();
//        //参数封装
//        Map<String, String> mapExtras = new HashMap<>();
//        mapExtras.put("siteName", station);
//        if (socket == null) {
//            mapExtras.put("boxCode", pile);
//        } else {
//            mapExtras.put("boxCode", pile + "序号" + socket);
//        }
//        if (sendMessageRequest.getWarningType() == AlarmEventTypeEnum.ALARM_MALFUNCTION.getValue()) {
//            //故障
//            mapExtras.put("warning", "故障，" + warningName);
//        } else {
//            //告警
//            mapExtras.put("warning", "告警，" + warningName);
//        }
//
//        //【任我充T】#siteName#场站（桩号 #boxCode#）发生#warning#
//        if (StringUtils.isNotBlank(phone)) {
//            boolean ret = this.ypSmsService.sendSms(apiKey, phone, String.valueOf(smsMouldId), mapExtras);
//            // 使用sdk方式发送云片短信。
//            log.info("单人告警短信发送结果。phone = {}，ret = {}", phone, ret);
//
//        } else {
//            //给多人发送
//            phones.stream().forEach(ph -> {
//                boolean ret = this.ypSmsService.sendSms(apiKey, ph, String.valueOf(smsMouldId), mapExtras);
//                //使用sdk方式发送云片短信。
//                log.info("多人告警短信发送结果。phone = {}，ret = {}", ph, ret);
//            });
//        }
//    }

    @Async
    @Override
    public void sendGwnoTimeoutAlarmMsg(SysAlarmMsgRequest request) throws DcServiceException {
        log.info("【网关登录超时】开始发送短信功能");
        // 多个手机号码发送，单个为空时发送多个
        List<String> phones = request.getPhones();
        String gwnos = request.getGwTimeoutList().stream().map(g -> g.getGwno()).distinct()
            .collect(Collectors.joining(","));
        List<SysAlarmMsgRequest.GwTimeoutPo> list = request.getGwTimeoutList();
        // 告警名称
        String warningName = request.getWarningName();
        if (CollectionUtils.isEmpty(phones)) {
            throw new DcServiceException(
                "【网关登录超时】网关 = " + gwnos + "发送网关登录超时告警失败，手机号不能为空");
        }
        if (StringUtils.isBlank(gwnos)) {
            throw new DcServiceException("【网关登录超时】发送告警信息失败，网关编号不能为空");
        }
        // 模板id
//        Long smsMouldId = propertiesConfig.getGwloginToutModuleId();
        //参数封装
        Map<String, String> mapExtras = new HashMap<>();
        mapExtras.put("gwno", gwnos);
        mapExtras.put("warningName", warningName);
        //【任我充T】网关编号: #gwno# 发生#warningName#告警
        this.sendMobileMsg(mapExtras, phones);
    }

    @Async
    @Override
    public void sendMicroServiceAlarmMsg(SysAlarmMsgRequest request) throws DcServiceException {
        log.info("【微服务告警】开始发送短信功能");
        // 多个手机号码发送，单个为空时发送多个
        List<String> phones = request.getPhones();
        String microServices = request.getAlerts().stream().map(alert -> {
            return String.format("应用名: %s,实例ID: %s ", alert.getAppName(),
                alert.getInstanceId());
        }).distinct().collect(Collectors.joining("; "));

        if (CollectionUtils.isEmpty(phones)) {
            throw new DcServiceException("【微服务告警】失败，手机号不能为空");
        }
        if (StringUtils.isBlank(microServices)) {
            throw new DcServiceException("【微服务告警】失败，微服务告警内容不能为空");
        }
        // 模板id
//        Long smsMouldId = propertiesConfig.getMicroServiceModuleId();
        //参数封装
        Map<String, String> mapExtras = new HashMap<>();
        mapExtras.put("microServices", microServices);
        mapExtras.put("message", "服务不可用");
        //【任我充T】微服务列表:#microServices# 告警:#message#
        this.sendMobileMsg(mapExtras, phones);
    }

    @Async
    @Override
    public void sendTranFinishAlarmMsg(TransFinshAlarmRequest request) throws DcServiceException {
        log.info("【资金周转告警】开始发送短信功能");
        // 多个手机号码发送，单个为空时发送多个
        List<String> phones = request.getPhones();
        if (CollectionUtils.isEmpty(phones)) {
            throw new DcServiceException("【资金周转告警】失败，手机号不能为空");
        }
        // 模板id
//        Long smsMouldId = propertiesConfig.getTransFinishModuleId();
        //参数封装
        Map<String, String> mapExtras = new HashMap<>();
        mapExtras.put("warningName", request.getWarningName());
        mapExtras.put("warningDesc",
            String.format("流水号：%s,错误信息：%s", request.getTradeNo(), request.getError()));
        //【任我充T】告警:#warnName# 告警描述:#warnDesc#
        this.sendMobileMsg(mapExtras, phones);
    }

    /**
     * 发送短信
     *
     * @param mapExtras
     * @param phones
     */
    @Override
    public void sendMobileMsg(Map<String, String> mapExtras, List<String> phones) {
        log.info("短信发送【开始】。mapExtras = {},phones = {}", mapExtras, phones);
        Long topCommId = 34474L;  // ???? 无处取值,暂时写死

        String cnt = JsonUtils.toJsonString(mapExtras);
        this.bizAuthFeignClient.getCommercialManage(topCommId)
            .flatMapMany(commRes -> Flux.fromIterable(phones)
                .map(p -> {
                    CommercialManageVo comm = commRes.getData();
                    SendSmsParam smsParam = new SendSmsParam();
                    smsParam.setContent(cnt)
                        .setPhone(p)
                        .setTopCommId(topCommId)
                        .setSmsOperatorType(comm.getSmsOperatorType())
                        .setSmsApiKey(comm.getSmsApiKey())
                        .setSmsApiPwd(comm.getSmsApiPwd());
                    return smsParam;
                }))
            .flatMap(smsParam -> this.bizMessageFeignClient.sendSms(smsParam))
            .blockLast();

//        // 单个手机号码发送
//        String phone = mapExtras.get("phone");
//        // 短信发送key
//        String apiKey = propertiesConfig.getSmsApiKey();
//        if (CollectionUtils.isNotEmpty(phones) && phones.size() == 1) {
//            boolean ret = this.ypSmsService.sendSms(apiKey, phones.get(0), smsMouldId.toString(), mapExtras);
//            // 使用sdk方式发送云片短信。
//            log.info("单人短信发送结果。phone = {}，ret = {}", phones.get(0), ret);
//        } else {
//            //给多人发送
//            phones.stream().forEach(ph -> {
//                boolean ret = this.ypSmsService.sendSms(apiKey, ph, smsMouldId.toString(), mapExtras);
//                //使用sdk方式发送云片短信。
//                log.info("多人短信发送结果。phone = {}，ret = {}", ph, ret);
//            });
//        }
    }

}
