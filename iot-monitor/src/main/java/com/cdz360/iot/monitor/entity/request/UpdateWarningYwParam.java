package com.cdz360.iot.monitor.entity.request;


import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@Schema(title = "告警分类查询入参")
public class UpdateWarningYwParam  {

    @Schema(description = "桩编号")
    private List<String> evseNoList;

    @Schema(description = "运维工单号")
    private String ywOrderNo;

    @Schema(description = "忽略的告警码")
    private List<String>  ignoreWarningCodeList;
}
