package com.cdz360.iot.monitor.entity.request;
//
//import com.cdz360.base.model.base.param.BaseListParam;
////import com.chargerlink.device.common.param.BaseRequest;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.io.Serializable;
//import java.util.Date;
//import java.util.List;
//
///**
// * 告警列表查询
// *
// * <AUTHOR>
// * @date Create on 2018/7/31 14:31
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class AlarmListRequest //extends BaseRequest
//    extends BaseListParam
//    implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * 告警开始时间
//     */
//    private Date startTime;
//
//    /**
//     * 告警结束时间
//     */
//    private Date endTime;
//
//    /**
//     * 告警类型{@link com.cdz360.iot.monitor.constant.AlarmTypeEnum}
//     */
//    private List<String> alarmTypeList;
//
//    /**
//     * 设备ID
//     */
//    private String deviceId;
//
//    /**
//     * 站点id
//     */
//    private List<String> siteIdList;
//
//    /**
//     * 代理商id
//     */
//    private Long businessId;
//
//    /**
//     * 代理商名称
//     */
//    private String businessName;
//
//    /**
//     * 告警设备类型{@link com.cdz360.iot.monitor.constant.DeviceAlarmTypeEnum}
//     */
//    private List<Integer> deviceTypeList;
//
//    /**
//     * 告警状态{@link com.cdz360.iot.model.alarm.type.AlarmStatusEnum}
//     */
//    private Integer alarmStatus;
//
//    /**
//     * 电子邮箱地址
//     */
//    private String emailAddress;
//
//    /**
//     * 站点ID
//     */
//    private String siteId;
//}
