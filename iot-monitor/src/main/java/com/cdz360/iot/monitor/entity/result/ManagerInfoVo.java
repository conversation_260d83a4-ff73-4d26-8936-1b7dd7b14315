package com.cdz360.iot.monitor.entity.result;

//
///**
// * 管理员账户信息
// *
// * <AUTHOR>
// * @date Create on 2017/11/23 9:38
// */
//@Data
//public class ManagerInfoVo implements Serializable {
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * 用户id
//     */
//    private Integer id;
//    /**
//     * 管理员类型
//     */
//    private Integer managerType;
//    /**
//     * 昵称
//     */
//    private String nickName;
//    /**
//     * 用户名
//     */
//    private String userName;
//    /**
//     * 用户所属省份<b>仅当managerType != MANAGER_COMMERCIAL时有效</b>
//     */
//    private String province;
//    /**
//     * 用户所属市<b>仅当managerType != MANAGER_COMMERCIAL时有效</b>
//     */
//    private String city;
//    /**
//     * 商户级别（商户级别分为集团商户、商户、子商户，目前只考虑商户和子商户）<br>
//     * <b>仅当managerType = MANAGER_COMMERCIAL时有效</b>
//     */
//    private Long commercialId;
//    /**
//     * 用于虚拟账号下面子账号集合
//     **/
//    private List<Long> merchantIds;
//    /**
//     * 物业Id
//     */
//    private Long merchantId;
//
//    /**
//     * 获取管理员账户类型枚举
//     *
//     * @return
//     */
//    public ManagerTypeEnum getManagerTypeEnum() {
//        for (ManagerTypeEnum typeEnum : ManagerTypeEnum.values()) {
//            if (typeEnum.getValue() == managerType) {
//                return typeEnum;
//            }
//        }
//        return null;
//    }
//
//}
