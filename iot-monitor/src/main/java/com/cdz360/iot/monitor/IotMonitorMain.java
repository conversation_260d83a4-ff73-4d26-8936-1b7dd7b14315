package com.cdz360.iot.monitor;

import com.netflix.discovery.EurekaClient;
import jakarta.annotation.PreDestroy;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import reactivefeign.spring.config.EnableReactiveFeignClients;
import reactor.core.publisher.Hooks;


//开启eureka客户端feign支持-调用外部接口时使用此注解，value为@FeignClient注解类所在的包路径
@EnableReactiveFeignClients(basePackages = {
    "com.cdz360.iot.feign.*"
})
@SpringBootApplication
@EnableTransactionManagement
@MapperScan(basePackages = {
    "com.cdz360.iot.monitor.mapper"
})
@ComponentScan(basePackages = {
    "com.cdz360.data.cache",
    "com.cdz360.iot.monitor"
})
@EnableDiscoveryClient(autoRegister = true)
public class IotMonitorMain {

    private static final Logger logger = LoggerFactory.getLogger(IotMonitorMain.class);

    @Autowired
    private EurekaClient discoveryClient;

    public static void main(String[] args) {
        logger.info("启动 iot-monitor ...");
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
        Hooks.enableContextLossTracking();
        new SpringApplicationBuilder(IotMonitorMain.class)
            .web(WebApplicationType.REACTIVE)
            .run(args);
    }

    @PreDestroy
    public void destroy() {
        logger.info("going to shutdown");

        discoveryClient.shutdown();
        logger.info(".....");
    }

}
