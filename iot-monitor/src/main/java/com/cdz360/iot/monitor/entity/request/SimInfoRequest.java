package com.cdz360.iot.monitor.entity.request;
//
//import com.chargerlink.device.common.param.BaseRequest;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.io.Serializable;
//import java.util.List;
//
///**
// * sim卡统计
// *
// * <AUTHOR>
// * @since 2018-11-8 10:10:04
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class SimInfoRequest extends BaseRequest implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * 代理商id
//     */
//    private Long commercialId;
//
//    /**
//     * 用于虚拟账号下面子账号集合
//     */
//    private List<Long> merchantIds;
//
//    /**
//     * 物业Id
//     */
//    private Long merchantId;
//
//    /**
//     * 用户所属省份
//     */
//    private String province;
//
//    /**
//     * 用户所属市
//     */
//    private String city;
//
//    /**
//     * sim卡运营商
//     */
//    private String agent;
//
//    /**
//     * 状态
//     */
//    private Integer status;
//
//    /**
//     * 最低流量
//     */
//    private Integer minFlow;
//}
