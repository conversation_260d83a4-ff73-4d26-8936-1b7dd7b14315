package com.cdz360.iot.monitor.constant;

import lombok.Getter;

/**
 * 用于告警的设备类型
 *
 * <AUTHOR>
 * @Date Create on 2018/7/24 19:51
 */
@Getter
public enum DeviceAlarmTypeEnum {

    ALARM_BOX(100, "充电桩告警"),
    ALARM_CHARGER(200, "插座告警"),
    ALARM_PARKING(300, "地锁告警"),
    ALARM_GEOMAGNETISM(400, "地磁告警"),
    ALARM_GATE(500, "闸门/门禁设备报警");

    private int value;
    private String lable;

    DeviceAlarmTypeEnum(int value, String lable) {
        this.value = value;
        this.lable = lable;
    }

}
