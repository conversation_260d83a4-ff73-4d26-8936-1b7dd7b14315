package com.cdz360.iot.monitor.config;

import com.cdz360.data.sync.constant.DcMqConstants;
import com.cdz360.iot.common.base.IotConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class MqListenerConfig {

    @Bean
    public Queue essInfoQueue() {
        return new Queue(IotConstants.MQ_IOT_QUEUE_ESS_INFO_MONITOR, true, false, true);
    }

    @Bean
    DirectExchange exchangeIot() {
        return new DirectExchange(DcMqConstants.MQ_EXCHANGE_NAME_IOT, true, true);
    }

    @Bean
    Binding bindingExchangeIotPlug(Queue essInfoQueue, DirectExchange exchangeIot) {

        return BindingBuilder.bind(essInfoQueue).to(exchangeIot).with(DcMqConstants.MQ_ROUTING_KEY_IOT);
    }
}
