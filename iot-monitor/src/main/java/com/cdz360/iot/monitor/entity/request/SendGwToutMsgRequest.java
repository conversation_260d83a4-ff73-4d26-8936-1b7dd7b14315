package com.cdz360.iot.monitor.entity.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Classname SendGwTimeoutMsgRequest
 * @Description 网关登录超时告警
 * <AUTHOR>
 * @Date 2019/8/20 17:23
 * @Email <EMAIL>
 */
@Data
public class SendGwToutMsgRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 手机号
     */
    String phone;

    /**
     * 一次发送多个的手机号
     */
    List<String> phones;

    /**
     * 网关编号
     */
    String gwno;

    /**
     * 桩
     */
    String ip;

    /**
     * 网关下场站数
     */
    int siteNum;

    /**
     * 别名
     */
    String mark;

    /**
     * 告警产生方式 0：桩端上报告警,1：桩端上报故障,2：告警逻辑生成告警 3 :定时任务监控网关登录超时
     * {@link com.cdz360.iot.monitor.constant.AlarmEventTypeEnum}
     */
    Integer warningType;
    /**
     * 发生的告警名
     */
    String warningName;
}
