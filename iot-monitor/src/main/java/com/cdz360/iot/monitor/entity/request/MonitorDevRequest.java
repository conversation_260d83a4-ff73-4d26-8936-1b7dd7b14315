package com.cdz360.iot.monitor.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 储能ESS上报告警信息
 */
@Accessors(chain = true)
@Schema(title = "储能信息")
@Data
public class MonitorDevRequest {

    /**
     * 故障设备列表
     */
    private List<MonitorErrorDevRequest> equipList;
    /**
     * 故障码
     */
    private Long errorCode;
}

