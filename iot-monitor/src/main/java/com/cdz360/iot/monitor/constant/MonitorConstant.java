package com.cdz360.iot.monitor.constant;

/**
 * 常量定义
 *
 * <AUTHOR>
 * @Date Create on 2018/7/24 19:52
 */
public interface MonitorConstant {

    /**
     * 监控平台用户token
     */
    String MONITOR_MANAGER_TOKEN = "token";
    /**
     * 监控平台告警下正在进行的订单状态
     */
    int CHARGER_ORDER_STATUS = 200;
    /**
     * 监控平台正在进行的插座告警状态
     */
    int WARNING_RECORD = 0;
    /**
     * 站点状态_已删除
     */
    int SITE_STATUS_DELETE = 0;
}
