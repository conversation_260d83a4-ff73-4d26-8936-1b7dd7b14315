package com.cdz360.iot.monitor.constant;

/**
 * 用户(权限)中心api调用常量
 *
 * @ClassName AuthCenterConstant
 * <AUTHOR>
 * @Description
 * @Date 2018.12.12
 */
public interface AuthCenterConstant {

    /**
     * token 获取 用户信息
     */
    String API_INFO_TOKEN = "/api/info/token";

    /**
     * 用户登录获取token
     */
    String DATA_COMMERCIALS_USER_LOGIN = "/api/login";
    /**
     * 用户退出登录
     */
    String DATA_COMMERCIALS_USER_LOGOUT = "/api/logout";
    /**
     * 根据token获取所属用户信息及商户id
     */
    String DATA_MERCHANT_COMMERCIALS_INFO = "/api/commercials/info/token";

}
