package com.cdz360.iot.monitor.entity.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 获取指定插座的详情
 *
 * <AUTHOR>
 * @Date Create on 2018/7/25 002510:26
 */
@Data
public class ConnectorDetailVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 插座id
     */
    private String id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 设备出厂编码
     */
    private String boxOutFctoryCode;

    /**
     * 订单时长
     */
    private String orderDuration;

    /**
     * 告警列表
     */
    private List alarmList;

    /**
     * 24小时功率/电压/电流(关联AC插座信息，需确认)
     * AcConnectorDataInfo
     */
    private List measureList;
}
