app:
  name: iot-meter-api

server:
  address: 0.0.0.0
  port: 8080
  use-forward-headers: true
  compression.enabled: true

spring:
  application:
    name: iot-meter-dev
  config:
    import: "configserver:http://oam-test.rnd.iot.renwochong.com"
  profiles:
    active: test01,common,rabbitmq,jdbc,redis,zipkin
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    config:
      label: test01

management:
  context-path: /admin
  security:
    enabled: false

netty:
  tcp-port: 9111
  boss-count: 1
  worker-count: 50
  keep-alive: true
  backlog: 100

eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:<EMAIL>:7001/eureka/,http://aaa:<EMAIL>/eureka/

feign:
  hystrix:
    enabled: true


hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 5000



logging:
  level:
    com.cdz360.iot: 'DEBUG'
    org.springframework: 'WARN'
    org.mybatis: 'DEBUG'


  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} - %logger{36}.%M\\(%line\\) - %msg%n"

springdoc:
  packagesToScan: com.cdz360.iot.meter.rest
  swagger-ui:
    path: /swagger-ui.html

baidu:
  ak: 3SX3AqbPYYGSOv2nCKoFHfpeSzaRuhAW
  ipLogLat: http://api.map.baidu.com/location/ip?ak={ak}&ip={ip}&coor={coor}
  coor: bd09ll