package com.cdz360.iot.meter.south;

import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageEncoder;

import java.util.List;

/**
 * 网关和电表间数据的组包
 */
@ChannelHandler.Sharable
public class IotEncoder extends MessageToMessageEncoder<byte[]> {
    @Override
    protected void encode(ChannelHandlerContext ctx, byte[] msg, List<Object> out) throws Exception {
        // TODO: 根据桩的协议做数据封包
        out.add(Unpooled.wrappedBuffer(msg));
    }
}
