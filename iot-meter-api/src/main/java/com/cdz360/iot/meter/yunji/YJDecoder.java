package com.cdz360.iot.meter.yunji;


import com.cdz360.iot.meter.utils.SeqGeneratorUtil;
import com.cdz360.iot.meter.yunji.qgdw376dot1.QGDW376Dot1Utils;
import com.cdz360.iot.meter.yunji.qgdw376dot1.model.QGDW376Dot1RawBody;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import io.netty.handler.codec.TooLongFrameException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class YJDecoder extends ByteToMessageDecoder {

    // 报文头
    private static final byte MSG_HEADER = (byte) 0x68;

    // 数据包最大长度
    private static final int MAX_FRAME_SIZE = 1024 * 4; // 4M

    @Autowired
    private YJChannelConnectionPool pool;

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf byteBuf, List<Object> out) throws Exception {
        String traceId = SeqGeneratorUtil.newStringId();
        int availableLength = byteBuf.readableBytes();

        // 安全操作
        if (availableLength > MAX_FRAME_SIZE) {
            log.error("traceId: {}, [{}] 客户端报文太大 !!! ", traceId, ctx.channel().remoteAddress().toString());
            resetClient(ctx.channel());//发送大量报文/或者从缓冲区收到大量报文直接踢掉
            throw new TooLongFrameException("Frame too big!");
        }


        if (availableLength < 3) {
            // 长度不够
            return;
        }


        String hexDump = ByteBufUtil.hexDump(byteBuf);
        byte[] msg = ByteBufUtil.decodeHexDump(hexDump);

        String channelKey = ctx.channel().remoteAddress().toString();
        log.info("traceId: {}, 收到报文。channelKey: {}, bytes: {}.", traceId, channelKey, hexDump);

        // 报文头判断
        byte header = msg[0];
        if (header != MSG_HEADER) {
            log.warn("traceId: {}, 报文头非法！ channelKey: {}, header: {}", traceId, channelKey, header);
            // 移动指针，丢第该帧
            byteBuf.readerIndex(availableLength);
            return;
        }

        // 校验长度
        int actualLength = QGDW376Dot1Utils.decodeLength(new byte[]{msg[1], msg[2]}) + 8;
        if (availableLength < actualLength) {
            // 还未读到实际的长度
            log.warn("traceId: {}, qyc测试打印: 还未读到实际长度", traceId);
            return;
        }


        byte[] dst = new byte[actualLength];
        byteBuf.readBytes(dst);
        QGDW376Dot1RawBody body = new QGDW376Dot1RawBody();
        body.setBody(dst);
        body.setTraceId(traceId);
        out.add(body);
        log.debug("traceId: {}, [{}] decoded end.", traceId, channelKey);
    }


    private void resetClient(Channel channel) {
        //idempotenceService.remove(channel.remoteAddress().toString());//踢出之前将前面发送的报文清空
        pool.removeConnection(channel.remoteAddress().toString());
        channel.close();
    }
}
