package com.cdz360.iot.meter.south.handler;

import com.cdz360.iot.meter.model.iot.IotBaseMsg;
import com.cdz360.iot.meter.model.type.CmdCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;

@Slf4j
@Service
public class ErrorHandler implements IotHandler {


    private static final CmdCode CMD_CODE = CmdCode.ERROR_RES;

    @Autowired
    private MeterHandlerFacade iotEvseFacade;


    @PostConstruct
    public void init() {
        this.iotEvseFacade.addProducer(CMD_CODE, this);
    }

    @Override
    public Mono<byte[]> process(IotBaseMsg msg) {
        log.info(">> traceId = {}", msg.getTraceId());

        return Mono.just(IotHandler.EMPTY);
    }
}
