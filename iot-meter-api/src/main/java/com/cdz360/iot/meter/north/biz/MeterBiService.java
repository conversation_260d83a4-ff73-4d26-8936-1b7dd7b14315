package com.cdz360.iot.meter.north.biz;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.MeterRecordRoDs;
import com.cdz360.iot.ds.ro.MeterRoDs;
import com.cdz360.iot.model.meter.dto.BiSiteMeterSummaryDto;
import com.cdz360.iot.model.meter.po.BiMeterSumPo;
import com.cdz360.iot.model.meter.vo.MeterEvseVo;
import com.cdz360.iot.model.param.MeterListParam;
import com.cdz360.iot.model.param.MeterRecordBiParam;
import com.cdz360.iot.model.param.SiteBiParam;
import com.cdz360.iot.model.type.SiteBiSampleType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Classname MeterBiService
 * @Description
 * @Date 11/8/2021 9:53 AM
 * @Created by Rafael
 */
@Slf4j
@Service
public class MeterBiService {
    @Autowired
    private MeterRecordRoDs meterRecordRoDs;

    @Autowired
    private MeterRoDs meterRoDs;

    public ListResponse<BiSiteMeterSummaryDto> getBiSiteMeterRecord(MeterRecordBiParam param) {

        IotAssert.isNotNull(param, "请传入参数");
        IotAssert.isNotNull(param.getSampleType(), "请传入采样单位，仅支持日、月");
        IotAssert.isNotNull(param.getFromTime(), "请传入开始时间");
        IotAssert.isNotNull(param.getToTime(), "请传入结束时间");

        if(CollectionUtils.isEmpty(param.getMeterIdList())) {
            log.info("电表列表为空，默认显示场站所有电表信息: {}", param.getSiteId());
            MeterListParam meterListParam = new MeterListParam();
            meterListParam.setSiteId(param.getSiteId());
            List<MeterEvseVo> meterList = meterRoDs.getMeterList(meterListParam);
            List<Long> meterIds = meterList.stream()
                    .map(MeterEvseVo::getId)
                    .collect(Collectors.toList());
            log.info("默认显示电表: {}", meterIds);
            param.setMeterIdList(meterIds);
        }

        List<BiSiteMeterSummaryDto> ret = meterRecordRoDs.getBiSiteMeterRecord(param);
        long total = ret.size();
        if(param.getStart() != null && param.getSize() != null && CollectionUtils.isNotEmpty(ret)) {
            ret = ret.stream()
                    .skip(param.getStart())
                    .limit(param.getSize())
                    .collect(Collectors.toList());
        }

        for(BiSiteMeterSummaryDto one : ret) {
            List<LocalDateTime> localDateTimes = SiteBiParam.reviseResult(param);
            List<BiMeterSumPo> summaryList = one.getBiSummaryList()
                    .stream()
                    .filter(e -> e.getDay() != null || e.getMonth() != null)
                    .collect(Collectors.toList());
            List<BiMeterSumPo> biMeterSumPos = localDateTimes.stream()
                    .map(e -> {
                        if (CollectionUtils.isNotEmpty(summaryList) &&
                                (e.equals(summaryList.get(0).getDay()) ||
                                        e.equals(summaryList.get(0).getMonth()))
                        ) {
                            return summaryList.remove(0);
                        } else {
                            BiMeterSumPo biMeterSumPo = new BiMeterSumPo();
                            if (SiteBiSampleType.MONTH.equals(param.getSampleType())) {
                                biMeterSumPo.setMonth(e);
                            } else {
                                biMeterSumPo.setDay(e);
                            }
                            return biMeterSumPo;
                        }
                    })
                    .collect(Collectors.toList());
            one.setBiSummaryList(biMeterSumPos);
        }

        return new ListResponse<>(ret, total);
    }
}