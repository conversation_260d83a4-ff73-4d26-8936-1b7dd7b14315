package com.cdz360.iot.meter.yunji.qgdw376dot1;

import cn.hutool.core.util.HexUtil;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.es.vo.MeterRtData;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.base.model.iot.vo.EssVo;
import com.cdz360.base.model.meter.vo.MeterAbcData;
import com.cdz360.base.model.meter.vo.MeterAbcItem;
import com.cdz360.base.model.meter.vo.MeterKhwData;
import com.cdz360.base.model.meter.vo.MeterKwhItem;
import com.cdz360.base.model.meter.vo.MeterTransformationRatio;
import com.cdz360.base.utils.DateUtils;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.ds.ro.MeterRoDs;
import com.cdz360.iot.ds.rw.MeterRwDs;
import com.cdz360.iot.meter.yunji.YJChannelConnectionPool;
import com.cdz360.iot.meter.yunji.qgdw376dot1.model.QGDW376Dot1Connection;
import com.cdz360.iot.meter.yunji.qgdw376dot1.model.QGDW376Dot1FieldC;
import com.cdz360.iot.meter.yunji.qgdw376dot1.model.QGDW376Dot1RawBody;
import com.cdz360.iot.model.meter.po.MeterPo;
import com.cdz360.iot.model.meter.type.MeterStatusType;
import com.cdz360.iot.model.pv.type.GtiVendor;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.ByteBuffer;

@Slf4j
@Component
public class QGDW376Dot1Decoder {

    @Autowired
    private YJChannelConnectionPool pool;

    @Autowired
    private DcEventPublisher dcEventPublisher;

    @Autowired
    private MeterRoDs meterRoDs;

    @Autowired
    private MeterRwDs meterRwDs;

    private final static BigDecimal K = new BigDecimal("1000");

    public void decode(QGDW376Dot1RawBody raw, String channelKey) {
        log.info("traceId: {}, begin to decode 376.1", raw.getTraceId());

        if (!check(raw.getBody())) {
            throw new DcServiceException("traceId: " + raw.getTraceId() + ", 376.1 帧数据校验错误");
        }

        QGDW376Dot1FieldC fieldC = QGDW376Dot1Utils.decodeFieldC(raw.getBody()[6]);
        byte functionCode = fieldC.getFunctionCode();
        if (fieldC.isD6()) {
            // PRM=1, 来自启动站
            switch (functionCode) {
                // 链路测试
                case 9:
                    decodeLinkTestData(raw, fieldC, channelKey);
                    break;
                default:
                    throw new DcServiceException("traceId: " + raw.getTraceId() + ", 376.1 帧 PRM=1 不支持的功能码: " + functionCode);
            }
        } else {
            // 来自从动站
            switch (functionCode) {
                // 用户数据
                case 8:
                    decodeLevel1Data(raw, fieldC, channelKey);
                    break;
                default:
                    throw new DcServiceException("traceId: " + raw.getTraceId() + ", 376.1 帧 PRM=0 不支持的功能码: " + functionCode);
            }
        }
        log.info("traceId: {}, end to decode 376.1", raw.getTraceId());
    }


    private void decodeLinkTestData(QGDW376Dot1RawBody raw, QGDW376Dot1FieldC fieldC, String channelKey) {
        byte[] msg = raw.getBody();
        byte AFN = msg[12];
        byte SEQ = msg[13];
        switch (AFN) {
            // 链路接口检测
            case 0x02:
                if (fieldC.isD7()) {
                    // 只处理上行报文
                    int i = 14;
                    int pn = QGDW376Dot1Utils.decodePN(new byte[]{msg[i], msg[i + 1]});
                    int fn = QGDW376Dot1Utils.decodeFN(new byte[]{msg[i + 2], msg[i + 3]});
                    i += 4;
                    if (pn != 0) {
                        // 忽略不是0的
                        break;
                    }
                    if (fn == 1) {
                        // 登陆, 加入pool中
                        String address = QGDW376Dot1Utils.decodeAddress(new byte[]{msg[7], msg[8], msg[9], msg[10], msg[11]});
                        QGDW376Dot1Connection connection = pool.getConnection(channelKey);
                        connection.setAddress(address);
                        int resq = connection.getAndUpdateRSEQ();
                        String CMD = QGDW376Dot1Encoder.getConfirmLoginCmdBy00F3(msg, resq, fieldC.isD5());
                        log.info("traceId: {}, 回复登陆请求: 回复指令: {}", raw.getTraceId(), CMD);
                        connection.getChannel().writeAndFlush(HexUtil.decodeHex(CMD));

                        // 发送 0A f89 指令, 读取网关信息, 先处理这个, 防止网关换新电表, 读取不到电表的信息
                        QGDW376Dot1Utils.sendCMDBy0AFn(connection, 89, raw.getTraceId());
                    } else if (fn == 3) {
                        QGDW376Dot1Connection connection = pool.getConnection(channelKey);
                        int resq = connection.getAndUpdateRSEQ();
                        String CMD = QGDW376Dot1Encoder.getConfirmHeartBeatCmdBy00F3(msg, resq, fieldC.isD5());
                        log.info("traceId: {}, 回复心跳请求: 回复指令: {}", raw.getTraceId(), CMD);
                        connection.getChannel().writeAndFlush(HexUtil.decodeHex(CMD));
                        // 数据单元体中，信号值代表的集中器的信号，17以上说明信号没问题，可以不用管
                        // not need to decode
                    } else {
                        // 忽略其他的fn
                        log.warn("traceId: {}, 其他忽略的链路测试指令: {}", raw.getTraceId(), HexUtil.encodeHexStr(msg));
                        break;
                    }
                }
                break;
            default:
                throw new DcServiceException("traceId: " + raw.getTraceId() + ", 376.1 帧不支持的应用层功能码: " + AFN);
        }
    }


    private void decodeLevel1Data(QGDW376Dot1RawBody raw, QGDW376Dot1FieldC fieldC, String channelKey) {
        byte[] msg = raw.getBody();
        byte AFN = msg[12];
        byte SEQ = msg[13];
        switch (AFN) {
            // 请求终端配置及信息
            case 0x09:
                decodeTerminalConfig(raw, fieldC);
                break;
            // 查询参数
            case 0x0A:
                decodeSearchData(raw, fieldC, channelKey);
                break;
            // 请求1类数据（实时）
            case 0x0C:
                decodeClass1Data(raw, fieldC, channelKey);
                break;
            // 请求2类数据（历史）
            case 0x0D:
                decodeClass2Data(raw, fieldC, channelKey);
                break;
            default:
                throw new DcServiceException("traceId: " + raw.getTraceId() + ", 376.1 帧不支持的应用层功能码: " + AFN);
        }
    }

    private static void decodeTerminalConfig(QGDW376Dot1RawBody raw, QGDW376Dot1FieldC fieldC) {
        byte[] msg = raw.getBody();
        int i = 14;
        int length = msg.length - 2;
        if (fieldC.isD7()) {
            // 上行
            while (i < length) {
                int pn = QGDW376Dot1Utils.decodePN(new byte[]{msg[i], msg[i + 1]});
                int fn = QGDW376Dot1Utils.decodeFN(new byte[]{msg[i + 2], msg[i + 3]});
                i += 4;
                switch (fn) {
                    // 没啥用
                    case 1: {
                        // 厂商代号
                        String manufacturerCode = QGDW376Dot1Utils.decodeAddress(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3]});
                        i += 4;

                        // 设备编号
                        String terminalCode = QGDW376Dot1Utils.decodeAddress(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5], msg[i + 6], msg[i + 7]});
                        i += 8;

                        // 终端软件版本号
                        String terminalSoftVer = QGDW376Dot1Utils.decodeAddress(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3]});
                        i += 4;

                        // 终端软件发布日期：日月年
                        String terminalSoftDate = decodeA20(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        i += 3;

                        // 终端配置容量信息码
                        String terminalCapacity = QGDW376Dot1Utils.decodeAddress(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5], msg[i + 6], msg[i + 7],
                                msg[i + 8], msg[i + 9], msg[i + 10]});
                        i += 11;

                        // 终端通信协议.版本号
                        String terminalProtocolVer = QGDW376Dot1Utils.decodeAddress(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3]});
                        i += 4;

                        // 终端硬件版本号
                        String terminalHardVer = QGDW376Dot1Utils.decodeAddress(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3]});
                        i += 4;

                        // 终端硬件发布日期：日月年
                        String terminalHardDate = decodeA20(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        i += 3;
                        QGDW376Dot1Utils.pushMqtt(String.format("厂商代号 %s, 设备编号 %s, 终端软件版本号 %s, 终端软件发布日期：日月年 %s, " +
                                        "终端配置容量信息码 %s, 终端通信协议.版本号 %s, 终端硬件版本号 %s, 终端硬件发布日期：日月年 %s",
                                manufacturerCode, terminalCode, terminalSoftVer, terminalSoftDate,
                                terminalCapacity, terminalProtocolVer, terminalHardVer, terminalHardDate));
                    }
                    break;
                    default:
                        throw new DcServiceException("traceId: " + raw.getTraceId() + ", 376.1 帧 09应用层功能码 不支持的FN: " + fn);
                }
            }
        }
    }

    private void decodeClass1Data(QGDW376Dot1RawBody raw, QGDW376Dot1FieldC fieldC, String channelKey) {
        byte[] msg = raw.getBody();
        int i = 14;
        int length = msg.length - 2;
        if (fieldC.isD7()) {
            // 上行
            QGDW376Dot1Connection connection = pool.getConnection(channelKey);
            BigDecimal ctr = BigDecimal.valueOf(connection.getCtr());
            BigDecimal vtr = BigDecimal.valueOf(connection.getVtr());
            while (i < length) {
                int pn = QGDW376Dot1Utils.decodePN(new byte[]{msg[i], msg[i + 1]});
                int fn = QGDW376Dot1Utils.decodeFN(new byte[]{msg[i + 2], msg[i + 3]});
                i += 4;
                switch (fn) {
                    // 当前三相及总有/无功功率、功率因数，三相电压、电流、零序电流、视在功率
                    case 25: {
                        // 解析时间
                        String time = decodeTime(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                        Date date = DateUtils.parseDate(time, "yy-MM-dd HH:mm");
                        i += 5;

                        // 当前总有功 kW
                        BigDecimal p = decodeA9(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        p = p.multiply(ctr).multiply(vtr);
                        i += 3;
                        // 三相有功功率
                        BigDecimal p1 = decodeA9(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        p1 = p1.multiply(ctr).multiply(vtr);
                        i += 3;
                        BigDecimal p2 = decodeA9(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        p2 = p2.multiply(ctr).multiply(vtr);
                        i += 3;
                        BigDecimal p3 = decodeA9(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        p3 = p3.multiply(ctr).multiply(vtr);
                        i += 3;

                        // 当前总无功 kW
                        BigDecimal q = decodeA9(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        q = q.multiply(ctr).multiply(vtr);
                        i += 3;
                        // 三相无功功率
                        BigDecimal q1 = decodeA9(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        q1 = q1.multiply(ctr).multiply(vtr);
                        i += 3;
                        BigDecimal q2 = decodeA9(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        q2 = q2.multiply(ctr).multiply(vtr);
                        i += 3;
                        BigDecimal q3 = decodeA9(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        q3 = q3.multiply(ctr).multiply(vtr);
                        i += 3;

                        // 当前总功率因数 %
                        BigDecimal hundred = new BigDecimal("100");
                        BigDecimal pf = decodeA5(new byte[]{msg[i], msg[i + 1]});
                        pf = pf.divide(hundred);
                        i += 2;
                        // 三相功率因数
                        BigDecimal pf1 = decodeA5(new byte[]{msg[i], msg[i + 1]});
                        pf1 = pf1.divide(hundred);
                        i += 2;
                        BigDecimal pf2 = decodeA5(new byte[]{msg[i], msg[i + 1]});
                        pf2 = pf2.divide(hundred);
                        i += 2;
                        BigDecimal pf3 = decodeA5(new byte[]{msg[i], msg[i + 1]});
                        pf3 = pf3.divide(hundred);
                        i += 2;

                        // 当前A相电压 V
                        BigDecimal ua = decodeA7(new byte[]{msg[i], msg[i + 1]});
                        ua = ua.multiply(vtr);
                        i += 2;
                        // 线电压
                        BigDecimal uab = ua.multiply(new BigDecimal("1.732")).setScale(1, RoundingMode.HALF_UP);

                        // 当前B相电压 V
                        BigDecimal ub = decodeA7(new byte[]{msg[i], msg[i + 1]});
                        ub = ub.multiply(vtr);
                        i += 2;
                        // 线电压
                        BigDecimal ubc = ub.multiply(new BigDecimal("1.732")).setScale(1, RoundingMode.HALF_UP);

                        // 当前C相电压 V
                        BigDecimal uc = decodeA7(new byte[]{msg[i], msg[i + 1]});
                        uc = uc.multiply(vtr);
                        i += 2;
                        // 线电压
                        BigDecimal uca = uc.multiply(new BigDecimal("1.732")).setScale(1, RoundingMode.HALF_UP);

                        // 当前A相电流 A
                        BigDecimal ia = decodeA25(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        ia = ia.multiply(ctr);
                        i += 3;

                        // 当前B相电流 A
                        BigDecimal ib = decodeA25(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        ib = ib.multiply(ctr);
                        i += 3;

                        // 当前C相电流 A
                        BigDecimal ic = decodeA25(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        ic = ic.multiply(ctr);
                        i += 3;

                        // 跳过零序电流
                        i += 3;

                        // 当前总视在功率 kVA
                        // 是null，别传
                        BigDecimal s = decodeA9(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        i += 3;
                        // 都为null, 跳过三相视在功率
                        i += 9;

                        QGDW376Dot1Utils.pushMqtt(
                                String.format("三相电压: %s , %s, %s; 三相电流: %s, %s, %s; " +
                                                "总有无功功率: %s, %s; 视在功率: %s; 功率因数: %s",
                                        ua, ub, uc, ia, ib, ic, p, q, s, pf)
                        );

                        EssVo<MeterRtData> essVo = new EssVo<>();
                        MeterRtData data = new MeterRtData();
                        MeterAbcData abcData = new MeterAbcData();

                        // 有功
                        MeterAbcItem activePower = new MeterAbcItem();
                        activePower.setTotal(p);
                        activePower.setV1(p1);
                        activePower.setV2(p2);
                        activePower.setV3(p3);
                        abcData.setActivePower(activePower);

                        // 无功
                        MeterAbcItem reactivePower = new MeterAbcItem();
                        reactivePower.setTotal(q);
                        reactivePower.setV1(q1);
                        reactivePower.setV2(q2);
                        reactivePower.setV3(q3);
                        abcData.setReactivePower(reactivePower);

                        // 功率因数
                        MeterAbcItem pfItem = new MeterAbcItem();
                        pfItem.setTotal(pf);
                        pfItem.setV1(pf1);
                        pfItem.setV2(pf2);
                        pfItem.setV3(pf3);
                        abcData.setPf(pfItem);


                        // 设置电压
                        MeterAbcItem voltage = new MeterAbcItem();
                        voltage.setV1(ua);
                        voltage.setV2(ub);
                        voltage.setV3(uc);
                        abcData.setVoltage(voltage);

                        // 电流
                        MeterAbcItem current = new MeterAbcItem();
                        current.setV1(ia);
                        current.setV2(ib);
                        current.setV3(ic);
                        abcData.setCurrent(current);

                        data.setAbc(abcData);

                        // 变比
                        MeterTransformationRatio ratio = new MeterTransformationRatio();
                        ratio.setCtr(ctr.intValue());
                        ratio.setVtr(vtr.intValue());
                        data.setTr(ratio);

                        data.setDno(connection.getDno());
                        data.setTs(date.getTime());

                        essVo.setDno(connection.getDno())
                                .setName(connection.getName())
                                .setSiteId(connection.getSiteId())
                                .setEssEquipType(EssEquipType.METER)
                                .setRtData(data);
                        dcEventPublisher.publishEssInfo(IotEvent.RT_DATA_CHANGE, essVo);
                    }
                    break;
                    // 当前正向有功电能示值
                    case 129: {
                        // 解析时间
                        String time = decodeTime(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                        Date date = DateUtils.parseDate(time, "yy-MM-dd HH:mm");
                        i += 5;
                        // 解析费率
                        int M = ((int) msg[i]) & 0xFF;
                        i += 1;
                        // 解析总值, 单位 kWh
                        BigDecimal total = decodeA14(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                        total = total.multiply(ctr).multiply(vtr);

                        i += M * 5 + 5;
                        QGDW376Dot1Utils.pushMqtt("当前正向有功电能示值 " + total.toPlainString());

                        // 发送数据
                        EssVo<MeterRtData> essVo = new EssVo<>();
                        MeterRtData data = new MeterRtData();
                        essVo.setDno(connection.getDno())
                                .setName(connection.getName())
                                .setSiteId(connection.getSiteId())
                                .setEssEquipType(EssEquipType.METER)
                                .setRtData(data);

                        MeterTransformationRatio ratio = new MeterTransformationRatio();
                        ratio.setCtr(ctr.intValue());
                        ratio.setVtr(vtr.intValue());
                        data.setTr(ratio);
                        data.setDno(connection.getDno());
                        data.setTs(date.getTime());

                        // 设置电能
                        MeterKhwData khwData = new MeterKhwData();
                        data.setKwh(khwData);

                        MeterKwhItem positive = new MeterKwhItem();
                        positive.setTotal(total);
                        khwData.setPositive(positive);
                        dcEventPublisher.publishEssInfo(IotEvent.RT_DATA_CHANGE, essVo);
                    }
                    break;
                    // 当前正向无功电能示值
                    case 130: {
                        // 解析时间
                        String time = decodeTime(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                        Date date = DateUtils.parseDate(time, "yy-MM-dd HH:mm");
                        i += 5;
                        // 解析费率
                        int M = ((int) msg[i]) & 0xFF;
                        i += 1;
                        // 解析总值, 单位 kvarh
                        BigDecimal total = decodeA11(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                        total = total.multiply(ctr).multiply(vtr);
                        i += M * 4 + 4;
                        QGDW376Dot1Utils.pushMqtt("当前正向无功电能示值 " + total.toPlainString());

                        // 发送数据
                        EssVo<MeterRtData> essVo = new EssVo<>();
                        MeterRtData data = new MeterRtData();
                        essVo.setDno(connection.getDno())
                                .setName(connection.getName())
                                .setSiteId(connection.getSiteId())
                                .setEssEquipType(EssEquipType.METER)
                                .setRtData(data);

                        MeterTransformationRatio ratio = new MeterTransformationRatio();
                        ratio.setCtr(ctr.intValue());
                        ratio.setVtr(vtr.intValue());
                        data.setTr(ratio);
                        data.setDno(connection.getDno());
                        data.setTs(date.getTime());

                        // 设置电能
                        MeterKhwData khwData = new MeterKhwData();
                        data.setKwh(khwData);

                        MeterKwhItem combineReactive1 = new MeterKwhItem();
                        combineReactive1.setTotal(total);
                        khwData.setCombineReactive1(combineReactive1);
                        dcEventPublisher.publishEssInfo(IotEvent.RT_DATA_CHANGE, essVo);
                    }
                    break;
                    // 当前反向有功电能示值
                    case 131: {
                        // 解析时间
                        String time = decodeTime(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                        Date date = DateUtils.parseDate(time, "yy-MM-dd HH:mm");
                        i += 5;
                        // 解析费率
                        int M = ((int) msg[i]) & 0xFF;
                        i += 1;
                        // 解析总值, 单位 kWh
                        BigDecimal total = decodeA14(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                        total = total.multiply(ctr).multiply(vtr);
                        i += M * 5 + 5;
                        QGDW376Dot1Utils.pushMqtt("当前反向有功电能示值 " + total.toPlainString());

                        // 发送数据
                        EssVo<MeterRtData> essVo = new EssVo<>();
                        MeterRtData data = new MeterRtData();
                        essVo.setDno(connection.getDno())
                                .setName(connection.getName())
                                .setSiteId(connection.getSiteId())
                                .setEssEquipType(EssEquipType.METER)
                                .setRtData(data);

                        MeterTransformationRatio ratio = new MeterTransformationRatio();
                        ratio.setCtr(ctr.intValue());
                        ratio.setVtr(vtr.intValue());
                        data.setTr(ratio);
                        data.setDno(connection.getDno());
                        data.setTs(date.getTime());

                        // 设置电能
                        MeterKhwData khwData = new MeterKhwData();
                        data.setKwh(khwData);

                        MeterKwhItem negative = new MeterKwhItem();
                        negative.setTotal(total);
                        khwData.setNegative(negative);
                        dcEventPublisher.publishEssInfo(IotEvent.RT_DATA_CHANGE, essVo);
                    }
                    break;
                    // 当前反向无功电能示值
                    case 132: {
                        // 解析时间
                        String time = decodeTime(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                        Date date = DateUtils.parseDate(time, "yy-MM-dd HH:mm");
                        i += 5;
                        // 解析费率
                        int M = ((int) msg[i]) & 0xFF;
                        i += 1;
                        // 解析总值, 单位 kvarh
                        BigDecimal total = decodeA11(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                        total = total.multiply(ctr).multiply(vtr);
                        i += M * 4 + 4;
                        QGDW376Dot1Utils.pushMqtt("当前反向无功电能示值 " + total.toPlainString());

                        // 发送数据
                        EssVo<MeterRtData> essVo = new EssVo<>();
                        MeterRtData data = new MeterRtData();
                        essVo.setDno(connection.getDno())
                                .setName(connection.getName())
                                .setSiteId(connection.getSiteId())
                                .setEssEquipType(EssEquipType.METER)
                                .setRtData(data);

                        MeterTransformationRatio ratio = new MeterTransformationRatio();
                        ratio.setCtr(ctr.intValue());
                        ratio.setVtr(vtr.intValue());
                        data.setTr(ratio);
                        data.setDno(connection.getDno());
                        data.setTs(date.getTime());

                        // 设置电能
                        MeterKhwData khwData = new MeterKhwData();
                        data.setKwh(khwData);

                        MeterKwhItem combineReactive2 = new MeterKwhItem();
                        combineReactive2.setTotal(total);
                        khwData.setCombineReactive2(combineReactive2);
                        dcEventPublisher.publishEssInfo(IotEvent.RT_DATA_CHANGE, essVo);
                    }
                    break;
                    default:
                        throw new DcServiceException("traceId: " + raw.getTraceId() + ", 376.1 帧 0C应用层功能码 不支持的FN: " + fn);
                }
            }
        } else {
            // 下行, 发送暂不处理 qyctodo
        }
    }


    private void decodeClass2Data(QGDW376Dot1RawBody raw, QGDW376Dot1FieldC fieldC, String channelKey) {
        byte[] msg = raw.getBody();
        int i = 14;
        int length = msg.length - 2;
        if (fieldC.isD7()) {
            // 上行
            QGDW376Dot1Connection connection = pool.getConnection(channelKey);
            String no = connection.getNo();
            while (i < length) {
                int pn = QGDW376Dot1Utils.decodePN(new byte[]{msg[i], msg[i + 1]});
                int fn = QGDW376Dot1Utils.decodeFN(new byte[]{msg[i + 2], msg[i + 3]});
                i += 4;
                switch (fn) {
                    case 21, 22, 23, 24: {
                        // 月冻结类数据时标 Td_m
                        String Td_m = decodeBCD(msg[i + 1]) + decodeBCD(msg[i]);
                        i += 2;

                        // 费率数 M
                        int numM = QGDW376Dot1Utils.decode1Byte(msg[i]);
                        i++;

                        // 月正向有功总电能量
                        BigDecimal total = decodeA13(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3]});
                        i += 4;

                        // 月费率 M 正向有功电能量
                        StringBuilder sb = new StringBuilder();
                        for (int k = 0; k < numM; k++) {
                            sb.append(decodeA13(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3]}).toPlainString());
                            sb.append(" | ");
                            i += 4;
                        }

                        String prefix = "";
                        if (fn == 21) {
                            prefix = "月冻结正向有功电能量";
                        } else if (fn == 22) {
                            prefix = "月冻结正向无功电能量";
                        } else if (fn == 23) {
                            prefix = "月冻结反向有功电能量";
                        } else if (fn == 24) {
                            prefix = "月冻结反向无功电能量";
                        }
                        QGDW376Dot1Utils.pushMqtt(prefix + " 总: " + total + "各个费率: " + sb.toString());
                    }
                    break;
                    case 177, 179: {
                        // 月冻结类数据时标 Td_m
                        String Td_m = decodeBCD(msg[i + 1]) + decodeBCD(msg[i]);
                        i += 2;

                        // 终端抄表时间
                        String time = decodeTime(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                        i += 5;

                        // 费率数 M
                        int numM = QGDW376Dot1Utils.decode1Byte(msg[i]);
                        i++;

                        // 正向有功总电能示值
                        BigDecimal total = decodeA14(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4]});
                        i += 5;

                        // 费率 M 正向有功电能示值
                        StringBuilder sb = new StringBuilder();
                        for (int k = 0; k < numM; k++) {
                            sb.append(decodeA14(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4]}).toPlainString());
                            sb.append(" | ");
                            i += 5;
                        }
                        String prefix = no;
                        if (fn == 177) {
                            prefix += " 月冻结正向有功电能示值";
                        } else if (fn == 179) {
                            prefix += " 月冻结反向有功电能示值";
                        }
                        QGDW376Dot1Utils.pushMqtt(prefix + " 抄表时间: " + time + " 总: " + total + "各个费率: " + sb.toString());
                    }
                    break;
                    case 178, 180: {
                        // 月冻结类数据时标 Td_m
                        String Td_m = decodeBCD(msg[i + 1]) + decodeBCD(msg[i]);
                        i += 2;

                        // 终端抄表时间
                        String time = decodeTime(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                        i += 5;

                        // 费率数 M
                        int numM = QGDW376Dot1Utils.decode1Byte(msg[i]);
                        i++;

                        // 正向有功总电能示值
                        BigDecimal total = decodeA11(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3]});
                        i += 4;

                        // 费率 M 正向有功电能示值
                        StringBuilder sb = new StringBuilder();
                        for (int k = 0; k < numM; k++) {
                            sb.append(decodeA11(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3]}).toPlainString());
                            sb.append(" | ");
                            i += 4;
                        }
                        String prefix = no;
                        if (fn == 178) {
                            prefix += " 月冻结正向无功（组合无功1）电能示值";
                        } else if (fn == 180) {
                            prefix += " 月冻结反向无功（组合无功1）电能示值";
                        }
                        QGDW376Dot1Utils.pushMqtt(prefix + " 抄表时间: " + time + " 总: " + total + "各个费率: " + sb.toString());
                    }
                    break;
                    default:
                        throw new DcServiceException("traceId: " + raw.getTraceId() + ", 376.1 帧 0D应用层功能码 不支持的FN: " + fn);
                }
            }
        } else {
            // 下行暂不处理
        }
    }


    private void decodeSearchData(QGDW376Dot1RawBody raw, QGDW376Dot1FieldC fieldC, String channelKey) {
        byte[] msg = raw.getBody();
        int i = 14;
        int length = msg.length - 2;
        if (fieldC.isD7()) {
            // 上行
            while (i < length) {
                int pn = QGDW376Dot1Utils.decodePN(new byte[]{msg[i], msg[i + 1]});
                int fn = QGDW376Dot1Utils.decodeFN(new byte[]{msg[i + 2], msg[i + 3]});
                i += 4;
                switch (fn) {
                    case 3: {
                        // 主用 IP 地址
                        String mainIpAddress = decodeIpAndPort(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                        i += 6;

                        // 备用 IP 地址
                        String backupIpAddress = decodeIpAndPort(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                        i += 6;

                        // APN
                        byte[] copy = new byte[16];
                        System.arraycopy(msg, i, copy, 0, 16);
                        String APN = escapeISO646(HexUtil.encodeHexStr(copy));
                        i += 16;

                        QGDW376Dot1Utils.pushMqtt(String.format("主用 IP 地址 %s, 备用 IP 地址 %s, APN %s",
                                mainIpAddress, backupIpAddress, APN));

                    }
                    break;
                    // 不支持
                    case 8: {
                        // 工作模式
                        int[] workMode = decodeWorkMode(msg[i]);
                        int tcpUdp = workMode[0];
                        int terminalWorkMode = workMode[1];
                        int onlineMode = workMode[2];
                        i++;

                        // 永久在线、时段在线模式重拨间隔 秒
                        int replayInterval = QGDW376Dot1Utils.decode2Byte(new byte[]{msg[i], msg[i + 1]});
                        i += 2;

                        // 被动激活模式重拨次数 次
                        int replayTime = QGDW376Dot1Utils.decode1Byte(msg[i]);
                        i++;

                        // 被动激活模式连续无通信自动断线时间 min
                        int autoOfflineTime = QGDW376Dot1Utils.decode1Byte(msg[i]);
                        i++;

                        // 时段在线模式允许在线时段标志
                        String onlineFlag = QGDW376Dot1Utils.decodeAddress(new byte[]{msg[i], msg[i + 1], msg[i + 2]});
                        i += 3;

                        QGDW376Dot1Utils.pushMqtt(String.format("工作模式 %s | %s | %s, 永久在线、时段在线模式重拨间隔 秒 %s, " +
                                        "被动激活模式重拨次数 次 %s, 被动激活模式连续无通信自动断线时间 min %s, 时段在线模式允许在线时段标志 %s",
                                tcpUdp, terminalWorkMode, onlineMode,
                                replayInterval,
                                replayTime,
                                autoOfflineTime,
                                onlineFlag));
                    }
                    break;
                    case 10: {
                        // 数量
                        int num = QGDW376Dot1Utils.decode2Byte(new byte[]{msg[i], msg[i + 1]});
                        i += 2;
                        for (int k = 0; k < num; k++) {
                            // 电能表/交流采样装置序号
                            int no = QGDW376Dot1Utils.decode2Byte(new byte[]{msg[i], msg[i + 1]});
                            i += 2;

                            // 所属测量点号
                            int measurePoint = QGDW376Dot1Utils.decode2Byte(new byte[]{msg[i], msg[i + 1]});
                            i += 2;

                            // 通信速率及端口号
                            int[] speedAndPort = QGDW376Dot1Utils.decodeConnectionSpeedAndPort(msg[i]);
                            int speed = speedAndPort[0];
                            int port = speedAndPort[1];
                            i++;

                            // 通信协议类型
                            int protocol = QGDW376Dot1Utils.decode1Byte(msg[i]);
                            i++;

                            // 通信地址
                            // 电表标号 no
                            BigDecimal connectionAddress = decodeA12(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                            i += 6;
                            String meterNo = connectionAddress.toPlainString();

                            // 通信密码
                            String password = QGDW376Dot1Utils.decodeAddress(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                            i += 6;

                            // 电能费率个数
                            int energyMNum = QGDW376Dot1Utils.decodeEnergyMNum(msg[i]);
                            i++;


                            // 有功电能示值的整数位及小数位个数
                            int[] integerAndDecimal = QGDW376Dot1Utils.decodeEnergyIntegerAndDecimal(msg[i]);
                            int integer = integerAndDecimal[0];
                            int decimal = integerAndDecimal[1];
                            i++;

                            // 所属采集器通信地址
                            BigDecimal collectorAddress = decodeA12(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3], msg[i + 4], msg[i + 5]});
                            i += 6;


                            int[] userBigAndSmall = QGDW376Dot1Utils.decodeUserBigAndSmall(msg[i]);
                            int userBig = userBigAndSmall[0];
                            int userSmall = userBigAndSmall[1];
                            i++;


                            QGDW376Dot1Utils.pushMqtt(String.format("电能表/交流采样装置序号 %s, 所属测量点号 %s, 通信速率及端口号 %s | %s, " +
                                            "通信协议类型 %s, 通信地址 %s, 通信密码 %s, 电能费率个数 %s, 有功电能示值整数位及小数位个数 %s | %s, " +
                                            "所属采集器通信地址 %s, 用户大类号及用户小类号 %s | %s",
                                    no, measurePoint, speed, port, protocol, meterNo, password,
                                    energyMNum, integer, decimal, collectorAddress.toPlainString(), userBig, userSmall
                            ));

                            addOrUpdateMeterNo2DBAndCache(pool.getConnection(channelKey), meterNo, raw.getTraceId());
                        }
                    }
                    break;
                    case 89: {
                        // 行政区码 低位在前
                        String administrativeAreaCode = decodeBCD(msg[i + 1]) + decodeBCD(msg[i]);
                        i += 2;

                        // 终端地址
                        String terminalAddress = QGDW376Dot1Utils.decodeTerminalAddress(new byte[]{msg[i], msg[i + 1]});
                        i += 2;

                        // 存入dno
                        String terminalNo = administrativeAreaCode + terminalAddress;

                        fireFindMeterNoAndSaveTerminalNo(pool.getConnection(channelKey), terminalNo, raw.getTraceId());

                        QGDW376Dot1Utils.pushMqtt(String.format("行政区码 %s, 终端地址 %s",
                                administrativeAreaCode, terminalAddress));
                    }
                    break;
                    default:
                        throw new DcServiceException("traceId: " + raw.getTraceId() + ", 376.1 帧 0A应用层功能码 不支持的FN: " + fn);
                }
            }
        } else {
            // 下行暂不处理
        }
    }

    // 其次查表号
    private void addOrUpdateMeterNo2DBAndCache(QGDW376Dot1Connection connection, String meterNo, String traceId) {
        connection.setNo(meterNo);
        MeterPo finded = meterRoDs.getByNo(meterNo);
        // 找不到就入库
        if (finded == null) {
            finded = new MeterPo();
            finded.setDno(connection.getDno());
            finded.setNo(meterNo);
            finded.setSiteId("empty");
            finded.setName("empty");
            finded.setStatus(MeterStatusType.ONLINE);
            finded.setNet(NetType.M4G);
            finded.setVendor(GtiVendor.LINYANG);
            finded.setOtherDevice(true);
            finded.setComment("empty");
            finded.setVtr(1);
            finded.setCtr(1);
            meterRwDs.insertMeter(finded);
        }

        // 加入缓存
        connection.setVtr(finded.getVtr());
        connection.setCtr(finded.getCtr());
        connection.setSiteId(finded.getSiteId());
        connection.setName(finded.getName());
        connection.setNo(finded.getNo());
        connection.setDno(finded.getDno());
    }

    // 优先查集中器
    private void fireFindMeterNoAndSaveTerminalNo(QGDW376Dot1Connection connection, String terminalNo, String traceId) {
        connection.setDno(terminalNo);
        MeterPo finded = meterRoDs.getByDno(terminalNo);
        if (finded == null) {
            // 发送查找标号的指令
            QGDW376Dot1Utils.sendCMDBy0AF10(connection, traceId);
        } else {
            // 不发送，设置参数
            connection.setVtr(finded.getVtr());
            connection.setCtr(finded.getCtr());
            connection.setSiteId(finded.getSiteId());
            connection.setName(finded.getName());
            connection.setNo(finded.getNo());
            connection.setDno(finded.getDno());
        }
    }


    public static String escapeISO646(String input) {
        StringBuilder escaped = new StringBuilder();
        for (char c : input.toCharArray()) {
            // 检查字符是否在ISO 646 US范围内
            if (c >= 0 && c <= 127) {
                // 转换为Unicode转义序列
                escaped.append(String.format("\\u%04x", (int) c));
            } else {
                // 直接添加非ISO 646字符
                escaped.append(c);
            }
        }
        return escaped.toString();
    }


    private static String decodeIpAndPort(byte[] bytes) {
        int a1 = QGDW376Dot1Utils.decode1Byte(bytes[0]);
        int a2 = QGDW376Dot1Utils.decode1Byte(bytes[1]);
        int a3 = QGDW376Dot1Utils.decode1Byte(bytes[2]);
        int a4 = QGDW376Dot1Utils.decode1Byte(bytes[3]);
        int port = QGDW376Dot1Utils.decode2Byte(new byte[]{bytes[4], bytes[5]});
        return String.format("%s.%s.%s.%s:%s", a1, a2, a3, a4, port);
    }


    private static int[] decodeWorkMode(byte b) {
        int ib = ((int) b & 0xFF);
        // D7：按位表示TCP/UDP，置“0”为TCP；置“1”为UDP。
        int D7 = (ib & 0x80) >> 7;
        // D5～D4：编码表示终端的三种工作模式，取值范围0～2依次表示混合模式、客户机模式、服务器模式
        int D5D4 = (ib & 0x30) >> 4;
        // D1～D0：编码表示当终端工作在客户机模式下的三种在线模式，取值范围1～3依次表示
        // 永久在线模式、被动激活模式、时段在线模式。
        int D1D0 = (ib & 0x03);
        return new int[]{D7, D5D4, D1D0};
    }


    /**
     * 解析bcd时间 解析A15
     *
     * @param bytes 5个字节
     * @return 年-月-日 时:分
     */
    private static String decodeTime(byte[] bytes) {
        if (!checkHasData(bytes)) {
            return "";
        }
        return String.format("%s-%s-%s %s:%s",
                decodeBCD(bytes[4]),
                decodeBCD(bytes[3]),
                decodeBCD(bytes[2]),
                decodeBCD(bytes[1]),
                decodeBCD(bytes[0])
        );
    }


    private static String decodeA20(byte[] bytes) {
        if (!checkHasData(bytes)) {
            return "";
        }
        return String.format("%s-%s-%s",
                decodeBCD(bytes[2]),
                decodeBCD(bytes[1]),
                decodeBCD(bytes[0])
        );
    }


    // +/-XXX.XXX
    private static BigDecimal decodeA25(byte[] bytes) {
        if (!checkHasData(bytes)) {
            return null;
        }
        String S = (bytes[2] & (byte) 0x80) == (byte) 0x80 ? "-" : "";
        String bcd1 = decodeBCD(bytes[1]);
        bcd1 = bcd1.substring(0, 1) + "." + bcd1.substring(1);
        String format = String.format("%s%s%s%s",
                S,
                decodeBCD((byte) (bytes[2] & (byte) 0x7F)),
                bcd1,
                decodeBCD(bytes[0])
        );
        return new BigDecimal(format);
    }

    // XXXXXX.XXXX
    private static BigDecimal decodeA14(byte[] bytes) {
        if (!checkHasData(bytes)) {
            return null;
        }
        String format = String.format("%s%s%s.%s%s",
                decodeBCD(bytes[4]),
                decodeBCD(bytes[3]),
                decodeBCD(bytes[2]),
                decodeBCD(bytes[1]),
                decodeBCD(bytes[0])
        );
        return new BigDecimal(format);
    }

    // XXXX.XXXX
    private static BigDecimal decodeA13(byte[] bytes) {
        if (!checkHasData(bytes)) {
            return null;
        }
        String format = String.format("%s%s.%s%s",
                decodeBCD(bytes[3]),
                decodeBCD(bytes[2]),
                decodeBCD(bytes[1]),
                decodeBCD(bytes[0])
        );
        return new BigDecimal(format);
    }

    // XXXXXX XXXXXX
    private static BigDecimal decodeA12(byte[] bytes) {
        if (!checkHasData(bytes)) {
            return null;
        }
        String format = String.format("%s%s%s%s%s%s",
                decodeBCD(bytes[5]),
                decodeBCD(bytes[4]),
                decodeBCD(bytes[3]),
                decodeBCD(bytes[2]),
                decodeBCD(bytes[1]),
                decodeBCD(bytes[0])
        );
        return new BigDecimal(format);
    }

    // XXXXXX.XX
    private static BigDecimal decodeA11(byte[] bytes) {
        if (!checkHasData(bytes)) {
            return null;
        }
        String format = String.format("%s%s%s.%s",
                decodeBCD(bytes[3]),
                decodeBCD(bytes[2]),
                decodeBCD(bytes[1]),
                decodeBCD(bytes[0])
        );
        return new BigDecimal(format);
    }

    // +/-XX.XXXX
    private static BigDecimal decodeA9(byte[] bytes) {
        if (!checkHasData(bytes)) {
            return null;
        }
        String S = (bytes[2] & (byte) 0x80) == (byte) 0x80 ? "-" : "";

        String format = String.format("%s%s.%s%s",
                S,
                decodeBCD((byte) (bytes[2] & (byte) 0x7F)),
                decodeBCD(bytes[1]),
                decodeBCD(bytes[0])
        );
        return new BigDecimal(format);
    }

    // XXX.X
    private static BigDecimal decodeA7(byte[] bytes) {
        if (!checkHasData(bytes)) {
            return null;
        }
        String bcd0 = decodeBCD(bytes[0]);
        bcd0 = bcd0.substring(0, 1) + "." + bcd0.substring(1);
        String format = String.format("%s%s",
                decodeBCD(bytes[1]),
                bcd0
        );
        return new BigDecimal(format);
    }

    // +/-XXX.X
    public static BigDecimal decodeA5(byte[] bytes) {
        if (!checkHasData(bytes)) {
            return null;
        }
        String S = (bytes[1] & (byte) 0x80) == (byte) 0x80 ? "-" : "";
        String bcd0 = decodeBCD(bytes[0]);
        bcd0 = bcd0.substring(0, 1) + "." + bcd0.substring(1);
        String format = String.format("%s%s%s",
                S,
                decodeBCD((byte) (bytes[1] & (byte) 0x7F)),
                bcd0
        );
        return new BigDecimal(format);
    }


    private static String decodeBCD(byte bcd) {
        StringBuilder decimalString = new StringBuilder();
        // 获取高四位和低四位
        int highNibble = (bcd >> 4) & 0x0F; // 高四位
        int lowNibble = bcd & 0x0F;          // 低四位

        // 将高四位和低四位转换为字符并添加到结果中
        decimalString.append(highNibble);
        decimalString.append(lowNibble);
        return decimalString.toString();
    }

    /**
     * 校验 帧 数据是否合法
     *
     * @param msg 帧数据
     * @return 是否合法
     */
    private static boolean check(byte[] msg) {
        int length = msg.length;
        if (length < 14) {
            return false;
        }
        // 校验字符
        if (msg[0] != 0x68 || msg[5] != 0x68) {
            return false;
        }
        // 校验长度
        int l1 = QGDW376Dot1Utils.decodeLength(new byte[]{msg[1], msg[2]});
        int l2 = QGDW376Dot1Utils.decodeLength(new byte[]{msg[3], msg[4]});
        if (l1 != l2 || (l1 + 8) != length) {
            return false;
        }

        // 校验结束
        int sum = msg[length - 2];
        int end = msg[length - 1];
        // 使用ByteBuffer包装byte数组
        ByteBuffer byteBuffer = ByteBuffer.wrap(msg);
        ByteBuffer slice = byteBuffer.slice(6, length - 8);
        // 将切片转换为byte数组
        byte[] slicedArray = new byte[slice.remaining()]; // 创建一个新的byte数组
        slice.get(slicedArray); // 将切片内容复制到新的byte数组中
        byte calSum = QGDW376Dot1Utils.calculateChecksum(slicedArray);
        if (end != 0x16 || sum != calSum) {
            return false;
        }
        return true;
    }

    /**
     * 当数据中全部是EE,返回false，代表没有数据
     * 有数据则返回true
     *
     * @param data 数据
     * @return boolean
     */
    private static boolean checkHasData(byte[] data) {
        for (byte b : data) {
            if (b != (byte) 0xEE) {
                return true; // 如果发现有不是0xEE的字节，返回false
            }
        }
        return false;
    }

    private void saveOrUpdateMeter() {
//        meterRoDs.getByNo();
//        meterRwDs.insertMeter();
    }
}
