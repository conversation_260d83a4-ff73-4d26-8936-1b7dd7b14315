app:
  name: iot-park-api

server:
  address: 0.0.0.0
  port: 8080
  use-forward-headers: true
  compression.enabled: true

spring:
  application:
    name: iot-park-dev
  config:
    import: "configserver:http://oam-test.rnd.iot.renwochong.com"
  profiles:
    active: test01,common,rabbitmq,jdbc,redis,zipkin
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    config:
      label: test01

management:
  context-path: /admin
  security:
    enabled: false


eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:<EMAIL>:7001/eureka/,http://aaa:<EMAIL>/eureka/

feign:
  hystrix:
    enabled: true


hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 5000



logging:
  level:
    com.cdz360.iot: 'DEBUG'
    org.springframework: 'WARN'
    org.mybatis: 'DEBUG'


  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} - %logger{36}.%M\\(%line\\) - %msg%n"

springdoc:
  packagesToScan: com.cdz360.iot.park.rest
  swagger-ui:
    path: /swagger-ui.html


iot:
  park:
    bsUrl: "http://istparking-beta.sciseetech.com"
    bsSignKey: "GRXC0EE8Z68TG14U"
    bsUrlZk: "https://api.4pyun.com"
    bsUrlYb: "http://testlpn.ebopark.com"
    bsUrlNtc: "https://park.ningtingche.com"
    bsUrlJs: "https://jsopen.jslife.com.cn"
    bsUrlStc: "http://yunweigmwinlead.cn"
  lotServerList:
    - siteIdList: # 使用该供应商的场站ID
        - 2005170156870639372
        - 2107017474751572482
#      host: "parklock.anneffi.com"
#      port: 9013
#      appId: 688127427  # 编码(平台方提供)
#      appKey: "3c1a6acd7470440f80b27c4924e4a013"   # 密码(平台方提供)
      host: "**************"
      port: 9013
      appId: 881274276  # 编码(平台方提供)
      appKey: "3c1a6acd7470440f80b27c4924e4a013"   # 密码(平台方提供)
      partner: "ANNEFFI" # 注意: 枚举值