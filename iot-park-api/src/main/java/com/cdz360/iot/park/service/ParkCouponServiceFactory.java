package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.park.vo.ParkCouponVo;
import com.cdz360.iot.park.feign.DataCoreFeignClient;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname ParkCouponServiceFactory
 * @Description
 * @Date 5/16/2024 2:28 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class ParkCouponServiceFactory {
    private Map<String, IParkCouponService> strategyMap = new ConcurrentHashMap<>();

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    protected void addStrategy(String proDefKey, IParkCouponService strategy) {
        this.strategyMap.put(proDefKey, strategy);
    }

    public IParkCouponService getStrategy(String proDefKey) {
        return this.strategyMap.get(proDefKey);
    }

    protected ParkCouponVo getParkCoupon(OrderStopRequestV2 orderStopRequestV2) {
        ObjectResponse<ParkCouponVo> parkCouponVoObjectResponse =
            dataCoreFeignClient.checkParkCoupon(orderStopRequestV2.getOrderNo(),
                orderStopRequestV2.getKwh());

        log.info("检查停车减免并推送减免res: {}", JsonUtils.toJsonString(parkCouponVoObjectResponse));

        ParkCouponVo data = null;
        if(parkCouponVoObjectResponse != null && parkCouponVoObjectResponse.getData() != null) {
            data = parkCouponVoObjectResponse.getData();
        }
        return data;
    }
}