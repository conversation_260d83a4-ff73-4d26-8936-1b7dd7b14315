package com.cdz360.iot.park.model.cs.anneffi;

import com.cdz360.iot.park.model.cs.CommanderDatabase;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
public class AnneffiCommanderData extends CommanderDatabase {

    @Schema(description = "开闭锁使用: true(打开);false(关闭)")
    private boolean open;
}
