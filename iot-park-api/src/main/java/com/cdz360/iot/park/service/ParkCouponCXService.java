package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.park.vo.ParkCouponCxVo;
import com.cdz360.iot.model.park.vo.ParkCouponVo;
import com.cdz360.iot.park.feign.ReactiveDataCoreFeignClient;
import com.cdz360.iot.park.service.cs.common.ParkConstants;
import java.nio.charset.StandardCharsets;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import reactor.core.publisher.Mono;

/**
 * @Classname ParkCouponCXService
 * @Description
 * @Date 5/16/2024 4:30 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class ParkCouponCXService extends AbstractParkCouponService {

    @Autowired
    private ReactiveDataCoreFeignClient reactiveDataCoreFeignClient;

    @Autowired
    private BsService bsService;

    @PostConstruct
    public void init() {
        this.parkCouponServiceFactory.addStrategy(ParkConstants.PARK_TYPE_CX, this);
    }

    @Override
    public Mono<BaseResponse> orderStop(OrderStopRequestV2 orderStopRequestV2, ParkCouponVo data) {
        ParkCouponCxVo req = new ParkCouponCxVo();
        req.setParkingId(data.getParkAppId())
            .setAppKey(DigestUtils.md5DigestAsHex(data.getParkAppSecret().getBytes(
                StandardCharsets.UTF_8)).toUpperCase())
            .setFavourableDuration(data.getDuration())
            .setPlateNumber(data.getCarNo())
            .setTimestamp(System.currentTimeMillis());
        String paramAuth = getHeaderAuthCx(req, data.getParkAppSecret());
        req.setSign(paramAuth);
        this.setCouponCx(req)
            .doOnSuccess(e -> {
                if (e.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                    reactiveDataCoreFeignClient.setParkCouponDuration(orderStopRequestV2.getOrderNo(),
                        data.getDuration()).subscribe();

                } else {
                    log.warn("第三方道闸CX返回状态异常: {}", e);
                }
            })
            .doOnError(e -> log.error(e.getMessage(), e))
            .subscribe();
        return Mono.just(RestUtils.success());
    }

    /**
     * 深圳创享智能开发有限公司-停车减免优惠券
     * @param param
     * @return
     */
    public Mono<BaseResponse> setCouponCx(ParkCouponCxVo param) {
        log.info("CX请求参数: {}", JsonUtils.toJsonString(param));
        return bsService.setCouponCx(param);
    }

    private static String getHeaderAuthCx(ParkCouponCxVo param, String appSecret) {
        String encryptStr = "favourableDuration=" + param.getFavourableDuration()
            + "&parkingId=" + param.getParkingId()
            + "&plateNumber=" + param.getPlateNumber()
            + "&timestamp=" + param.getTimestamp() + param.getAppKey();
        String s = DigestUtils.md5DigestAsHex(encryptStr.getBytes(StandardCharsets.UTF_8)).toUpperCase();
        return s;
    }
}