package com.cdz360.iot.park.service.cs.comsumer;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.ds.rw.ParkingLockRwDs;
import com.cdz360.iot.model.park.po.ParkingLockPo;
import com.cdz360.iot.model.park.type.ParkingLockPartner;
import com.cdz360.iot.model.park.type.ParkingLockStatus;
import com.cdz360.iot.park.feign.DataCoreFeignClient;
import com.cdz360.iot.park.model.cs.EventDataBase;
import com.cdz360.iot.park.model.cs.ParkingLockErrorParam;
import com.cdz360.iot.park.model.cs.anneffi.AnneffiLockErrorCode;
import com.cdz360.iot.park.model.cs.anneffi.AnneffiLockInfo;
import com.cdz360.iot.park.service.ParkingLockEventLogService;
import com.cdz360.iot.park.service.cs.job.AbstractJob;
import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public abstract class AbstractParkingLockSubObserver<T extends AbstractJob> implements
    ParkingLockSubObserver {

    private static final int TIMEOUT_MINUTE = 60 * 1000; // 1分钟

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    protected ParkingLockRwDs parkingLockRwDs;

    @Autowired
    private ParkingLockEventLogService eventLogService;

    private Map<String, T> jobMap = new ConcurrentHashMap<>();

    private static String key(ParkingLockPartner partner, String deviceNo) {
        return partner + ":" + deviceNo;
    }

    protected void addJob(ParkingLockPartner partner, String deviceNo, T job) {
        this.jobMap.put(key(partner, deviceNo), job);
    }

    @Override
    public void notify(EventDataBase msg) {
        if (msg.getTaskCode().isOutflow()) { // 相应类信息
            log.info("[供应商服务响应]: partner = {}, deviceNo = {}, code = {}",
                msg.getPartner(), msg.getDeviceNo(), msg.getTaskCode());
            T job = jobMap.get(key(msg.getPartner(), msg.getDeviceNo()));
            if (job == null) {
                log.warn("job not exist. partner = {}, deviceNo = {}", msg.getPartner(),
                    msg.getDeviceNo());
                return;
            }

            try {
                job.fillData(msg);
                jobMap.remove(key(msg.getPartner(), msg.getDeviceNo()));
                job.finish();
            } catch (Exception ex) {
                log.warn("处理异常: {}", ex.getMessage(), ex);
                jobMap.remove(key(msg.getPartner(), msg.getDeviceNo()));
                job.exception(ex);
            }
        } else { // 接受推送类信息
            this.notifyMsg(msg);
        }
    }

    private void notifyMsg(EventDataBase msg) {
        log.info("[供应商服务推送]: partner = {}, deviceNo = {}, code = {}",
            msg.getPartner(), msg.getDeviceNo(), msg.getTaskCode());
        switch (msg.getTaskCode()) {
            case NOTIFY_NEW_LOCK:
                AnneffiLockInfo lock = JsonUtils.fromJson(msg.getData(), AnneffiLockInfo.class);
                ParkingLockPo lockPo = new ParkingLockPo()
                    .setPartner(msg.getPartner())
                    .setSerialNumber(lock.getSerialNumber())
                    .setDevUuid(lock.getDevId())
                    .setStatus(ParkingLockStatus.anneffiSwap(lock.getStatus()))
                    .setElectricQuantity(lock.getElectricQuantity())
                    .setParkingLotId(lock.getParkingLotId())
                    .setParkingLotName(lock.getParkingLotName())
                    .setParkingSpaceCode(lock.getParkingSpaceCode())
                    .setSrcDetail(msg.getData());
                parkingLockRwDs.upsetParkingLock(lockPo);
                break;
            case NOTIFY_LOCK_ERR:
                ParkingLockPo oldLock = parkingLockRwDs.getByUniqueKey(msg.getPartner(),
                    msg.getDeviceNo(), true);
                if (null == oldLock) {
                    log.warn("地锁信息不存在: error code = {}", msg.getData());
                    break;
                }

                lockPo = new ParkingLockPo()
                    .setPartner(msg.getPartner())
                    .setSerialNumber(msg.getDeviceNo())
                    .setStatus(ParkingLockStatus.ERROR)
                    .setErrorMsg(
                        AnneffiLockErrorCode.valueOf(Integer.parseInt(msg.getData())).getMsg());
                parkingLockRwDs.upsetParkingLock(lockPo);

                try {
                    if (StringUtils.isNotBlank(lockPo.getErrorMsg()) &&
                        !lockPo.getErrorMsg().equals(oldLock.getErrorMsg()) &&
                        StringUtils.isNotBlank(oldLock.getEvseNo()) &&
                        null != lockPo.getPlugId()) {
                        ParkingLockErrorParam sendParam = new ParkingLockErrorParam();
                        sendParam.setSerialNumber(oldLock.getSerialNumber())
                            .setEvseNo(oldLock.getEvseNo())
                            .setPlugId(oldLock.getPlugId())
                            .setErrorMsg(lockPo.getErrorMsg());
                        dataCoreFeignClient.sendParkingLockError(sendParam);
                    }
                } catch (Exception ex) {
                    log.error("[地锁]订阅消息推送异常: err = {}", ex.getMessage(), ex);
                }
                break;
            case NOTIFY_CAR_ARRIVE:
                lockPo = new ParkingLockPo()
                    .setPartner(msg.getPartner())
                    .setSerialNumber(msg.getDeviceNo())
                    .setCarNo(msg.getData());
                parkingLockRwDs.upsetParkingLock(lockPo);

                // 车牌识别
                eventLogService.carInEventLog(msg.getPartner(), msg.getDeviceNo(), msg.getData());
                break;
            case NOTIFY_CAR_LEAVE:
                lockPo = new ParkingLockPo()
                    .setPartner(msg.getPartner())
                    .setSerialNumber(msg.getDeviceNo())
                    .setCarNo("");
                parkingLockRwDs.upsetParkingLock(lockPo);

                // 车牌离开
                eventLogService.carOutEventLog(msg.getPartner(), msg.getDeviceNo());
                break;
            default:
                log.warn("[{}]不支持推送业务码[{}]: msg = {}",
                    msg.getPartner(), msg.getTaskCode(), msg);
        }
    }

    public void clsTimeoutJob() {
        final long now = System.currentTimeMillis();
        final ArrayList<String> timeoutList = new ArrayList<>();
        jobMap.forEach((k, v) -> {
            if (now - v.getT() > TIMEOUT_MINUTE) {
                timeoutList.add(k);
            }
        });

        if (CollectionUtils.isNotEmpty(timeoutList)) {
            timeoutList.forEach(k -> {
                final T v = jobMap.remove(k);
                if (null != v) {
                    v.exception(new DcServiceException("time out"));
                }
            });
            log.info("清理数量: {}", timeoutList.size());
        }
    }
}
