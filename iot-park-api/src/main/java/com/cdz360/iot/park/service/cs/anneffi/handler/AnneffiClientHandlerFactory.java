package com.cdz360.iot.park.service.cs.anneffi.handler;

import com.cdz360.iot.park.model.cs.ClientTaskCode;
import com.cdz360.iot.park.service.cs.common.CsClientTransHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class AnneffiClientHandlerFactory {

    private Map<ClientTaskCode, CsClientTransHandler> handlers = new HashMap<>();

    public void addHandler(ClientTaskCode taskCode, CsClientTransHandler handler) {
        this.handlers.put(taskCode, handler);
    }

    public Mono<CsClientTransHandler> getHandler(ClientTaskCode taskCode) {
        return Mono.justOrEmpty(this.handlers.get(taskCode));
    }
}
