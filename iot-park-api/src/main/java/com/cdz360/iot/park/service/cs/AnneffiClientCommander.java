package com.cdz360.iot.park.service.cs;

import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.iot.park.model.cs.CsClientEncodeBase;
import com.cdz360.iot.park.service.cs.anneffi.commander.AnneffiClientCommanderFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.io.ByteArrayOutputStream;

@Slf4j
@Service(value = "anneffiClientCommander")
public class AnneffiClientCommander implements CsClientCommander {

    @Autowired
    private AnneffiClientCommanderFactory commanderFactory;

    @Override
    public Mono<ByteArrayOutputStream> process(CsClientEncodeBase data) {
        return commanderFactory.getCommander(data.getBase().getTaskCode())
                .switchIfEmpty(Mono.error(new DcServerException("不支持的commander: code = " + data.getBase().getTaskCode())))
                .flatMap(commander -> commander.build(data));
    }
}
