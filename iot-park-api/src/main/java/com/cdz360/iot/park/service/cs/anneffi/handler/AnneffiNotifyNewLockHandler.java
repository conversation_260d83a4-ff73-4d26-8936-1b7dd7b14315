package com.cdz360.iot.park.service.cs.anneffi.handler;

import com.cdz360.iot.park.model.cs.ClientTaskCode;
import com.cdz360.iot.park.model.cs.CsClientEncodeBase;
import com.cdz360.iot.park.model.cs.CsClientTransBase;
import com.cdz360.iot.park.model.cs.EventDataBase;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class AnneffiNotifyNewLockHandler extends AnneffiAbstractClientHandler {

    private static final ClientTaskCode TASK_CODE = ClientTaskCode.NOTIFY_NEW_LOCK;

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(TASK_CODE, this);
    }

    @Override
    public Mono<ByteArrayOutputStream> process(ByteBuf msg, CsClientTransBase base) {
        long lockId = msg.readUnsignedIntLE();
        // {"id":"2c9480827b8f3408017c01f185c80019","deleted":false,"createTime":1632120178000,"updateTime":1640835348000,"code":"321011000100010027","customerCode":27,"serialNumber":"1125721429","devId":"1125721429","status":1,"type":"新能源地锁","address":"姑苏区广济北路777号平江新城摩尔商场","parkingSpaceCode":"0001","parkingLotId":"2c948088792564930179597f905e1283","parkingLotName":"国充★苏州广济路平江摩尔充电站","operatorId":"2c9480887776d2b00178fd60ee577af0","operatorName":"国充充电科技江苏股份有限公司","phone":"89860492192070706593","electricQuantity":100.0,"microwaveDistance":3.6,"parkWaitTime":30,"parkCheckTime":30,"pickCheckTime":4,"pickWaitTime":2,"alarmDuration":60}
        byte[] cntBytes = ByteBufUtil.getBytes(msg, msg.readerIndex(), msg.readableBytes() - 2);
        String lockInfo = new String(cntBytes, StandardCharsets.UTF_8);
        log.debug("收到新锁信息[{}]: {}", lockId, lockInfo);
        EventDataBase dataBase = EventDataBase.success(lockInfo)
                .setTaskCode(TASK_CODE)
                .setPartner(base.getPartner())
                .setDeviceNo("" + lockId);
        parkingLockObserver.addEventMsg(dataBase);
        parkingLockObserver.notifyEvent();
        return commanderFactory.getCommander(TASK_CODE)
                .flatMap(commander -> commander.build(new CsClientEncodeBase().setBase(base)));
    }
}
