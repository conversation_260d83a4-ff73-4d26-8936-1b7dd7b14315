package com.cdz360.iot.park.cfg;

import com.cdz360.iot.model.park.type.ParkingLockPartner;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties("iot")
public class CsClientProperties {
    private List<LotServer> lotServerList = new ArrayList<>();

    @Data
    @Accessors(chain = true)
    public static class LotServer {
        // 场站ID列表
        private List<String> siteIdList;

        // 地锁云host
        private String host;

        // 地锁云port
        private Integer port;

        @Schema(description = "地锁供应商")
        private ParkingLockPartner partner;

        // 地所云提供的客户编码
        private Long appId;

        // 地所云提供的客户密码
        private String appKey;

        public String channelKey() {
            return host + ":" + port;
        }
    }

    public LotServer getLotServer(String siteId) {
        return lotServerList.stream()
                .filter(serv -> serv.getSiteIdList().contains(siteId))
                .findFirst()
                .orElse(null);
    }

    public LotServer getLotServer(String host, Integer port) {
        return lotServerList.stream()
                .filter(serv -> serv.getHost().startsWith(host) && serv.getPort().equals(port))
                .findFirst()
                .orElse(null);
    }

    public LotServer getLotServerByChannelKey(String channelKey) {
        return lotServerList.stream()
                .filter(serv -> (serv.getHost() + ":" + serv.getPort()).equals(channelKey))
                .findFirst()
                .orElse(null);
    }
}
