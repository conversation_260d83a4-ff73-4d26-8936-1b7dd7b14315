package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.park.vo.ParkCouponVo;
import com.cdz360.iot.model.park.vo.ParkCouponYbVo;
import com.cdz360.iot.park.service.cs.common.ParkConstants;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import reactor.core.publisher.Mono;

/**
 * @Classname ParkCouponYBService
 * @Description
 * @Date 5/16/2024 4:26 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class ParkCouponYBService extends AbstractParkCouponService {

    @Autowired
    private BsService bsService;

    @PostConstruct
    public void init() {
        this.parkCouponServiceFactory.addStrategy(ParkConstants.PARK_TYPE_YB, this);
    }

    @Override
    public Mono<BaseResponse> orderStop(OrderStopRequestV2 orderStopRequestV2, ParkCouponVo data) {
        final int freeHours = data.getDuration() / 60;
        ParkCouponYbVo req = new ParkCouponYbVo();
        req.setParkCode(String.valueOf(data.getParkId()))
            .setCarNo(data.getCarNo())
            .setAppId("")
            .setIsAddUseFlage(1)
            .setFreehours(freeHours)
            .setFreeMoney(BigDecimal.ZERO)
            .setType(2)
            .setStoreName(data.getParkAppId());
        String paramAuth = getHeaderAuthYb(req, data.getParkAppSecret());
        this.setCouponYb(req, data.getParkAppId(), paramAuth).subscribe();
        return Mono.just(RestUtils.success());
    }

    /**
     * 宜泊-停车减免优惠券
     * @param param
     * @param parkCode
     * @param paramAuth
     * @return
     */
    public Mono<BaseResponse> setCouponYb(ParkCouponYbVo param, String parkCode, String paramAuth) {
        log.info("yb请求参数: {}, parkCode: {}, paramAuth: {}", param, parkCode, paramAuth);
        return bsService.setCouponYb(param, parkCode, paramAuth);
    }

    private static String getHeaderAuthYb(ParkCouponYbVo param, String appSecret) {
        String jsonStr = JsonUtils.toJsonString(param);
        String encryptStr = jsonStr + appSecret;
        String s = DigestUtils.md5DigestAsHex(encryptStr.getBytes(StandardCharsets.UTF_8));
        return s;
    }

}