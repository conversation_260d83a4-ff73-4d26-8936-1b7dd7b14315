app:
  name: IotDsTest

server:
  address: 0.0.0.0
  port: 8080
  use-forward-headers: true
  compression.enabled: true


management:
  context-path: /admin
  security:
    enabled: false

env: dev



logging:
  level:
    com.cdz360: 'DEBUG'
    org.springframework: 'INFO'
    org.springframework.transaction: DEBUG
    org.springframework.jdbc: DEBUG
    com.zaxxer.hikari: DEBUG

  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] - %logger{36}.%M\\(%line\\) - %msg%n"



#spring:
#  datasource:
#    type: com.zaxxer.hikari.HikariDataSource
#    continue-on-error: false
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    name: iot
#    username: iot
#    password: iotpass
#    url: *******************************************************************************************************************************************************************************************************
#    hikari:
#      minimumIdle: 50
#      maximumPoolSize: 200
#      idleTimeout: 30000


spring:
  h2:
    console:
      enabled: true
  datasource:
    continue-on-error: false
    driver-class-name: org.h2.Driver
    name: iot
    username: sa
    password:
    url: jdbc:h2:mem:testdbsa;mode=MySQL
    initialPoolSize: 1
    minPoolSize: 1
    maxPoolSize: 20
    acquireIncrement: 1
    maxIdleTime: 10
    checkoutTimeout: 30000

