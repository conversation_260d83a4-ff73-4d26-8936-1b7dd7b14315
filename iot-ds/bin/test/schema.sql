DROP TABLE IF EXISTS t_site;

CREATE TABLE t_site (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `name` varchar(128) DEFAULT NULL,
  `commId` bigint(20) NOT NULL,
  `dzType` int(1) DEFAULT '2',
  `status` int(4)  DEFAULT '1',
  `priceCode` bigint(20) DEFAULT NULL,
  `lon` double DEFAULT NULL,
  `lat` double DEFAULT NULL,
  `address` varchar(512) DEFAULT '' ,
  `phone` varchar(32) DEFAULT NULL,
  `provinceCode` varchar(8) DEFAULT '' ,
  `cityCode` varchar(8) DEFAULT '' ,
  `areaCode` varchar(8) DEFAULT '' ,
  `createTime` datetime NOT NULL ,
  `updateTime` datetime NOT NULL ,
  `dzId` varchar(32) DEFAULT NULL,
  UNIQUE KEY `dzId` (`dzId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ;


DROP TABLE IF EXISTS t_evse_bundle;
CREATE TABLE `t_evse_bundle` (
`id`  bigint(20) NOT NULL AUTO_INCREMENT ,
`version` bigint(20) not null comment '升级包唯一编号',
`fileName`  varchar(255) NULL COMMENT '上传升级压缩包的文件名' ,
`releaseNote`  text COMMENT '升级要点' ,
`opId` bigint(20) not null COMMENT '操作人ID' ,
`opName` varchar(32) comment '操作人姓名',
`protocol` int(4) not null comment '自描述文件版本',
`context` text comment '自描述文件内容',
`createTime`  datetime NULL COMMENT '新增时间' ,
`updateTime`  datetime NULL COMMENT '最后更新时间' ,
`enable` boolean NOT NULL  COMMENT '是否有效' ,
PRIMARY KEY (`id`),
unique key t_evse_bundle_version(`version`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ;


DROP TABLE IF EXISTS t_evse_bundle_pc;
CREATE TABLE `t_evse_bundle_pc` (
`id`  bigint(20) NOT NULL AUTO_INCREMENT ,
`bundleId`  bigint(20) NOT NULL COMMENT '关联的包id. t_evse_bundle.id' ,
`pcName`  varchar(255) NOT NULL COMMENT '模块类型,如PC01,PC02,PC01-1' ,
`hwVer`  int  not NULL COMMENT '升级后的硬件版本号' ,
`swVer`  int  not NULL COMMENT '升级后的软件版本号' ,
`vendorCode`  int  not NULL COMMENT '升级后的定制版本号' ,
`path` varchar(256) NULL COMMENT 'PC01下载地址, 不含协议和域名部分',
`createTime`  datetime NULL COMMENT '创建时间',
PRIMARY KEY (`id`),
index t_evse_bundle_pc_pcName(`pcName`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ;


DROP TABLE IF EXISTS t_evse_bundle_pc_origin;
CREATE TABLE `t_evse_bundle_pc_origin` (
`id`  bigint(20) NOT NULL AUTO_INCREMENT ,
`bundleId`  bigint(20) NOT NULL COMMENT '关联的包id.  t_evse_bundle.id' ,
`pcId`  bigint(20) NOT NULL COMMENT '关联的包id. t_evse_bundle_pc.id' ,
`pcName`  varchar(255) NOT NULL COMMENT '模块类型,如PC01,PC02,PC01-1' ,
`origin`  varchar(255) NOT NULL COMMENT '源版本信息, 格式为: 硬件版本号-软件版本号-定制号' ,
`createTime`  datetime NULL COMMENT '创建时间',
PRIMARY KEY (`id`),
index t_evse_bundle_pc_origin_pcName(`pcName`),
index t_evse_bundle_pc_origin_origin(`origin`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ;

DROP TABLE IF EXISTS t_upgrade_task;
CREATE TABLE `t_upgrade_task` (
`id`  bigint(20) NOT NULL AUTO_INCREMENT ,
`siteId`  varchar(64) NOT NULL COMMENT '场站id' ,
`bundleId`  bigint(20) NOT NULL COMMENT '升级包编码' ,
`evseCount`  int(10) NOT NULL COMMENT '升级设备总数' ,
`opId` bigint(20) not null COMMENT '操作人ID' ,
`opName` varchar(32) comment '操作人姓名',
`createTime`  datetime NOT NULL COMMENT '升级时间' ,
PRIMARY KEY (`id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ;


DROP TABLE IF EXISTS t_upgrade_task_detail;
CREATE TABLE `t_upgrade_task_detail` (
`id`  bigint(20) NOT NULL AUTO_INCREMENT ,
`taskId`  bigint(20) NOT NULL COMMENT '任务Id' ,
`evseId`  varchar(32) NOT NULL COMMENT '桩编号' ,
`pc01Ver`  varchar(255) NULL COMMENT 'PC01版本号, 格式为: 硬件版本号-软件版本号-定制版本号' ,
`pc02Ver`  varchar(255) NULL COMMENT 'PC02版本号, 格式为: 硬件版本号-软件版本号-定制版本号' ,
`pc03Ver`  varchar(255) NULL COMMENT 'PC03版本号, 格式为: 硬件版本号-软件版本号-定制版本号' ,
`status`  enum('UPDATING','UPDATED','FAIL') NULL COMMENT '升级状态' ,
`failReason`  varchar(255) NULL COMMENT '失败原因' ,
`updateTime`  datetime NULL ON UPDATE CURRENT_TIMESTAMP ,
PRIMARY KEY (`id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_upgrade_task_rel;
CREATE TABLE `t_upgrade_task_rel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `taskId` bigint(20) NOT NULL COMMENT '任务id',
  `evseIds` text COMMENT '升级的桩列表，用,号分割',
  `createTime` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_evse;
CREATE TABLE `t_evse` (
`id` BIGINT ( 20 ) NOT NULL AUTO_INCREMENT COMMENT '主键id',
`gwno` VARCHAR ( 16 ) NOT NULL COMMENT '网关编号',
`evseId` VARCHAR ( 32 ) NOT NULL COMMENT '充电桩编号',
`evseStatus` VARCHAR ( 32 ) NOT NULL COMMENT '充电桩状态',
`name` VARCHAR ( 64 ) NOT NULL DEFAULT '' COMMENT '桩名称',
`siteId` VARCHAR ( 32 ) DEFAULT NULL COMMENT '场站ID',
`supply` enum ( 'AC', 'DC', 'BOTH', 'UNKNOWN' ) NOT NULL DEFAULT 'UNKNOWN' COMMENT '电流类型',
`net` enum ( 'ETHENET', 'WIFI', 'UNKNOWN' ) NOT NULL DEFAULT 'UNKNOWN' COMMENT '网络类型',
`plugNum` SMALLINT ( 6 ) DEFAULT NULL COMMENT '充电枪数量',
`priceCode` BIGINT ( 20 ) DEFAULT NULL COMMENT '价格模板ID',
`protocol` VARCHAR ( 16 ) DEFAULT NULL COMMENT '桩协议类型',
`protocolVer` INT ( 8 ) NOT NULL DEFAULT '0' COMMENT '桩协议版本',
`firmwareVer` VARCHAR ( 32 ) NOT NULL DEFAULT '' COMMENT '桩软件版本',
`pc01Ver` VARCHAR ( 256 ) DEFAULT NULL COMMENT 'PC01版本号, 格式为: 硬件版本号-软件版本号-定制号',
`pc02Ver` VARCHAR ( 256 ) DEFAULT NULL COMMENT 'PC02版本号, 格式为: 硬件版本号-软件版本号-定制号',
`pc03Ver` VARCHAR ( 256 ) DEFAULT NULL COMMENT 'PC03版本号, 格式为: 硬件版本号-软件版本号-定制号',
`passcodeVer` BIGINT ( 20 ) DEFAULT NULL COMMENT '当前最新的密钥版本号',
`power` INT ( 11 ) DEFAULT NULL COMMENT '桩体额定功率',
`createTime` datetime NOT NULL COMMENT '记录创建时间',
`updateTime` datetime NOT NULL COMMENT '记录最后修改时间',
PRIMARY KEY ( `id` ),
UNIQUE KEY `t_evse_evseId` ( `evseId` ) USING BTREE,
KEY `t_evse_gwno` ( `gwno` ) USING BTREE,
KEY `t_evse_evseStatus` ( `evseStatus` ) USING BTREE,
KEY `t_evse_siteid` ( `siteId` ) USING BTREE
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4;

DROP TABLE IF EXISTS t_evse_passcode;
CREATE TABLE t_evse_passcode (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `evseNo` varchar(32) NOT NULL COMMENT '充电桩编号',
  `ver` bigint(20) NOT NULL COMMENT '密钥版本号',
  `passcode` varchar(32) COMMENT 'HEX编码的密钥',
  `enable` boolean NOT NULL COMMENT '是否有效',
  `createTime` datetime NOT NULL COMMENT '创建时间',
  `updateTime` datetime NOT NULL COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  KEY `t_evse_passcode_evseNo_ver` (`evseNo`, `ver`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



DROP TABLE IF EXISTS t_evse_cfg;
CREATE TABLE `t_evse_cfg` (
  `id` bigint(10) NOT NULL AUTO_INCREMENT,
  `evseNo` varchar(32) NOT null COMMENT '桩号',
  `adminPassword` varchar(20)   COMMENT '管理员密码',
  `level2Password` varchar(20) COMMENT '二级管理员密码',
  `dayVolume` int(10)  COMMENT '白天音量',
  `nightVolume` int(10)  COMMENT '夜晚音量',
  `qrUrl` varchar(100)  COMMENT '二维码url',
  `whiteCardList` text  COMMENT '紧急充电卡逻辑卡号列表，用,分隔',
  `isQueryChargeRecord` boolean COMMENT '是否支持充电记录查询 （true是false否）',
  `isTimedCharge` boolean  COMMENT '是否支持定时充电 （true是false否）',
  `isNoCardCharge` boolean COMMENT '是否支持无卡充电 （true是false否）',
  `isScanCharge` boolean COMMENT '是否支持扫码充电 （true是false否）',
  `isVinCharge` boolean COMMENT '是否支持Vin码充电（true是false否）',
  `isCardCharge` boolean COMMENT '是否支持刷卡充电 （true是false否）',
  `isQuotaEleCharge` boolean COMMENT '是否支持定额电量充电 （true是false否）',
  `isQuotaMoneyCharge` boolean COMMENT '是否支持固定金额充电（true是false否）',
  `isQuotaTimeCharge` boolean COMMENT '是否支持固定时长充电 （true是false否）',
  `opUid` bigint(20) COMMENT '操作人id',
  `enable` boolean comment '（true有效false无效）',
  `createTime` datetime DEFAULT NULL COMMENT '创建时间',
  `updateTime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `t_evse_cfg_evseNo` (`evseNo`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;



DROP TABLE IF EXISTS t_evse_cfg_result;
CREATE TABLE `t_evse_cfg_result` (
  `id` bigint(10) NOT NULL AUTO_INCREMENT,
  `evseNo` varchar(32) NOT null COMMENT '桩号',
  priceCodeResult int(4) comment '价格模板下发结果. 0, 成功; 300, 下发中; 301, 超时; 302, 桩离线; 其他都为桩端返回失败码',
  expectPriceCode bigint(20) comment '期望的价格模板ID',
  actualPriceCode bigint(20) comment '当前生效的价格模板ID',
  cfgResult int(4) comment '桩配置下发结果. 0, 成功; 300, 下发中; 301, 超时; 302, 桩离线; 其他都为桩端返回失败码',
  expectCfgCode bigint(20) comment '期望的桩配置ID',
  actualCfgCode bigint(20) comment '当前生效的桩配置ID',
  whiteCardResult int(4) comment '白名单列表下发结果. 0, 成功; 300, 下发中; 301, 超时; 302, 桩离线; 其他都为桩端返回失败码',
  expectWhiteCardCode bigint(20) comment '期望的白名单配置ID',
  actualWhiteCardCode bigint(20) comment '当前生效的白名单配置ID',
  `enable` boolean comment '（true有效false无效）',
  `createTime` datetime DEFAULT NULL COMMENT '创建时间',
  `updateTime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `t_evse_cfg_result_evseNo` (`evseNo`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_evse_bind_history;
CREATE TABLE t_evse_bind_history (
`id`  bigint(20) NOT NULL AUTO_INCREMENT  ,
`siteId`  varchar(32) NULL ,
`evseNo`  varchar(32) NOT NULL ,
`time`  datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP ,
`action`  enum('BIND','UNBIND') NULL COMMENT '绑定，解绑' ,
`createTime`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
`updateTime`  datetime NULL ON UPDATE CURRENT_TIMESTAMP ,
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `t_site_ctrl`;
CREATE TABLE `t_site_ctrl` (
`id`  bigint(20) NOT NULL AUTO_INCREMENT ,
`num`  varchar(16) NOT NULL COMMENT '编号' ,
`siteId` varchar(32) NOT NULL DEFAULT '' COMMENT '站点编号',
`name`  varchar(32) NULL ,
`status`  enum('STARTUP','WORK','ALERT','ERROR','UPGRADE','DEBUG','OFFLINE','ONLINE') NULL ,
`passcode`  varchar(18) NULL ,
`protocolVer`  int(4) NULL ,
`swVer`  varchar(16) NULL ,
`lanIp`  varchar(64) NULL ,
`mac`  varchar(32) NULL ,
`enable`  tinyint(1) NOT NULL DEFAULT 1 ,
`loadRatio`  decimal(7,2) NULL COMMENT '最新负载率' ,
`pwrTemp`  decimal(7,2) NULL COMMENT '最新配电柜温度' ,
`loginTime`  datetime NULL ,
`createTime`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
`updateTime`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP ,
PRIMARY KEY (`id`),
  UNIQUE KEY `idx_ctrlNum` (`num`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `t_site_ctrl_cfg`;
CREATE TABLE `t_site_ctrl_cfg` (
`id`  bigint(20) NOT NULL AUTO_INCREMENT ,
`ctrlNum`  varchar(32) NOT NULL ,
`pwrCtrl`  tinyint(1) NULL COMMENT '功率分配功能开关' ,
`infoUp`  tinyint(1) NULL COMMENT '监测信息上报开关' ,
`pwrLoadAlm`  tinyint(1) NULL COMMENT '功率负荷报警开关' ,
`pwrTempAlm`  tinyint(1) NULL COMMENT '配电柜温度报警开关' ,
`chgFireAlm`  tinyint(1) NULL COMMENT '充电桩烟雾报警开关' ,
`chgDoorAlm`  tinyint(1) NULL COMMENT '充电桩门禁报警开关' ,
`pwrCap`  tinyint(1) NULL COMMENT '配电容量,功率分配功能为开时必须发送' ,
`pwrCtrlLmt`  text NULL COMMENT 'JSON功率限定策略,功率分配功能为开时必须发送' ,
`tempSample`  int(4) NULL COMMENT '配电柜温度信息采样周期（秒钟）,监测信息上报为开时必须发送' ,
`pwrSample`  int(4) NULL COMMENT '功率负载采样周期（秒钟）,监测信息上报为开时必须发送' ,
`infoUpLoop`  int(4) NULL COMMENT '监测信息上报时间间隔（秒钟）,监测信息上报为开时必须发送' ,
`pwrLoadLmt`  int(4) NULL COMMENT '负载率报警阈值,功率负荷报警为开时必须发送' ,
`pwrTempLmt`  int(4) NULL COMMENT '温度报警阈值,配电柜温度报警为开时必须发送' ,
`createTime`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
`updateTime`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP ,
PRIMARY KEY (`id`),
  UNIQUE KEY `idx_ctrlNum_cfg` (`ctrlNum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `t_site_ctrl_cfg_log`;
CREATE TABLE `t_site_ctrl_cfg_log` (
`id`  bigint(20) NOT NULL AUTO_INCREMENT ,
`ctrlNum`  varchar(32) NOT NULL ,
`status`  enum('SUCCESS','SENDING','FAIL','UNSEND','INIT') NULL ,
`triggerRst`  int(4) NULL COMMENT '各种开关项配置结果,不传表示不做变更,0x00：成功,其它表示失败' ,
`pwrCtrlRst`  int(4) NULL COMMENT '功率限定配置结果,不传表示不做变更,0x00：成功,其它表示失败' ,
`infoUpRst`  int(4) NULL COMMENT '监测信息上报配置结果,不传表示不做变更,0x00：成功,其它表示失败' ,
`loadAlmRst`  int(4) NULL COMMENT '功率负载预警配置结果,不传表示不做变更,0x00：成功,其它表示失败' ,
`pwrTempRst`  int(4) NULL COMMENT '温度预警配置结果,不传表示不做变更,0x00：成功,其它表示失败' ,
`createTime`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
`updateTime`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP ,
PRIMARY KEY (`id`),
  UNIQUE KEY `idx_ctrlNum_log` (`ctrlNum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 配件操作日志
DROP TABLE IF EXISTS `t_parts_op_log`;
CREATE TABLE `t_parts_op_log` (
`id` bigint(20) NOT NULL AUTO_INCREMENT,
`opType` enum('STORE_2_SEND','RECEIVE_','APPLY_4_STORE','APPROVE_AND_SEND','STORE_2_EVSE','EVSE_2_STORE','STATUS_MODIFY','STORE_ROLLBACK','STORE_BROKEN') NOT NULL COMMENT '操作类型:STORE_2_SEND("总库发货");RECEIVE_("签收物料");APPLY_4_STORE("申请调拨");APPROVE_AND_SEND("同意调拨");STORE_2_EVSE("物料被使用");EVSE_2_STORE("物料被拆卸");STATUS_MODIFY("状态修改");STORE_ROLLBACK("物料退回总部");STORE_BROKEN("报废")',
`opUid` bigint(20) DEFAULT 0 COMMENT '操作人员ID(sys_user.id)',
`detail` text comment '操作记录内容:{name,status,orderNo}',
`partsCode` varchar(16) NOT NULL COMMENT '配件唯一编码(t_parts.code)',
`createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
PRIMARY KEY (`id`),
KEY `t_parts_op_log_partsCode` (`partsCode`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 储能ESS充放电时段参数配置
DROP TABLE IF EXISTS `t_ess_in_out_cfg`;
CREATE TABLE `t_ess_in_out_cfg` (
  `cfgId` bigint(20) NOT NULL COMMENT '模板id(t_dev_cfg.id)',
  `coupleMode` enum('UNKNOWN','AC','DC','HYBRID') NOT NULL DEFAULT 'UNKNOWN' COMMENT '模板类型: AC(AC模式); DC(DC模式); HYBRID(混合模式)',
  `demandCtrlEnable` tinyint(1) NOT NULL COMMENT '需量控制使能',
  `demand` int(16) DEFAULT '0' COMMENT '最大需量阈值',
  `capacity` int(16) DEFAULT '0' COMMENT '变压器容量',
  `inEnable` tinyint(1) NOT NULL COMMENT '充电使能',
  `inPower` decimal(10,2) DEFAULT NULL COMMENT '充电功率, 单位: kW',
  `inTime` text DEFAULT NULL COMMENT '充电时间段, 单位: 分钟: [{start: 100, end: 600}]',
  `inStopSoc` int(4) DEFAULT NULL COMMENT '充电截止SOC',
  `outEnable` tinyint(1) NOT NULL COMMENT '放电使能',
  `outStopSoc` int(4) DEFAULT NULL COMMENT '放电截止SOC',
  `outTime` text DEFAULT NULL COMMENT '充电时间段, 单位: 分钟: [{start: 100, end: 600}]',
  UNIQUE KEY `t_ess_cfg_cfgId` (`cfgId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ;