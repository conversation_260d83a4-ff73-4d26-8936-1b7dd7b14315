insert into t_site
(name, `commId`, `dzType`, status, lon, lat, address, `provinceCode`, `cityCode`, `areaCode`, `createTime`, `updateTime`, `dzId`)
VALUES('单元测试站点', 33421, 2, 1, 121.421864, 31.360608, '', '310000', '310100', '310113', '2019-08-15 09:20:18.000', '2019-08-15 09:20:18.000', '20190815101854281680527169');

insert into t_evse_bundle
(`id`, `version`, `fileName`, `releaseNote`, `opId`, `opName`, `protocol`, `context`, `createTime`, `updateTime`, `enable`)
VALUES (56, 3, 'Evse_Bundle_File呵呵.zip', '1、新增紧急充电卡功能 <br/> 2、修复订单异常bug', 1, '默认管理员', 1, '{\r\n    \"protocol\": 1,\r\n    \"ver\": 3,\r\n	\"pc0xList\" :[\r\n		{\r\n			\"type\":\"PC01\",\r\n			\"hw\": \"1\",\r\n			\"sw\": \"325\",\r\n			\"oder\": \"11\",\r\n			\"adaptive\": {\r\n				\"hw\": [\r\n					1,\r\n					2,\r\n					3,\r\n					4\r\n				],\r\n				\"sw\": [\r\n					321,\r\n					323,\r\n					324\r\n				],\r\n				\"oder\": [\r\n					11\r\n				]\r\n			}\r\n		},\r\n    {	\"type\":\"PC02\",\r\n        \"hw\": 1,\r\n        \"sw\": \"5\",\r\n        \"oder\": \"11\",\r\n        \"adaptive\": {\r\n            \"hw\": [\r\n                1,\r\n                2,\r\n                3,\r\n                4\r\n            ],\r\n            \"sw\": [\r\n                1,\r\n                3,\r\n                4\r\n            ],\r\n            \"oder\": [\r\n                11\r\n            ]\r\n        }\r\n    },\r\n    {\r\n		\"type\":\"PC03\",\r\n        \"hw\": 104,\r\n        \"sw\": \"15\",\r\n        \"oder\": \"11\",\r\n        \"adaptive\": {\r\n            \"hw\": [\r\n                101,\r\n                102\r\n            ],\r\n            \"sw\": [\r\n                9,\r\n                13,\r\n                14\r\n            ],\r\n            \"oder\": [\r\n                13\r\n            ]\r\n        }\r\n    }],\r\n    \"releaseNote\": [\r\n        \"1、新增紧急充电卡功能\",\r\n        \"2、修复订单异常bug\"\r\n    ]\r\n}\r\n', '2019-09-23 15:06:23', '2019-09-23 15:06:30', 1);

insert into t_evse_bundle
(`id`, `version`, `fileName`, `releaseNote`, `opId`, `opName`, `protocol`, `context`, `createTime`, `updateTime`, `enable`)
VALUES (57, 4, 'Evse_Bundle_File呵呵.zip', '1、新增紧急充电卡功能 <br/> 2、修复订单异常bug', 1, '默认管理员', 1, '{\r\n    \"protocol\": 1,\r\n    \"ver\": 3,\r\n	\"pc0xList\" :[\r\n		{\r\n			\"type\":\"PC01\",\r\n			\"hw\": \"1\",\r\n			\"sw\": \"325\",\r\n			\"oder\": \"11\",\r\n			\"adaptive\": {\r\n				\"hw\": [\r\n					1,\r\n					2,\r\n					3,\r\n					4\r\n				],\r\n				\"sw\": [\r\n					321,\r\n					323,\r\n					324\r\n				],\r\n				\"oder\": [\r\n					11\r\n				]\r\n			}\r\n		},\r\n    {	\"type\":\"PC02\",\r\n        \"hw\": 1,\r\n        \"sw\": \"5\",\r\n        \"oder\": \"11\",\r\n        \"adaptive\": {\r\n            \"hw\": [\r\n                1,\r\n                2,\r\n                3,\r\n                4\r\n            ],\r\n            \"sw\": [\r\n                1,\r\n                3,\r\n                4\r\n            ],\r\n            \"oder\": [\r\n                11\r\n            ]\r\n        }\r\n    },\r\n    {\r\n		\"type\":\"PC03\",\r\n        \"hw\": 104,\r\n        \"sw\": \"15\",\r\n        \"oder\": \"11\",\r\n        \"adaptive\": {\r\n            \"hw\": [\r\n                101,\r\n                102\r\n            ],\r\n            \"sw\": [\r\n                9,\r\n                13,\r\n                14\r\n            ],\r\n            \"oder\": [\r\n                13\r\n            ]\r\n        }\r\n    }],\r\n    \"releaseNote\": [\r\n        \"1、新增紧急充电卡功能\",\r\n        \"2、修复订单异常bug\"\r\n    ]\r\n}\r\n', '2019-09-23 15:06:23', '2019-09-23 15:06:30', 1);

insert into t_evse_bundle_pc
(`id`, `bundleId`, `pcName`, `hwVer`, `swVer`, `vendorCode`, `path`, `createTime`)
VALUES (254, 56, 'PC01', 1, 325, 11, '56/PC01', '2019-09-24 09:04:45');

insert into t_evse_bundle_pc
(`id`, `bundleId`, `pcName`, `hwVer`, `swVer`, `vendorCode`, `path`, `createTime`)
VALUES (255, 57, 'PC01', 1, 325, 11, '57/PC01', '2019-09-24 09:04:45');

insert into t_evse_bundle_pc_origin
(`id`, `bundleId`, `pcId`, `pcName`, `origin`, `createTime`)
VALUES (2131, 56, 218, 'PC01', '1-321-11', '2019-09-23 13:17:35');

insert into t_evse_bundle_pc_origin
(`id`, `bundleId`, `pcId`, `pcName`, `origin`, `createTime`)
VALUES (2132, 57, 218, 'PC01', '1-323-11', '2019-09-23 13:17:35');

insert into t_upgrade_task
(`id`, `siteId`, `bundleId`, `evseCount`, `opId`, `opName`, `createTime`)
VALUES (1, '20190826195336575502558871', 1, 1, 1, NULL, '2019-09-19 09:10:34');

insert into t_upgrade_task_detail
(`id`, `taskId`, `evseId`, `pc01Ver`, `pc02Ver`, `pc03Ver`, `status`, `failReason`, `updateTime`)
VALUES (1, 1, '028888831050', '111-222-333', NULL, NULL, 'UPDATING', NULL, '2019-09-23 11:22:36');

insert into t_evse
(`id`, `gwno`, `evseId`, `evseStatus`, `name`, `siteId`, `supply`, `net`, `plugNum`, `priceCode`, `protocol`, `protocolVer`, `firmwareVer`, `pc01Ver`, `pc02Ver`, `pc03Ver`, `passcodeVer`, `power`, `createTime`, `updateTime`)
VALUES (1809243, 'abc8765', '013013744187', 'OFFLINE', '', '20190813952219002583048590', 'AC', 'WIFI', 1, NULL, 'CCTIA', 350, '123-345-653', NULL, NULL, NULL, NULL, NULL, '2019-03-01 17:17:40', '2019-07-24 19:12:02');

INSERT INTO `t_evse_bind_history` (`siteId`, `evseNo`, `time`, `action`)
VALUES ('siteId', 'evseNo', '2020-03-17 14:00:00', 'BIND');
INSERT INTO `t_evse_bind_history` (`siteId`, `evseNo`, `time`, `action`)
VALUES ('siteId', 'evseNo', '2020-03-17 15:00:00', 'UNBIND');
INSERT INTO `t_evse_bind_history` (`siteId`, `evseNo`, `time`, `action`)
VALUES ('siteId', 'evseNo', '2020-03-17 16:00:00', 'BIND');
INSERT INTO `t_evse_bind_history` (`siteId`, `evseNo`, `time`, `action`)
VALUES ('siteId', 'evseNo', '2020-03-17 17:00:00', 'UNBIND');
INSERT INTO `t_evse_bind_history` (`siteId`, `evseNo`, `time`, `action`)
VALUES ('siteId', 'evseNo', '2020-03-17 18:00:00', 'BIND');
INSERT INTO `t_evse_bind_history` (`siteId`, `evseNo`, `time`, `action`)
VALUES ('siteId', 'evseNo', '2020-03-17 19:00:00', 'UNBIND');
INSERT INTO `t_evse_bind_history` (`siteId`, `evseNo`, `time`, `action`)
VALUES ('siteId', 'evseNo', '2020-03-17 20:00:00', 'BIND');
INSERT INTO `t_evse_bind_history` (`siteId`, `evseNo`, `time`, `action`)
VALUES ('siteId', 'evseNo', '2020-03-17 21:00:00', 'UNBIND');
INSERT INTO `t_evse_bind_history` (`siteId`, `evseNo`, `time`, `action`)
VALUES ('siteId', 'evseNo', '2020-03-17 22:00:00', 'BIND');
INSERT INTO `t_evse_bind_history` (`siteId`, `evseNo`, `time`, `action`)
VALUES ('siteId', 'evseNo', '2020-03-17 23:00:00', 'UNBIND');
INSERT INTO `t_evse_bind_history` (`siteId`, `evseNo`, `time`, `action`)
VALUES ('siteId', 'evseNo', '2020-03-17 23:30:00', 'BIND');



INSERT INTO t_ess_in_out_cfg
(cfgId, coupleMode, demandCtrlEnable, demand, capacity, inEnable, inPower, inTime, inStopSoc, outEnable, outStopSoc, outTime)
VALUES(197, 'AC', 1, 10, 10, 0, NULL, '[{"end": 600, "soc": 90, "start": 0, "flowType": 1, "activePower": 60.123}, {"end": 1440, "soc": 95, "start": 1200, "flowType": 1, "activePower": 80.345}]', NULL, 0, NULL, '[{"end": 1200, "soc": 30, "start": 600, "flowType": 2, "activePower": 20.345}]');
