<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.EvsePackageRoMapper">


  <resultMap id="EVSEPACKAGE" type="com.cdz360.iot.model.evse.vo.EvsePackageVo">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="type" property="type" typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="brand" property="brand" jdbcType="VARCHAR"/>
    <result column="packageName" property="packageName" jdbcType="VARCHAR"/>
    <result column="status" property="status" jdbcType="INTEGER"/>
    <result column="version" property="version" jdbcType="VARCHAR"/>
    <result column="packageInfo" property="packageInfo"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="account" property="account" jdbcType="VARCHAR"/>
    <result column="password" property="password" jdbcType="VARCHAR"/>
    <result column="opName" property="opName" jdbcType="VARCHAR"/>
  </resultMap>

  <sql id="query_list">
    <where>
      enable = true
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( keyWord )">
        and packageName like CONCAT('%', #{keyWord}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( brand )">
        and brand = #{brand}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( version )">
        and version like CONCAT('%', #{version}, '%')
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
    </where>
  </sql>

  <select id="getList" resultMap="EVSEPACKAGE">
    select * from t_evse_package
    <include refid="query_list"/>
    order by id desc
    limit #{start}, #{size}
  </select>

  <select id="getCount" resultType="java.lang.Long">
    select count(*)from t_evse_package
    <include refid="query_list"/>
  </select>

  <select id="getBrandList" resultType="java.lang.String">
    SELECT DISTINCT
    brand
    FROM
    t_evse_package
    WHERE
    `enable` = 1
  </select>


</mapper>