<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssGeneratorCfgRoMapper">



	<resultMap id="RESULT_ESSGENERATORCFG_PO" type="com.cdz360.iot.model.ess.po.EssGeneratorCfgPo">

		<result column="cfgId" jdbcType="BIGINT" property="cfgId" />

		<result column="ctrlEnable" jdbcType="BOOLEAN" property="ctrlEnable" />

		<result column="stopMode" property="stopMode" />

		<result column="startSoc" jdbcType="INTEGER" property="startSoc" />

		<result column="stopSoc" jdbcType="INTEGER" property="stopSoc" />

		<result column="startTime" jdbcType="INTEGER" property="startTime" />

		<result column="stopTime" jdbcType="INTEGER" property="stopTime" />

		<result column="powerOutMode" property="powerOutMode" />

		<result column="chargePower" jdbcType="DECIMAL" property="chargePower" />

		<result column="ratedPower" jdbcType="DECIMAL" property="ratedPower" />

	</resultMap>
    <select id="getById" resultType="com.cdz360.iot.model.ess.po.EssGeneratorCfgPo">
		select * from t_ess_generator_cfg where cfgId = #{id}
	</select>


</mapper>

