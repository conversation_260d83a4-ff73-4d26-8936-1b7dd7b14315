<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.GwInfoRoMapper">


  <resultMap type="com.cdz360.iot.model.site.dto.GwInfoDto" id="gwCommonInfoDto">
    <id column="id" property="id"/>
    <result column="gwno" property="gwno"/>
    <result column="ip" property="ip"/>
    <result column="siteId" property="siteId"/>
    <result column="status" property="status"/>
    <result column="mqType" property="mqType" jdbcType="INTEGER"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="ver" property="ver"/>
    <result column="cityCode" property="cityCode"/>
    <result column="siteName" property="siteName"/>
    <result column="lon" property="lon"/>
    <result column="lat" property="lat"/>
    <result column="lanIp" property="lanIp"/>
    <result column="mac" property="mac"/>
    <result column="bootTime" property="bootTime"/>
    <result column="swVer" property="swVer"/>
    <result column="swVerCode" property="swVerCode"/>
    <result column="sourceCodeVer" property="sourceCodeVer"/>

    <result column="createTime" property="createTime"/>
    <result column="updateTime" property="updateTime"/>
  </resultMap>

  <select id="getByGwno" resultMap="gwCommonInfoDto"
    resultType="com.cdz360.iot.model.site.po.GwInfoPo">
    select gw.*, sr.siteId, site.name siteName from t_gw_info gw
    left join t_gw_site_ref sr on sr.gwno = gw.gwno
    left join t_site site on site.dzId = sr.siteId
    where gw.gwno=#{gwno}
  </select>

  <select id="getByGwnoAndEnable" resultMap="gwCommonInfoDto"
    resultType="com.cdz360.iot.model.site.po.GwInfoPo">
    select * from t_gw_info where gwno=#{gwno} and `enable`=TRUE

  </select>

  <resultMap type="com.cdz360.iot.model.site.dto.GwInfoDto" id="gwInfoMac">
    <id column="id" property="id"/>
    <result column="ip" property="ip"/>
    <result column="lon" property="lon"/>
    <result column="lat" property="lat"/>
    <result column="status" property="status"/>
    <result column="mqType" property="mqType" jdbcType="INTEGER"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="ver" property="ver"/>

    <result column="cityCode" property="cityCode"/>
    <result column="createTime" property="createTime"/>
    <result column="updateTime" property="updateTime"/>
    <result column="lanIp" property="lanIp"/>
    <result column="mac" property="mac"/>
  </resultMap>

  <select id="getGwList" resultMap="gwInfoMac" resultType="com.cdz360.iot.model.site.po.GwInfoPo">
    select * from t_gw_info where gwno in
    <foreach collection="gwnos" item="gwno" open="(" close=")" separator=",">
      #{gwno}
    </foreach>
    <if test="status != null">
      and status=#{status}
    </if>
  </select>

  <select id="getByMac" resultMap="gwCommonInfoDto"
    resultType="com.cdz360.iot.model.site.po.GwInfoPo">
    select * from t_gw_info where mac=#{mac}

  </select>


  <select id="listGw" resultMap="gwInfoMac" resultType="com.cdz360.iot.model.site.po.GwInfoPo">
    select * from t_gw_info where 1=1
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( statusList )">
      and `status` in
      <foreach collection="statusList" open="(" close=")"
        separator="," item="status">
        #{status}
      </foreach>
    </if>
    <if test="mqType != null">
      and mqType = #{mqType.code}
    </if>
    <if test="updateTime != null">
      and updateTime &gt; #{updateTime}
    </if>
    order by id
    limit #{start},#{size}
  </select>

  <select id="getGwnosLoginTout" resultType="com.cdz360.iot.model.gw.GwTimeoutPo">
    select
    gwno,
    <!--            siteId siteNum,-->
    loginTime
    from t_gw_info
    where timestampdiff(MINUTE,loginTime,now()) > #{timeout} OR `status` ='OFFLINE';
  </select>

  <select id="getByKeyword" resultMap="gwCommonInfoDto"
    resultType="com.cdz360.iot.model.site.po.GwInfoPo">
    select * from t_gw_info where gwno=#{keyword} or name=#{keyword}
  </select>

</mapper>