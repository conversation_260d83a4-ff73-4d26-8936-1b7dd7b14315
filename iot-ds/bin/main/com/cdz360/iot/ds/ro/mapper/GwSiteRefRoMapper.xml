<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.GwSiteRefRoMapper">

  <resultMap type="com.cdz360.iot.model.site.dto.GwInfoDto" id="gwCommonInfoDto">
    <id column="id" property="id"/>
    <result column="gwno" property="gwno"/>
    <result column="passcode" property="passcode"/>
    <result column="ip" property="ip"/>
    <result column="siteId" property="siteId"/>
    <result column="siteCommId" property="siteCommId"/>
    <result column="siteName" property="siteName"/>
    <result column="status" property="status"/>
    <result column="mqType" property="mqType" jdbcType="INTEGER"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="ver" property="ver"/>
    <result column="cityCode" property="cityCode"/>
    <result column="siteName" property="siteName"/>
    <result column="lon" property="lon"/>
    <result column="lat" property="lat"/>
    <result column="lanIp" property="lanIp"/>
    <result column="mac" property="mac"/>

    <result column="createTime" property="createTime"/>
    <result column="updateTime" property="updateTime"/>
  </resultMap>

  <resultMap id="ctrlVoListMap" type="com.cdz360.iot.model.site.vo.DeviceInfoVo">
    <result column="siteId" jdbcType="VARCHAR" property="siteId"/>
    <collection property="gwInfoList" ofType="com.cdz360.iot.model.site.vo.DeviceInfoVoDetail"
      javaType="list">
      <result column="gwno" jdbcType="VARCHAR" property="gwno"/>
      <result column="num" jdbcType="BIGINT" property="num"/>
    </collection>
  </resultMap>


  <select id="listAllGwSiteRef" resultType="com.cdz360.iot.model.site.po.GwSiteRefPo">
    select * from t_gw_site_ref where enable = true
    order by gwno
  </select>

  <select id="getGwInfo" resultMap="gwCommonInfoDto"
    resultType="com.cdz360.iot.model.site.po.GwInfoPo">
    select gw.* from t_gw_site_ref as ref
    left join t_gw_info as gw on ref.gwno = gw.gwno
    where ref.enable = true and ref.siteId = #{siteId} limit 1
  </select>

  <select id="getGwInfoByGwno" resultMap="gwCommonInfoDto">
    select gw.*, ref.siteId siteId, site.commId siteCommId, site.name siteName
    from t_gw_site_ref as ref
    left join t_site site on site.dzId = ref.siteId
    left join t_gw_info as gw on ref.gwno = gw.gwno
    where ref.gwno = #{gwno}
    <if test="null != enable and enable">
      and ref.enable = #{enable}
    </if>
    limit 1
  </select>

  <select id="countCtrl" resultType="java.lang.Long">
    select
    count(distinct ref.gwno) ctrlNum
    from
    t_gw_site_ref ref
    left join t_gw_info gw on
    ref.gwno = gw.gwno
    where
    ref.siteId = #{siteId}
    and ref.enable = true
  </select>

  <select id="countGti" resultType="java.lang.Long">
    select
    count(distinct gti.dno) gtiNum
    from
    t_gw_site_ref ref
    left join t_gw_info gw on
    ref.gwno = gw.gwno
    left join t_gti gti on
    gti.gwno = ref.gwno
    where
    ref.siteId = #{siteId}
    and ref.enable = true
    and gti.status != 99
  </select>

  <select id="countEss" resultType="java.lang.Long">
    select
    count(distinct ess.dno) essNum
    from
    t_gw_site_ref ref
    left join t_gw_info gw on
    ref.gwno = gw.gwno
    left join t_ess ess on
    ess.gwno = ref.gwno
    where
    ref.siteId = #{siteId}
    and ref.enable = true
    and ess.status != 99
  </select>

  <select id="findCtrlVoList" resultMap="ctrlVoListMap">
    select
    ref.siteId,
    gw.gwno gwno,
    count(gti.dno) as num
    from
    t_gw_site_ref ref
    left join t_gw_info gw on
    ref.gwno = gw.gwno
    left join t_gti gti on
    gti.gwno = ref.gwno
    where
    ref.enable = true
    and gti.status in (1, 2)
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and ref.siteId in
      <foreach collection="siteIdList" open="(" close=")"
        separator="," item="item" index="index">
        #{item}
      </foreach>
    </if>
    group by
    ref.siteId,
    gw.gwno
  </select>

  <select id="findSrsVoList" resultMap="ctrlVoListMap">
    select
    ref.siteId,
    gw.gwno gwno,
    count(ts.dno) as num
    from
    t_gw_site_ref ref
    left join t_gw_info gw on
    ref.gwno = gw.gwno
    left join t_srs ts on
    ts.gwno = ref.gwno
    where
    ref.enable = true
    and ts.status in (1, 2)
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and ref.siteId in
      <foreach collection="siteIdList" open="(" close=")"
        separator="," item="item" index="index">
        #{item}
      </foreach>
    </if>
    group by
    ref.siteId,
    gw.gwno
  </select>

  <select id="findCtrlList"
    parameterType="com.cdz360.iot.model.pv.param.ListCtrlParam"
    resultType="com.cdz360.iot.model.site.vo.GwInfoVo">
    select gw.gwno gwno, gw.swVerCode swVerCode, any_value(gw.name) name, any_value(gw.status)
    status,
    any_value('光储充控制器') type, any_value(ref.siteId) siteId,
    any_value(site.name) as siteName,
    any_value(site.dzId) as siteId,
    any_value(log.upgradeStatus) upgradeStatus
    from t_gw_site_ref ref
    <!--<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">-->
    left join t_site site on site.dzId = ref.siteId
    left join t_r_commercial comm on comm.id = site.commId
    <!--</if>-->
    left join t_gw_info gw on ref.gwno = gw.gwno
    left join t_upgrade_log log on log.deviceNo = gw.gwno
    left join t_gti gti on gti.gwno = ref.gwno and gti.status != 99
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( tempName )">
      left join t_dev_cfg gtiCfg on gtiCfg.id = gti.cfgSuccessId
    </if>
    left join t_ess ess on ess.gwno = gw.gwno and ess.status != 99
    left join t_ess_equip tee on ess.dno = tee.essDno and tee.enable = true
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( tempName )">
      left join t_dev_cfg essCfg on essCfg.id = ess.cfgSuccessId
    </if>
    where 1=1
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and ref.siteId = #{siteId}
    </if>

    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and ref.siteId IN
      <foreach collection="siteIdList" index="index" item="item"
        open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>

    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.`idChain` like concat(#{commIdChain}, '%')
    </if>

    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( devNo )">
      and (gw.gwno = #{devNo}
      or gti.id = #{devNo}
      or gti.sid = #{devNo}
      or ess.sn = #{devNo}
      or tee.id = #{devNo})
    </if>

    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( devName )">
      and (gw.name like concat('%', #{devName}, '%')
      or gti.name like concat('%', #{devName}, '%')
      or ess.name like concat('%', #{devName}, '%')
      or tee.name like concat('%', #{devName}, '%'))
    </if>

    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( tempName )">
      and (gtiCfg.name = #{tempName} or essCfg.name = #{tempName})
    </if>

    and ref.enable = true
    group by ref.gwno
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          any_value(${sort.columnsString}) ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by any_value(ref.id) desc
      </otherwise>
    </choose>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <when test="size != null">
        limit #{size}
      </when>
      <!--<otherwise>
          limit #{size}
      </otherwise>-->
    </choose>
  </select>
  <select id="findCtrlListCount" resultType="java.lang.Long">
    select count(*) from (
    select gw.gwno
    from t_gw_site_ref ref
    <!--        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">-->
    left join t_site site on site.dzId = ref.siteId
    left join t_r_commercial comm on comm.id = site.commId
    <!--        </if>-->
    left join t_gw_info gw on ref.gwno = gw.gwno
    left join t_gti gti on gti.gwno = ref.gwno
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( tempName )">
      left join t_dev_cfg gtiCfg on gtiCfg.id = gti.cfgSuccessId
    </if>
    left join t_ess ess on ess.gwno = gw.gwno
    left join t_ess_equip tee on ess.dno = tee.essDno
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( tempName )">
      left join t_dev_cfg essCfg on essCfg.id = ess.cfgSuccessId
    </if>
    where 1=1
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and ref.siteId = #{siteId}
    </if>

    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and ref.siteId IN
      <foreach collection="siteIdList" index="index" item="item"
        open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>

    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.`idChain` like concat(#{commIdChain}, '%')
    </if>

    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( devNo )">
      and (gw.gwno = #{devNo}
      or gti.id = #{devNo}
      or gti.sid = #{devNo}
      or ess.sn = #{devNo}
      or tee.id = #{devNo})
    </if>

    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( devName )">
      and (gw.name like concat('%', #{devName}, '%')
      or gti.name like concat('%', #{devName}, '%')
      or ess.name like concat('%', #{devName}, '%')
      or tee.name like concat('%', #{devName}, '%'))
    </if>

    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( tempName )">
      and (gtiCfg.name = #{tempName} or essCfg.name = #{tempName})
    </if>

    and ref.enable = true
    group by ref.gwno
    ) temp
  </select>
  <select id="getGwInfoByName" resultMap="gwCommonInfoDto">
    select gw.*, ref.siteId siteId, site.commId siteCommId, site.name siteName
    from t_gw_site_ref as ref
    left join t_site site on site.dzId = ref.siteId
    left join t_gw_info as gw on ref.gwno = gw.gwno
    where gw.name like concat(#{name}, '%')
    <if test="excludeGwno != null">
      and ref.gwno != #{excludeGwno}
    </if>
    and ref.enable = true
    limit 1
  </select>

  <select id="findGtiAndEssListByGwno" resultType="com.cdz360.iot.model.pv.dto.UpdateCtrlDeviceDto">
    select
    'PV' as deviceType,
    dno ,
    name ,
    sid ,
    vendor,
    status
    from
    t_gti
    where
    status != 99
    and gwno in
    <foreach item="gwno" collection="gwnoList"
      open="(" close=")" separator=",">
      #{gwno}
    </foreach>
    union all
    select
    'ESS' as deviceType,
    dno ,
    name ,
    null as sid,
    vendor,
    status
    from
    t_ess
    where
    status != 99
    and gwno in
    <foreach item="gwno" collection="gwnoList"
      open="(" close=")" separator=",">
      #{gwno}
    </foreach>
    union all
    select
    'SRS' as deviceType,
    dno ,
    name ,
    sid ,
    vendor,
    status
    from
    t_srs
    where
    status != 99
    and gwno in
    <foreach item="gwno" collection="gwnoList"
      open="(" close=")" separator=",">
      #{gwno}
    </foreach>
  </select>

</mapper>