<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.SiteRoMapper">


    <resultMap type="com.cdz360.iot.model.site.po.SitePo" id="sitePo">
        <id column="id" property="id"/>
        <result column="siteId" property="siteId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="commId" property="commId"/>
        <result column="lon" property="lon"/>
        <result column="lat" property="lat"/>
        <result column="timeZone" property="timeZone"/>
        <result column="address" property="address"/>
        <result column="phone" property="phone" />
        <result column="priceCode" property="priceCode"/>
        <result column="dyPow" property="dyPow"
                typeHandler="org.apache.ibatis.type.EnumOrdinalTypeHandler"/>
        <result column="provinceCode" property="provinceCode"/>
        <result column="cityCode" property="cityCode"/>

        <result column="areaCode" property="areaCode"/>
        <result column="status" property="status"
                typeHandler="org.apache.ibatis.type.EnumOrdinalTypeHandler"/>
        <result column="createTime" property="createTime"/>
        <result column="updateTime" property="updateTime"/>
    </resultMap>

    <resultMap type="com.cdz360.iot.model.site.dto.SiteDto" id="siteDto">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="commId" property="commId"/>
        <result column="dzType" property="dzType"/>
        <result column="lon" property="lon"/>
        <result column="lat" property="lat"/>
        <result column="address" property="address"/>
        <result column="provinceCode" property="provinceCode"/>
        <result column="cityCode" property="cityCode"/>

        <result column="areaCode" property="areaCode"/>
        <result column="status" property="status"
                typeHandler="org.apache.ibatis.type.EnumOrdinalTypeHandler"/>
        <result column="createTime" property="createTime"/>
        <result column="updateTime" property="updateTime"/>
    </resultMap>

    <sql id="siteLeftJoinGw">
        select A.id,
               A.name,
               A.commId,
               A.lon,
               A.lat,
               A.address,
               A.provinceCode,
               A.cityCode,
               A.areaCode,
               A.status,
               A.createTime,
               A.updateTime,
               A.dzType as type,
               B.id as gwId,
               B.gwno,
               B.status as gwStatus

         from t_site as A left join t_gw_info as B on A.id=B.siteId
    </sql>

    <sql id="siteSelect">
        select site.id,
        site.name,
        site.commId,
        site.lon,
        site.lat,
        site.timeZone,
        site.address,
        site.phone,
        site.provinceCode,
        site.cityCode,
        site.areaCode,
        site.status,
        site.priceCode,
        site.dyPow,
        site.createTime,
        site.updateTime,
        site.dzType as type,
        site.dzId as siteId
        from t_site as site
    </sql>

    <select id="queryByCondition" resultMap="sitePo">
        <include refid="siteSelect" />
        <where>
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
                and site.dzId in
                <foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
                    #{siteId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getSite" resultMap="sitePo">
        <include refid="siteSelect" />
        where dzId=#{siteId}
    </select>

    <select id="listSite" resultMap="sitePo">
        <include refid="siteSelect" />
        where `status` in (1, 2, 3, 4)
        order by id
        limit ${start},${size}
    </select>

    <select id="listSiteUnify" resultMap="sitePo">
        <include refid="siteSelect" />
        ORDER BY createTime DESC
        limit ${start},${size}
    </select>

    <select id="listSiteUnifyCount" resultType="java.lang.Long">
        select count(*)
        from t_site as site
    </select>

    <select id="getIdleSiteIdList" resultType="java.lang.String">
        select distinct site.`dzId` from t_plug plug
        left join t_evse evse on plug.`evseId` = evse.`evseId`
        left join t_site site on evse.`siteId` = site.`dzId`
        where plug.`plugStatus` = "IDLE"
        and site.`status` = 2
        and site.topCommId = #{topCommId}
    </select>

    <select id="getSiteCount" resultType="java.lang.Integer">
        select count(1)
        from t_site s
        left join t_r_commercial c on c.id=s.commId
        <where>
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteStatusList )">
                and s.status in
                <foreach collection="siteStatusList" item="status"
                  open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( bizTypeList )">
                and s.bizType in
                <foreach collection="bizTypeList" item="bizType"
                  open="(" close=")" separator=",">
                    #{bizType}
                </foreach>
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( idChain )">
                and c.idChain like CONCAT(#{idChain}, '%')
            </if>
            <if test="commId != null">
                and c.id = #{commId}
            </if>
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gids )">
                and s.dzId in (
                select siteId from t_r_site_group_site_ref as sg
                where sg.gid in
                <foreach item="gid" index="index" collection="gids"
                  open=" (" separator="," close=")">
                    #{gid}
                </foreach>
                )
            </if>
        </where>
    </select>

    <select id="getExpireDate" resultType="java.util.Date">
        select
            max(expireDate)
        from
            t_evse
        where
            siteId = #{siteId}
    </select>

</mapper>