<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.PartsRoMapper">

  <resultMap id="RESULT_PARTS_PO" type="com.cdz360.iot.model.parts.po.PartsPo">
    <result column="code" jdbcType="VARCHAR" property="code"/>
    <result column="status" property="status"/>
    <result column="locationStatus" property="locationStatus"/>
    <result column="typeId" jdbcType="BIGINT" property="typeId"/>
    <result column="storeCode" jdbcType="VARCHAR" property="storeCode"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="createBy" jdbcType="BIGINT" property="createBy"/>
    <result column="applyTime" jdbcType="TIMESTAMP" property="applyTime"/>
    <result column="applyBy" jdbcType="BIGINT" property="applyBy"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
  </resultMap>

  <resultMap id="RESULT_PARTS_VO" extends="RESULT_PARTS_PO"
    type="com.cdz360.iot.model.parts.vo.PartsVo">
    <!-- 物料规格信息 -->
    <result column="typeCode" jdbcType="VARCHAR" property="typeCode"/>
    <result column="typeName" jdbcType="VARCHAR" property="typeName"/>
    <result column="typeFullModel" jdbcType="VARCHAR" property="typeFullModel"/>

    <!-- 物料库存信息 -->
    <result column="storageCode" jdbcType="VARCHAR" property="storageCode"/>
    <result column="storageName" jdbcType="VARCHAR" property="storageName"/>
    <result column="storageType" property="storageType"/>
    <result column="storageUid" jdbcType="BIGINT" property="storageUid"/>
  </resultMap>

  <sql id="FIND_PARTS_WHERE_SQL">
    <where>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( locationStatusList )">
        and p.locationStatus in
        <foreach collection="locationStatusList" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( statusList )">
        and p.status in
        <foreach collection="statusList" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(partsCodeLike)">
        and p.code like CONCAT('%', #{partsCodeLike}, '%')
      </if>
      <if test="null != typeId">
        and p.typeId = #{typeId}
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( typeIdList )">
        and p.typeId in
        <foreach collection="typeIdList" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(typeCodeLike)">
        and pt.code like CONCAT('%', #{typeCodeLike}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(typeNameLike)">
        and pt.name like CONCAT('%', #{typeNameLike}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(typeName)">
        and pt.name = #{typeName}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(typeModelLike)">
        and pt.fullModel = like CONCAT('%', #{typeModelLike}, '%')
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( uidList )">
        and st.uid in
        <foreach collection="uidList" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( exUidList )">
        and st.uid not in
        <foreach collection="exUidList" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( storageCodeList )">
        and st.code in
        <foreach collection="storageCodeList" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(expressNoLike)">
        and ex.expressNo like CONCAT('%', #{expressNoLike}, '%')
      </if>
    </where>
  </sql>

  <select id="getByCode" resultMap="RESULT_PARTS_PO">
    select * from t_parts where `code` = #{code}
  </select>

  <select id="findParts"
    parameterType="com.cdz360.iot.model.parts.param.ListPartsParam"
    resultMap="RESULT_PARTS_VO">
    select
    p.*,
    pt.code typeCode, pt.name typeName, pt.fullModel typeFullModel,
    st.code storageCode, st.name storageName, st.type storageType, st.uid storageUid
    from t_parts p
    left join t_parts_type pt on pt.id = p.typeId
    left join t_storage st on st.code = p.storeCode
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(expressNoLike)">
      left join t_parts_trans_ref ref on ref.partsCode = p.code
      left join t_express ex on ex.transOrderNo = ref.transOrderNo
    </if>
    <include refid="FIND_PARTS_WHERE_SQL"/>
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          ${sort.columnsString} ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by p.createTime desc, p.code desc
      </otherwise>
    </choose>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>
  <select id="countParts" resultType="java.lang.Long">
    select count(p.code)
    from t_parts p
    left join t_parts_type pt on pt.id = p.typeId
    left join t_storage st on st.code = p.storeCode
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(expressNoLike)">
      left join t_parts_trans_ref ref on ref.partsCode = p.code
      left join t_express ex on ex.transOrderNo = ref.transOrderNo
    </if>
    <include refid="FIND_PARTS_WHERE_SQL"/>
  </select>
  <select id="findPartsStatus" resultMap="RESULT_PARTS_PO">
    select p.code, p.locationStatus, p.status
    from t_parts p
    where p.code in
    <foreach collection="codeList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>


</mapper>

