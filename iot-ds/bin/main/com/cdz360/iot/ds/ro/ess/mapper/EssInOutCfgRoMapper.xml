<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssInOutCfgRoMapper">



	<resultMap id="RESULT_ESSINOUTCFG_PO" type="com.cdz360.iot.model.ess.po.EssInOutCfgPo">

		<result column="cfgId" jdbcType="BIGINT" property="cfgId" />

		<result column="coupleMode" property="coupleMode" />

		<result column="demandCtrlEnable" jdbcType="BOOLEAN" property="demandCtrlEnable" />

		<result column="demand" jdbcType="INTEGER" property="demand" />

		<result column="capacity" jdbcType="INTEGER" property="capacity" />

		<result column="inEnable" jdbcType="BOOLEAN" property="inEnable" />

		<result column="inPower" jdbcType="DECIMAL" property="inPower" />

		<result column="inTime"  typeHandler="com.cdz360.iot.ds.EssInOutStrategyItemListTypeHandler" property="inTime" />

		<result column="inStopSoc" jdbcType="INTEGER" property="inStopSoc" />

		<result column="outEnable" jdbcType="BOOLEAN" property="outEnable" />

		<result column="outStopSoc" jdbcType="INTEGER" property="outStopSoc" />

		<result column="outTime"  typeHandler="com.cdz360.iot.ds.EssInOutStrategyItemListTypeHandler" property="outTime" />

	</resultMap>
    <select id="getById" resultType="com.cdz360.iot.model.ess.po.EssInOutCfgPo" resultMap="RESULT_ESSINOUTCFG_PO">
		select * from t_ess_in_out_cfg where cfgId = #{id}
	</select>


</mapper>

