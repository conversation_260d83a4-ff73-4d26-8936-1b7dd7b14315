<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.EvseCfgRoMapper">

	<resultMap id="RESULT_EVSEMETER_PO" type="com.cdz360.iot.model.evse.cfg.CfgEvseAllV2">
		<id column="evseNo" jdbcType="VARCHAR" property="evseNo" />
		<result column="adminCodeA" jdbcType="VARCHAR" property="adminCodeA" />
		<result column="adminCodeB" jdbcType="VARCHAR" property="adminCodeB" />
		<result column="priceCode" jdbcType="INTEGER" property="priceCode" />
		<result column="vin" jdbcType="BOOLEAN" property="vin" />
		<result column="qrUrl" jdbcType="VARCHAR" property="qrUrl" />
		<result column="bmsVer" jdbcType="VARCHAR" property="bmsVer" />
		<result column="balanceMode" jdbcType="VARCHAR" property="balanceMode" />
		<result column="combination" jdbcType="BOOLEAN" property="combination" />
		<result column="heating" jdbcType="BOOLEAN" property="heating" />
		<result column="heatingVoltage" jdbcType="INTEGER" property="heatingVoltage" />
		<result column="batteryCheck" jdbcType="BOOLEAN" property="batteryCheck" />
		<result column="queryChargeRecord" jdbcType="BOOLEAN" property="queryChargeRecord" />
		<result column="securityCheck" jdbcType="BOOLEAN" property="securityCheck" />
		<result column="constantCharge" jdbcType="BOOLEAN" property="constantCharge" />
		<result column="vinDiscover" jdbcType="BOOLEAN" property="vinDiscover" />
		<result column="orderPrivacySetting" jdbcType="BOOLEAN" property="orderPrivacySetting" />
		<result column="accountDisplayType" jdbcType="BOOLEAN" property="accountDisplayType" />
		<result column="qrCharge" jdbcType="BOOLEAN" property="qrCharge" />
		<result column="cardCharge" jdbcType="BOOLEAN" property="cardCharge" />
		<result column="noCardCharge" jdbcType="BOOLEAN" property="noCardCharge" />
		<result column="timedCharge" jdbcType="BOOLEAN" property="timedCharge" />
		<result column="dayVolume" jdbcType="INTEGER" property="dayVolume" />
		<result column="nightVolume" jdbcType="INTEGER" property="nightVolume" />
		<association property="stopMode" javaType="com.cdz360.iot.model.evse.cfg.ChargeStopMode">
			<result column="amount" jdbcType="BOOLEAN" property="amount" />
			<result column="kwh" jdbcType="BOOLEAN" property="kwh" />
			<result column="time" jdbcType="BOOLEAN" property="time" />
		</association>
	</resultMap>

	<select id="queryById" resultType="com.cdz360.iot.model.evse.po.EvseCfgPo">
		select
			*
		from
			t_evse_cfg ec
		where
			ec.id = #{cfgId}
		limit 1
	</select>

	<select id="queryByEvseNo"
			resultMap="RESULT_EVSEMETER_PO">
		select
			ecr.evseNo,
			ec.adminPassword as adminCodeA,
			ec.level2Password as adminCodeB,
			ecr.actualPriceCode as priceCode,
			ec.isVinCharge as vin,
			ec.qrUrl,
			case
				when ISNULL(ec.internationalAgreement) || length(trim(ec.internationalAgreement)) = 0 then 'UNKNOWN'
				else ec.internationalAgreement
			end as bmsVer,
			case
				ec.avgOrTurnCharge when 0 then 'AVERAGE'
				when 1 then 'ROUND'
			end as balanceMode,
			ec.isCombineCharge as combination,
			ec.heating,
			ec.heatingVoltage,
			ec.batteryCheck,
			ec.isQueryChargeRecord as queryChargeRecord,
			ec.securityCheck,
			ec.constantCharge,
			ec.vinDiscover,
			ec.orderPrivacySetting,
			ec.accountDisplayType,
			ec.isScanCharge as qrCharge,
			ec.isCardCharge as cardCharge,
			ec.isNoCardCharge as noCardCharge,
			ec.isTimedCharge as timedCharge,
			ec.dayVolume,
			ec.nightVolume,
			ec.isQuotaEleCharge as kwh,
			ec.isQuotaMoneyCharge as amount,
			ec.isQuotaTimeCharge as time
		from
			t_evse_cfg_result ecr
		inner join t_evse_cfg ec on
			ecr.actualCfgCode = ec.id
		where
			ecr.enable = true
			and ecr.evseNo = #{evseNo}
		limit 1
	</select>

</mapper>
