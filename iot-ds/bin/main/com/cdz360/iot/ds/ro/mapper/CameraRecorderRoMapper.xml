<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.CameraRecorderRoMapper">

	<resultMap id="RESULT_CAMERARECORDER_PO" type="com.cdz360.iot.model.camera.po.CameraRecorderPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="cameraSiteId" jdbcType="BIGINT" property="cameraSiteId" />
		<result column="deviceSerial" jdbcType="VARCHAR" property="deviceSerial" />
		<result column="validateCode" jdbcType="VARCHAR" property="validateCode" />
		<result column="deviceName" jdbcType="VARCHAR" property="deviceName" />
		<result column="model" jdbcType="VARCHAR" property="model" />
		<result column="status" jdbcType="INTEGER" property="status" />
		<result column="password" jdbcType="VARCHAR" property="password" />
		<result column="ip" jdbcType="VARCHAR" property="ip" />
		<result column="type" jdbcType="JAVA_OBJECT" property="type" />
		<result column="enable" jdbcType="BOOLEAN" property="enable" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_CAMERARECORDER_PO">	
		select * from t_camera_recorder where id = #{id}
	</select>

	<select id="getByStoreId"
			resultMap="RESULT_CAMERARECORDER_PO">
		select * from t_camera_recorder
		<where>
			cameraSiteId = #{cameraSiteId}
			and enable = true
		</where>
		order by id
	</select>

	<select id="findByDeviceSerial"
			resultMap="RESULT_CAMERARECORDER_PO">
		select * from t_camera_recorder
		<where>
			deviceSerial = #{deviceSerial}
			and enable = #{enable}
		</where>
	</select>

	<resultMap id="RESULT_CAMERA_PO" type="com.cdz360.iot.model.camera.po.CameraPo">
		<id column="camera_id" jdbcType="BIGINT" property="id" />
		<result column="camera_cameraSiteId" jdbcType="BIGINT" property="cameraSiteId" />
		<result column="camera_deviceId" jdbcType="VARCHAR" property="deviceId" />
		<result column="camera_deviceName" jdbcType="VARCHAR" property="deviceName" />
		<result column="camera_deviceModel" jdbcType="VARCHAR" property="deviceModel" />
		<result column="camera_deviceSerial" jdbcType="VARCHAR" property="deviceSerial" />
		<result column="camera_channelId" jdbcType="VARCHAR" property="channelId" />
		<result column="camera_channelName" jdbcType="VARCHAR" property="channelName" />
		<result column="camera_channelNo" jdbcType="INTEGER" property="channelNo" />
		<result column="camera_channelStatus" jdbcType="INTEGER" property="channelStatus" />
		<result column="camera_channelPicUrl" jdbcType="VARCHAR" property="channelPicUrl" />
		<result column="camera_liveAddress" jdbcType="VARCHAR" property="liveAddress" />
		<result column="camera_liveAddressHD" jdbcType="VARCHAR" property="liveAddressHD" />
		<result column="camera_hlsLiveAddress" jdbcType="VARCHAR" property="hlsLiveAddress" />
		<result column="camera_hlsLiveAddressHD" jdbcType="VARCHAR" property="hlsLiveAddressHD" />
		<result column="camera_liveExpireTime" jdbcType="TIMESTAMP" property="liveExpireTime" />
		<result column="camera_cameraCaptureUrl" jdbcType="VARCHAR" property="cameraCaptureUrl" />
		<result column="camera_enable" jdbcType="BOOLEAN" property="enable" />
		<result column="camera_createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="camera_updateTime" jdbcType="TIMESTAMP" property="updateTime" />
		<result column="camera_recorderId" jdbcType="BIGINT" property="recorderId" />
		<result column="camera_cameraSerial" jdbcType="VARCHAR" property="cameraSerial" />
		<result column="camera_validateCode" jdbcType="VARCHAR" property="validateCode" />
		<result column="camera_password" jdbcType="VARCHAR" property="password" />
	</resultMap>

	<resultMap id="RESULT_CAMERA_VO" type="com.cdz360.iot.model.camera.vo.CameraVo" extends="RESULT_CAMERA_PO">
		<result column="camera_accountToken" jdbcType="VARCHAR" property="accountToken" />
		<result column="camera_accountExpireTime" jdbcType="TIMESTAMP" property="accountExpireTime" />
		<result column="camera_subAccessToken" jdbcType="VARCHAR" property="subAccessToken" />
		<result column="camera_accessToken" jdbcType="VARCHAR" property="accessToken" />
		<result column="site_siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="site_siteName" jdbcType="VARCHAR" property="siteName" />
		<result column="recordDeviceSerial" jdbcType="VARCHAR" property="recordDeviceSerial" />
		<result column="aspectRatio" jdbcType="VARCHAR" property="aspectRatio" />
	</resultMap>

	<resultMap id="RESULT_RECORDER_VO" type="com.cdz360.iot.model.camera.vo.CameraRecorderVo" extends="RESULT_CAMERARECORDER_PO">
		<result column="site_siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="site_siteName" jdbcType="VARCHAR" property="siteName" />
		<collection property="cameraList"
					ofType="com.cdz360.iot.model.camera.vo.CameraVo"
					javaType="list"
					resultMap="RESULT_CAMERA_VO">
		</collection>
	</resultMap>

	<select id="listRecorder"
			resultMap="RESULT_RECORDER_VO">
		select
			recorder.*,
			camera.id as camera_id,
			camera.cameraSiteId as camera_cameraSiteId,
			camera.deviceId as camera_deviceId,
			camera.deviceName as camera_deviceName,
			camera.deviceModel as camera_deviceModel,
			camera.deviceSerial as camera_deviceSerial,
			camera.channelId as camera_channelId,
			camera.channelName as camera_channelName,
			camera.channelNo as camera_channelNo,
			camera.channelStatus as camera_channelStatus,
			camera.channelPicUrl as camera_channelPicUrl,
			camera.liveAddress as camera_liveAddress,
			camera.liveAddressHD as camera_liveAddressHD,
			camera.hlsLiveAddress as camera_hlsLiveAddress,
			camera.hlsLiveAddressHD as camera_hlsLiveAddressHD,
			camera.liveExpireTime as camera_liveExpireTime,
			camera.cameraCaptureUrl as camera_cameraCaptureUrl,
			camera.enable as camera_enable,
			camera.createTime as camera_createTime,
			camera.updateTime as camera_updateTime,
			camera.recorderId as camera_recorderId,
			camera.cameraSerial as camera_cameraSerial,
			camera.validateCode as camera_validateCode,
			camera.password as camera_password,
			recorder.deviceSerial as recordDeviceSerial,
			acc.accountToken as camera_accountToken,
			acc.accountExpireTime as camera_accountExpireTime,
			site.accessToken as camera_subAccessToken,
			acc.accessToken as camera_accessToken,

			evse_site.dzId as site_siteId,
			evse_site.name as site_siteName,
			camera.aspectRatio
		from

		(
		select
			camera_recorder.*
		from
		<include refid="recorder_list_sub"></include>
		order by camera_recorder.createTime desc

		<if test="start != null and size != null">
			limit #{start}, #{size}
		</if>
		)
<!--			t_camera_recorder -->
		recorder
			left join t_camera_site recorderSite on recorder.cameraSiteId = recorderSite.id
			left join t_camera camera on recorder.id = camera.recorderId
			left join t_camera_site site on site.id = camera.cameraSiteId
			left join t_camera_account acc on acc.id = site.accountId

			left join t_site evse_site on evse_site.dzId = site.siteId
			left join t_r_commercial trc on trc.id = evse_site.commId
		<where>
			camera.enable = true
<!--			recorder.enable = true-->
<!--			and recorderSite.enable = true-->
<!--			and acc.enable = true-->
<!--			and camera.enable = true-->
<!--			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( deviceName )">-->
<!--				and recorder.deviceName like CONCAT('%', #{deviceName}, '%')-->
<!--			</if>-->
<!--			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">-->
<!--				and recorderSite.siteId = #{siteId}-->
<!--			</if>-->
<!--			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( deviceSerial )">-->
<!--				and recorder.deviceSerial = #{deviceSerial}-->
<!--			</if>-->
<!--			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( idChain )">-->
<!--				and trc.idChain like concat(#{idChain}, '%')-->
<!--			</if>-->
		</where>

	</select>

	<select id="listRecorderTotal" resultType="long">
		select
			count(0)
		from
		<include refid="recorder_list_sub"></include>
	</select>

	<sql id="recorder_list_sub">

		t_camera_recorder camera_recorder
		left join t_camera_site site on site.id = camera_recorder.cameraSiteId
		left join t_camera_account acc on acc.id = site.accountId
		left join t_site evse_site on evse_site.dzId = site.siteId
		left join t_r_commercial trc on trc.id = evse_site.commId
		<where>
			camera_recorder.enable = true
			and site.enable = true
			and acc.enable = true
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( deviceName )">
				and camera_recorder.deviceName like CONCAT('%', #{deviceName}, '%')
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
				and site.siteId = #{siteId}
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( deviceSerial )">
				and camera_recorder.deviceSerial = #{deviceSerial}
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( deviceSerialLike )">
				and camera_recorder.deviceSerial like concat('%', #{deviceSerialLike}, '%')
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteNameLike )">
				and evse_site.name like concat('%', #{siteNameLike}, '%')
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( idChain ) and !@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIds )">
				and trc.idChain like concat(#{idChain}, '%')
			</if>
			<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIds ) and !@com.cdz360.base.utils.StringUtils@isNotBlank( idChain )">
				and evse_site.dzId in
				<foreach collection="siteIds" index="index" item="item" open="(" close=")"
						 separator=",">
					#{item}
				</foreach>
			</if>
		</where>
	</sql>


	<update id="updateRecorder" parameterType="com.cdz360.iot.model.camera.vo.CameraRecorderVo">
		update t_camera_recorder
		<set>
			<if test="param.deviceName != null">
				deviceName=#{param.deviceName},
			</if>
			<if test="param.password != null">
				password=#{param.password},
			</if>
			<if test="param.ip != null">
				ip=#{param.ip},
			</if>
		</set>
		<where>
			id = #{param.id}
		</where>
	</update>

</mapper>
