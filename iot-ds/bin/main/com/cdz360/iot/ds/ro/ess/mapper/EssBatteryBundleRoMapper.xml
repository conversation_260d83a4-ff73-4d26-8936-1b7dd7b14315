<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssBatteryBundleRoMapper">

  <resultMap id="RESULT_BATTERY_BUNDLE_PO" type="com.cdz360.iot.model.ess.po.EssBatteryBundlePo">
    <result column="dno" jdbcType="VARCHAR" property="dno"/>
    <result column="essDno" jdbcType="VARCHAR" property="essDno"/>
    <result column="bmsDno" jdbcType="VARCHAR" property="bmsDno"/>
    <result column="stackDno" jdbcType="VARCHAR" property="stackDno"/>
    <result column="idx" jdbcType="INTEGER" property="idx"/>
    <result column="stackEquipId" jdbcType="BIGINT" property="stackEquipId"/>
    <result column="equipId" jdbcType="BIGINT" property="equipId"/>
    <result column="clusterNo" jdbcType="BIGINT" property="clusterNo"/>
  </resultMap>

  <select id="getBatteryBundleList"
    parameterType="com.cdz360.iot.model.ess.param.ListEssEquipParam"
    resultMap="RESULT_BATTERY_BUNDLE_PO">
    select * from t_ess_battery_bundle bundle
    where 1=1
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( essDno )">
      and bundle.essDno = #{essDno}
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( bmsDnos )">
      and bundle.bmsDno in
      <foreach collection="bmsDnos" item="bmsDno" open="(" close=")" separator=",">
        #{bmsDno}
      </foreach>
    </if>
    order by bundle.`idx`
    <if test="start != null and size != null">
      limit #{start},#{size}
    </if>
  </select>

  <select id="getByEssDnoEquipIdStackEquipId"
    resultMap="RESULT_BATTERY_BUNDLE_PO">
    select * from t_ess_battery_bundle bundle
    where bundle.essDno = #{essDno}
    and bundle.equipId = #{equipId}
    and bundle.stackEquipId = #{stackEquipId}
  </select>
  <select id="getByDno"
    resultMap="RESULT_BATTERY_BUNDLE_PO">
    select * from t_ess_battery_bundle bundle
    where bundle.dno = #{dno}
  </select>

  <select id="bmsRelevant"
    parameterType="com.cdz360.iot.model.ess.param.BmsRelevantParam"
    resultType="com.cdz360.iot.model.ess.vo.BmsRelevantVo">
    select bmsDno, stackDno, dno clusterDno
    from t_ess_battery_bundle bundle
    where (bundle.dno = #{dno} or bundle.stackDno = #{dno})
    limit 1
  </select>

</mapper>

