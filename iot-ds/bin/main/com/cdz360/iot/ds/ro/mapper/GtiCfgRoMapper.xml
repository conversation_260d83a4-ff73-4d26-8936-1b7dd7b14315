<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.mapper.GtiCfgRoMapper">



	<resultMap id="RESULT_GTICFG_PO" type="com.cdz360.iot.model.pv.po.GtiCfgPo">

		<id column="cfgId" jdbcType="BIGINT" property="cfgId" />

		<result column="samplingTime" jdbcType="INTEGER" property="samplingTime" />

		<result column="timeout" jdbcType="INTEGER" property="timeout" />

		<result column="bootVoltage" jdbcType="DECIMAL" property="bootVoltage" />

		<result column="minVoltage" jdbcType="DECIMAL" property="minVoltage" />

		<result column="maxVoltage" jdbcType="DECIMAL" property="maxVoltage" />

		<result column="minFrequency" jdbcType="DECIMAL" property="minFrequency" />

		<result column="maxFrequency" jdbcType="DECIMAL" property="maxFrequency" />

		<result column="gridMode" jdbcType="INTEGER" property="gridMode" />

	</resultMap>
	
	<resultMap id="LIST_RESULT" extends="RESULT_GTICFG_PO" type="com.cdz360.iot.model.pv.vo.GtiCfgVo">
		<collection property="inverterIdList" ofType="java.lang.Long">
			<result column="inverterId" property="value"/>
		</collection>
	</resultMap>



	<select id="getById"

			resultMap="RESULT_GTICFG_PO">
		select
			*
		from
			t_gti_cfg gc
		left join t_dev_cfg dev on
			gc.cfgId = dev.id
		where cfgId = #{id}
		limit 1
	</select>

	<select id="findList" parameterType="com.cdz360.iot.model.pv.param.ListGtiCfgParam"
			resultMap="LIST_RESULT">
		select
			temp.*,
			g.id as inverterId
		from
			(
			select
				devCfg.id as cfgId,
				devCfg.name ,
				devCfg.opUid ,
				devCfg.opName ,
				devCfg.updateTime
			from
				t_gti_cfg cfg
				left join t_dev_cfg devCfg on cfg.cfgId = devCfg.id
			where
				devCfg.enable = true
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( param.cfgName )">
				and devCfg.name = #{param.cfgName}
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( param.siteId )">
				and devCfg.siteId = #{param.siteId}
			</if>
			order by
				devCfg.createTime desc
			limit #{param.start}, #{param.size}) as temp
		left join t_gti g on
			temp.id = g.cfgSuccessId
			and g.status != #{status.code}
	</select>

	<select id="findListCount" parameterType="com.cdz360.iot.model.pv.param.ListGtiCfgParam"
			resultType="java.lang.Long">
		select
			count(*)
		from
			t_gti_cfg cfg
			left join t_dev_cfg devCfg on cfg.cfgId = devCfg.id
		where
			devCfg.enable = true
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( cfgName )">
			and devCfg.name = #{cfgName}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
			and devCfg.siteId = #{siteId}
		</if>
	</select>

	<select id="countByName" resultType="java.lang.Long">
		select
			count(*)
		from
			t_gti_cfg cfg
			left join t_dev_cfg devCfg on cfg.cfgId = devCfg.id
		where
			devCfg.name = #{name}
			and devCfg.enable = 1
		<if test="siteId != null">
			and devCfg.siteId = #{siteId}
		</if>
		<if test="id != null">
			and devCfg.id != #{id}
		</if>
	</select>

	<select id="getConnectedGtiNum" resultType="java.lang.Long">
		select
			count(*)
		from
			t_gti
		where
			status != 99
			and cfgSuccessId = #{cfgId}
	</select>

	<select id="getIssuingGtiNum" resultType="java.lang.Long">
		select
			count(*)
		from
			t_gti
		where
			status != 99
			and cfgId = #{cfgId}
	</select>



</mapper>

