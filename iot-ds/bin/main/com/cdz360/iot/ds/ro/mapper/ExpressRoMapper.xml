<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.mapper.ExpressRoMapper">



	<resultMap id="RESULT_EXPRESS_PO" type="com.cdz360.iot.model.parts.po.ExpressPo">

		<id column="id" jdbcType="BIGINT" property="id" />

		<result column="expressNo" jdbcType="VARCHAR" property="expressNo" />
		<result column="expressName" jdbcType="VARCHAR" property="expressName" />

		<result column="fromAddress" jdbcType="VARCHAR" property="fromAddress" />

		<result column="toAddress" jdbcType="VARCHAR" property="toAddress" />

		<result column="transOrderNo" jdbcType="VARCHAR" property="transOrderNo" />

	</resultMap>



	<select id="getById"

			resultMap="RESULT_EXPRESS_PO">	
		select * from t_express where id = #{id}

	</select>



</mapper>

