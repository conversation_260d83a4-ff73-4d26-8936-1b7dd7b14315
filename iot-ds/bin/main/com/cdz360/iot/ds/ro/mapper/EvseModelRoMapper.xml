<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.EvseModelRoMapper">
    <resultMap id="BASE" type="com.cdz360.iot.model.evse.po.EvseModelPo" >
        <result column="supply" property="supply" jdbcType="VARCHAR" />
        <result column="flags" property="flags"
                typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    </resultMap>

    <select id="findById" resultMap="BASE">
        select
            *
        from
            t_evse_model
        where
            id = #{id}
    </select>

    <select id="getList" parameterType="com.cdz360.iot.model.evse.param.ListEvseModelParam"
            resultMap="BASE">
        select *
        from t_evse_model
        <where>
            enable = true
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( modelIdList )">
                and id in
                <foreach collection="modelIdList" item="modelId" open="(" close=")" separator=",">
                    #{modelId}
                </foreach>
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( model )">
                and model like concat('%', #{model}, '%')
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( brand )">
                and brand like concat('%', #{brand}, '%')
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( series )">
                and series like concat('%', #{series}, '%')
            </if>
            <if test="supply != null">
                and supply = #{supply}
            </if>
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( flagList )">
                and json_contains(flags, json_array
                <foreach collection="flagList" open="(" close=")"
                         separator="," item="flag">
                    #{flag.code}
                </foreach>
                )
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        order by createTime desc
        <if test="start != null and size != null">
            limit #{start},#{size}
        </if>
    </select>

    <select id="getListCount" parameterType="com.cdz360.iot.model.evse.param.ListEvseModelParam"
            resultType="java.lang.Long">
        select
            count(*)
        from
            t_evse_model
        <where>
            enable = true
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( model )">
                and model like concat('%', #{model}, '%')
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( brand )">
                and brand like concat('%', #{brand}, '%')
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( series )">
                and series like concat('%', #{series}, '%')
            </if>
            <if test="supply != null">
                and supply = #{supply}
            </if>
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( flagList )">
                and json_contains(flags, json_array
                <foreach collection="flagList" open="(" close=")"
                         separator="," item="flag">
                    #{flag.code}
                </foreach>
                )
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        order by createTime desc
    </select>

    <select id="findByName" resultMap="BASE">
        select
            *
        from
            t_evse_model
        where
            model = #{exactModel}
        limit 1
    </select>

    <select id="countByCondition" resultType="java.lang.Long">
        select
            count(*)
        from
            t_evse_model
        <where>
            <if test="enable != null">
                and enable = #{enable}
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( exactModel )">
                and model = #{exactModel}
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( exactBrand )">
                and brand = #{exactBrand}
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( exactSeries )">
                and series = #{exactSeries}
            </if>
            <if test="exactSupply != null">
                and supply = #{exactSupply}
            </if>
            <if test="exactPower != null">
                and power = #{exactPower}
            </if>
            <if test="exactPlugNum != null">
                and plugNum = #{exactPlugNum}
            </if>
        </where>
    </select>

    <select id="findOneByCondition" resultMap="BASE">
        select
            *
        from
            t_evse_model
        where
            enable = 1
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( exactModel )">
            and model = #{exactModel}
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( exactBrand )">
            and brand = #{exactBrand}
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( exactSeries )">
            and series = #{exactSeries}
        </if>
        <if test="exactSupply != null">
            and supply = #{exactSupply}
        </if>
        <if test="exactPower != null">
            and power = #{exactPower}
        </if>
        <if test="exactPlugNum != null">
            and plugNum = #{exactPlugNum}
        </if>
        order by
            createTime desc
        limit 1
    </select>

    <select id="check" resultType="java.lang.Long">
        select
            count(e.id)
        from
            t_evse_model m
        inner join t_evse e on
            e.modelId = m.id
        where m.id = #{id}
    </select>
    <select id="getBrandList" resultType="java.lang.String">
        SELECT DISTINCT
        brand
        FROM
        t_evse_model
        WHERE
        `status` = 1
        AND `enable` =1
    </select>

    <select id="getEvseOpInfoByEvseId" resultType="com.cdz360.iot.model.gw.vo.EvseOpInfoVo">
        select
            em.plugNum,
            em.brand,
            em.supply,
            s.timeZone
        from
            t_evse e
        left join t_evse_model em on
            e.modelId = em.id
            and e.model = em.model
        left join t_site s on
            e.siteId = s.dzId
        where
            e.evseId = #{evseId}
        limit 1
    </select>

</mapper>