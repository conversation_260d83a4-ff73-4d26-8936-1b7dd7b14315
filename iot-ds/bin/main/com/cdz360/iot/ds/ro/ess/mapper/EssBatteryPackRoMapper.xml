<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssBatteryPackRoMapper">



	<resultMap id="RESULT_ESSBATTERYPACK_PO" type="com.cdz360.iot.model.ess.po.EssBatteryPackPo">

		<result column="essDno" jdbcType="VARCHAR" property="essDno" />

		<result column="stackEquipId" jdbcType="BIGINT" property="stackEquipId" />

		<result column="clusterEquipId" jdbcType="BIGINT" property="clusterEquipId" />

		<result column="lmuSn" jdbcType="BIGINT" property="lmuSn" />

		<result column="packNo" jdbcType="BIGINT" property="packNo" />

	</resultMap>

	<sql id="LIMIT_CHOOSE">
		<choose>
			<when test="start != null and size != null">
				limit #{start}, #{size}
			</when>
			<when test="size != null">
				limit #{size}
			</when>
		</choose>
	</sql>

	<sql id="find_cluster_list">
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
			and ess.siteId in
			<foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
				#{siteId}
			</foreach>
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( essDno )">
			and pack.essDno = #{essDno}
		</if>
		<if test="null != lmuSn">
			and pack.lmuSn = #{lmuSn}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( stackEquipName )">
			and stack.name = #{stackEquipName}
		</if>
		<if test="null != stackEquipId">
			and stack.equipId = #{stackEquipId}
		</if>
		<if test="null != clusterNo">
			and bc.clusterNo = #{clusterNo}
		</if>
		<if test="null != clusterEquipId">
			and pack.clusterEquipId = #{clusterEquipId}
		</if>
	</sql>

    <select id="findBatteryPack"
			parameterType="com.cdz360.iot.model.ess.param.ListEssBatteryPackParam"
			resultType="com.cdz360.iot.model.ess.vo.EssEquipBatteryPackVo">
		select pack.packNo, pack.lmuSn,
		ess.dno essDno, cluster.status, cluster.alertStatus, ess.siteId, site.name siteName,
		stack.name stackEquipName, pack.stackEquipId,
		pack.clusterEquipId, cluster.name clusterEquipName, bc.clusterNo clusterNo
		from t_ess_battery_pack pack
		left join t_ess ess on ess.dno = pack.essDno
		left join t_site site on site.dzId = ess.siteId
		left join t_ess_battery_bundle bc on bc.essDno = ess.dno and bc.equipId = pack.clusterEquipId
		left join t_ess_equip cluster on cluster.essDno = pack.essDno and cluster.equipId = pack.clusterEquipId
		left join t_ess_equip stack on stack.essDno = pack.essDno and stack.equipId = pack.stackEquipId
		where 1=1
		<include refid="find_cluster_list" />
		<choose>
			<when test="start != null and size != null">
				limit #{start},#{size}
			</when>
			<otherwise>
				limit #{size}
			</otherwise>
		</choose>
	</select>
	<select id="countBatteryPack" resultType="java.lang.Long">
		select count(*) from t_ess_battery_pack pack
		left join t_ess ess on ess.dno = pack.essDno
		left join t_site site on site.dzId = ess.siteId
		left join t_ess_battery_bundle bc on bc.essDno = ess.dno and bc.equipId = pack.clusterEquipId
		left join t_ess_equip cluster on cluster.essDno = pack.essDno and cluster.equipId = pack.clusterEquipId
		left join t_ess_equip stack on stack.essDno = pack.essDno and stack.equipId = pack.stackEquipId
		where 1=1
		<include refid="find_cluster_list" />
	</select>

	<select id="findBatteryPackSimpleVoList"
		resultType="com.cdz360.iot.model.ess.vo.EssEquipBatteryPackSimpleVo">
		select
			lmuSn as batteryPackSN,
			packNo
		from
			t_ess_battery_pack
		where
			essDno = #{essDno}
		<if test="stackEquipId != null">
			and stackEquipId = #{stackEquipId}
		</if>
		<if test="clusterEquipId != null">
			and clusterEquipId = #{clusterEquipId}
		</if>
		<include refid="LIMIT_CHOOSE"/>
	</select>

</mapper>

