<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssEquipLangRoMapper">


  <resultMap id="RESULT_ESS_EQUIP_LANG_PO" type="com.cdz360.iot.model.ess.po.EssEquipLangPo">

    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="vendor" jdbcType="VARCHAR" property="vendor"/>
    <result column="equipType" property="equipType"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="lang" jdbcType="VARCHAR" property="lang"/>
    <result column="code" jdbcType="VARCHAR" property="code"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="unit" jdbcType="VARCHAR" property="unit"/>
    <result column="vals" jdbcType="VARCHAR" property="values"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="desc" jdbcType="VARCHAR" property="desc"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="DATE" property="createTime"/>
    <result column="updateTime" jdbcType="DATE" property="updateTime"/>
  </resultMap>


  <select id="getById"
    resultMap="RESULT_ESS_EQUIP_LANG_PO">
    select * from t_ess_equip_lang where id = #{id}
  </select>

  <select id="getEquipLangList"
    resultMap="RESULT_ESS_EQUIP_LANG_PO">
    select eel.* from t_ess_equip_lang eel
    where eel.vendor = #{vendor}
    and eel.equipType = #{equipType}
    and eel.`lang` = lower(#{lang})
    and eel.`enable` = true
  </select>


</mapper>

