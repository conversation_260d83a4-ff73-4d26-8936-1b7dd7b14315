<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.mapper.ParkChannelRoMapper">



	<resultMap id="RESULT_PARKCHANNEL_PO" type="com.cdz360.iot.model.park.po.ParkChannelPo">

		<id column="id" jdbcType="BIGINT" property="id" />

		<result column="parkId" jdbcType="BIGINT" property="parkId" />

		<result column="channelId" jdbcType="BIGINT" property="channelId" />

		<result column="deviceId" jdbcType="VARCHAR" property="deviceId" />
		<result column="enable" property="enable" />

		<result column="passType" property="passType"
				typeHandler="com.cdz360.ds.type.EnumTypeHandler" />

		<result column="channelName" jdbcType="VARCHAR" property="channelName" />

		<result column="thirdRecId" jdbcType="VARCHAR" property="thirdRecId" />

		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />

		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />

	</resultMap>

	<resultMap id="RESULT_PARKCHANNEL_VO" type="com.cdz360.iot.model.park.vo.ParkChannelVo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="channelId" jdbcType="BIGINT" property="channelId" />
		<result column="deviceId" jdbcType="VARCHAR" property="deviceId" />
		<result column="passType" property="passType"
				typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="channelName" jdbcType="VARCHAR" property="channelName" />
	</resultMap>



	<select id="getById"

			resultMap="RESULT_PARKCHANNEL_PO">	
		select * from t_park_channel where id = #{id}

	</select>

	<select id="findChannel" resultMap="RESULT_PARKCHANNEL_PO">
		select * from t_park_channel
		<where>
			parkId = #{parkId} and channelId = #{channelId} and deviceId = #{deviceId}
		</where>

	</select>

	<select id="findSiteChannelList"
			resultMap="RESULT_PARKCHANNEL_VO">
		select channel.* from t_park_channel channel
		left join t_site site on site.parkId = channel.parkId
		where site.dzId = #{siteId} and channel.enable = true
	</select>


</mapper>

