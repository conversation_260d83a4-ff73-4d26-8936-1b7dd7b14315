<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.ParkMsgRoMapper">

	<resultMap id="RESULT_PARKMSG_PO" type="com.cdz360.iot.model.park.po.ParkMsgPo">
		<id column="id" jdbcType="INTEGER" property="id" />
		<result column="status" jdbcType="JAVA_OBJECT" property="status" />
		<result column="parkOrderId" jdbcType="VARCHAR" property="parkOrderId" />
		<result column="createTIme" jdbcType="TIMESTAMP" property="createTIme" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
		<result column="msg" jdbcType="VARCHAR" property="msg" />
		<result column="response" jdbcType="VARCHAR" property="response" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_PARKMSG_PO">	
		select * from t_park_msg where id = #{id}
	</select>

</mapper>
