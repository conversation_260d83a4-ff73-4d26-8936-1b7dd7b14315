<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.ParkingLockEventLogRoMapper">

  <resultMap id="RESULT_PARKINGLOCKEVENTLOG_PO"
    type="com.cdz360.iot.model.park.po.ParkingLockEventLogPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="parkingLockId" jdbcType="BIGINT" property="parkingLockId"/>
    <result column="eventType" jdbcType="INTEGER" property="eventType"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="carNo" jdbcType="VARCHAR" property="carNo"/>
    <result column="orderNo" jdbcType="VARCHAR" property="orderNo"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
  </resultMap>

  <resultMap id="RESULT_PARKINGLOCKEVENTLOG_VO" extends="RESULT_PARKINGLOCKEVENTLOG_PO"
    type="com.cdz360.iot.model.park.vo.ParkingLockEventLogVo">
  </resultMap>

  <select id="getById"
    resultMap="RESULT_PARKINGLOCKEVENTLOG_PO">
    select * from t_parking_lock_event_log where id = #{id}
  </select>
  <select id="eventLogRecent"
    resultMap="RESULT_PARKINGLOCKEVENTLOG_PO">
    select * from t_parking_lock_event_log where parkingLockId = #{parkingLockId}
    order by id desc
    limit #{limit}
  </select>
  <select id="latestStatusUpdateTime"
    resultType="com.cdz360.iot.model.park.po.ParkingLockEventLogPo">
    SELECT parkingLockId, max(id) id, max(createTime) createTime
    FROM t_parking_lock_event_log
    WHERE parkingLockId in
    <foreach collection="parkingLockIdList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and eventType in (10, 11)
    group by parkingLockId
  </select>

</mapper>

