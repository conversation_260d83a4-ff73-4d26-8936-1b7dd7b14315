<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.mapper.SrsRoMapper">



	<resultMap id="RESULT_SRS_PO" type="com.cdz360.iot.model.srs.po.SrsPo">

		<id column="id" jdbcType="BIGINT" property="id" />

		<result column="dno" jdbcType="VARCHAR" property="dno" />

		<result column="name" jdbcType="VARCHAR" property="name" />

		<result column="sid" jdbcType="INTEGER" property="sid" />

		<result column="vendor" jdbcType="VARCHAR" property="vendor" />

		<result column="gwno" jdbcType="VARCHAR" property="gwno" />

		<result column="siteId" jdbcType="VARCHAR" property="siteId" />

		<result column="deviceModel" jdbcType="VARCHAR" property="deviceModel" />

		<result column="status" property="status" typeHandler="com.cdz360.ds.type.EnumTypeHandler" />

		<result column="alertStatus" property="alertStatus" typeHandler="com.cdz360.ds.type.EnumTypeHandler" />

		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />

		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />

	</resultMap>


	<select id="getByGwno" parameterType="com.cdz360.iot.model.pv.param.ListCtrlParam"
			resultMap="RESULT_SRS_PO">
		select
			srs.*
		from
			t_srs srs
		<where>
			srs.status != 99
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( gwno )">
				and srs.gwno = #{gwno}
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( devNo )">
				and srs.id = #{devNo}
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( devName )">
				and srs.name like concat('%', #{devName}, '%')
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( tempName )">
				and srs.name = #{tempName}
			</if>
		</where>
	</select>

	<select id="getOneByGwno"
			resultMap="RESULT_SRS_PO">
		select
			*
		from
			t_srs
		where
			gwno = #{gwno}
			and status != 99
		limit 1
	</select>

	<select id="getByDno"
			resultMap="RESULT_SRS_PO">
		select * from t_srs where dno = #{dno}
	</select>

</mapper>

