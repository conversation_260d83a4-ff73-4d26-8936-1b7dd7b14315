<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.EvseBundlePcQueryMapper">
    <resultMap id="BaseResultMap" type="com.cdz360.iot.model.evse.EvseBundlePc">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bundleId" jdbcType="BIGINT" property="bundleId"/>
        <result column="pcName" jdbcType="VARCHAR" property="pcName"/>
        <result column="hwVer" jdbcType="INTEGER" property="hwVer"/>
        <result column="swVer" jdbcType="INTEGER" property="swVer"/>
        <result column="vendorCode" jdbcType="INTEGER" property="vendorCode"/>
        <result column="path" jdbcType="VARCHAR" property="path"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, bundleId, pcName, hwVer, swVer, vendorCode, path, createTime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_evse_bundle_pc
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByBundleId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_evse_bundle_pc
        where bundleId = #{bundleId,jdbcType=BIGINT}
    </select>
</mapper>