<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssDrySpotCfgRoMapper">



	<resultMap id="RESULT_ESSDRYSPOTCFG_PO" type="com.cdz360.iot.model.ess.po.EssDrySpotCfgPo">

		<result column="cfgId" jdbcType="BIGINT" property="cfgId" />

		<result column="enable" jdbcType="BOOLEAN" property="enable" />

		<result column="ctrlMode" property="ctrlMode" />

		<result column="rangeTime"  typeHandler="com.cdz360.iot.ds.ListTypeHandler" property="rangeTime" />

		<result column="socThreshold" jdbcType="INTEGER" property="socThreshold" />

		<result column="weekEnable" jdbcType="INTEGER" property="weekEnable" />

	</resultMap>
    <select id="getById" resultMap="RESULT_ESSDRYSPOTCFG_PO">
		select * from t_ess_dry_spot_cfg where cfgId = #{id}
	</select>


</mapper>

