<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssDtuEssRefRoMapper">

  <resultMap id="RESULT_ESS_DTU_ESS_REF_PO" type="com.cdz360.iot.model.dtu.po.EssDtuEssRefPo">
    <result column="serialNo" jdbcType="VARCHAR" property="serialNo"/>
    <result column="dno" jdbcType="VARCHAR" property="dno"/>
  </resultMap>

  <select id="getBySerialNoAndDno"
    resultMap="RESULT_ESS_DTU_ESS_REF_PO">
    select * from t_ess_dtu_ess_ref where serialNo = #{serialNo} and dno = #{dno}
  </select>
  <select id="getBySerialNo"
    resultMap="RESULT_ESS_DTU_ESS_REF_PO">
    select * from t_ess_dtu_ess_ref where serialNo = #{serialNo}
  </select>
</mapper>

