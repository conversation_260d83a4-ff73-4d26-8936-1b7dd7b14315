<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.mapper.ParkingLockRoMapper">


  <resultMap id="RESULT_PARKINGLOCK_PO" type="com.cdz360.iot.model.park.po.ParkingLockPo">

    <id column="id" jdbcType="BIGINT" property="id"/>

    <result column="partner" property="partner"/>

    <result column="serialNumber" jdbcType="VARCHAR" property="serialNumber"/>
    <result column="positionCode" jdbcType="VARCHAR" property="positionCode"/>
    <result column="status" property="status"/>

    <result column="devUuid" jdbcType="VARCHAR" property="devUuid"/>

    <result column="type" jdbcType="VARCHAR" property="type"/>

    <result column="electricQuantity" jdbcType="INTEGER" property="electricQuantity"/>

    <result column="parkingLotId" jdbcType="VARCHAR" property="parkingLotId"/>

    <result column="parkingLotName" jdbcType="VARCHAR" property="parkingLotName"/>

    <result column="parkingSpaceCode" jdbcType="VARCHAR" property="parkingSpaceCode"/>

    <result column="carNo" jdbcType="VARCHAR" property="carNo"/>
    <result column="evseNo" property="evseNo"/>
    <result column="plugId" property="plugId"/>

    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>

    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>

  </resultMap>


  <resultMap id="RESULT_PARKINGLOCK_VO" type="com.cdz360.iot.model.park.vo.ParkingLockVo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="partner" property="partner"/>
    <result column="serialNumber" jdbcType="VARCHAR" property="serialNumber"/>
    <result column="positionCode" jdbcType="VARCHAR" property="positionCode"/>
    <result column="status" property="status"/>
    <result column="electricQuantity" jdbcType="INTEGER" property="electricQuantity"/>
    <result column="parkingLotId" jdbcType="VARCHAR" property="parkingLotId"/>
    <result column="parkingLotName" jdbcType="VARCHAR" property="parkingLotName"/>
    <result column="parkingSpaceCode" jdbcType="VARCHAR" property="parkingSpaceCode"/>
    <result column="evseNo" property="evseNo"/>
    <result column="plugId" property="plugId"/>
    <result column="carNo" property="carNo"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>

    <!--    <result column="statusUpdateTime" jdbcType="TIMESTAMP" property="statusUpdateTime"/>-->

    <result column="siteId" property="siteId"/>
    <result column="siteName" property="siteName"/>
    <result column="evseName" property="evseName"/>
    <result column="plugName" property="plugName"/>
    <result column="orderNo" property="orderNo"/>
    <result column="plugStatus" property="plugStatus"/>
  </resultMap>

  <select id="getByUniqueKey" resultType="com.cdz360.iot.model.park.po.ParkingLockPo">
    select * from t_parking_lock where partner = #{partner} and serialNumber = #{serialNumber}
  </select>

  <select id="getByEvseNoAndPlugId"
    resultType="com.cdz360.iot.model.park.po.ParkingLockPo">
    select * from t_parking_lock where evseNo = #{evseNo} and plugId = #{plugId}
    limit 1
  </select>

  <select id="getById"
    resultMap="RESULT_PARKINGLOCK_PO">
    select * from t_parking_lock where id = #{id}
  </select>

  <select id="getVoById"
    resultMap="RESULT_PARKINGLOCK_VO">
    select
    plock.*,
    evse.name evseName, evse.siteId,
    plug.name plugName, plug.orderNo, plug.plugStatus
    from t_parking_lock plock
    left join t_evse evse on evse.evseId = plock.evseNo
    left join t_plug plug on plug.evseId = evse.evseId and plug.plugId = plock.plugId
    where plock.id = #{id}
  </select>

  <select id="parkingLockList"
    parameterType="com.cdz360.iot.model.park.param.ListParkingLockParam"
    resultMap="RESULT_PARKINGLOCK_VO">
    select
    plock.*,
    <!--    log.createTime statusUpdateTime,-->
    evse.name evseName, evse.siteId,
    plug.name plugName, plug.orderNo, plug.plugStatus
    from t_parking_lock plock
    left join t_evse evse on evse.evseId = plock.evseNo
    left join t_plug plug on plug.evseId = evse.evseId and plug.plugId = plock.plugId
    <!--    left join t_parking_lock_event_log log-->
    <!--    on log.parkingLockId = plock.id and log.eventType in (10, 11)-->
    <!--		同步过来的数据仅能在数据库中查看-->
    where plock.evseNo != ""
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( serialNumber )">
      and plock.`serialNumber` like concat('%', #{serialNumber}, '%')
    </if>

    <if test="null != status">
      and plock.`status` = #{status}
    </if>

    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( statusList )">
      AND plock.`status` IN
      <foreach collection="statusList" index="index" item="item"
        open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>

    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and evse.`siteId` = #{siteId}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( sk )">
      and (evse.evseId like concat('%', #{sk}, '%') or
      evse.name like concat('%', #{sk}, '%'))
    </if>

    <if test="null != date">
      and plock.createTime <![CDATA[ >= ]]> #{date.startTime}
      and plock.createTime <![CDATA[ <= ]]> #{date.endTime}
    </if>

    ORDER BY plock.id desc
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>

  <select id="count"
    resultType="java.lang.Long"
    parameterType="com.cdz360.iot.model.park.param.ListParkingLockParam">
    select count(*)
    from t_parking_lock plock
    left join t_evse evse on evse.evseId = plock.evseNo
    where plock.evseNo != ""
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( serialNumber )">
      and plock.`serialNumber` like concat('%', #{serialNumber}, '%')
    </if>

    <if test="null != status">
      and plock.`status` = #{status}
    </if>

    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( statusList )">
      AND plock.`status` IN
      <foreach collection="statusList" index="index" item="item"
        open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>

    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and evse.`siteId` = #{siteId}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( sk )">
      and (evse.evseId like concat('%', #{sk}, '%') or
      evse.name like concat('%', #{sk}, '%'))
    </if>

    <if test="null != date">
      and plock.createTime <![CDATA[ >= ]]> #{date.startTime}
      and plock.createTime <![CDATA[ <= ]]> #{date.endTime}
    </if>

  </select>

</mapper>

