<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.CameraSiteRoMapper">

  <resultMap id="RESULT_CAMERASITE_PO" type="com.cdz360.iot.model.camera.po.CameraSitePo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="siteId" jdbcType="VARCHAR" property="siteId"/>
    <result column="accountId" jdbcType="BIGINT" property="accountId"/>
    <result column="cameraSiteId" jdbcType="VARCHAR" property="cameraSiteId"/>
    <result column="cameraSiteName" jdbcType="VARCHAR" property="cameraSiteName"/>
    <result column="cameraSiteMeasure" jdbcType="VARCHAR" property="cameraSiteMeasure"/>
    <result column="cameraSiteDetailAddress" jdbcType="VARCHAR" property="cameraSiteDetailAddress"/>
    <result column="addressProvince" jdbcType="VARCHAR" property="addressProvince"/>
    <result column="addressCity" jdbcType="VARCHAR" property="addressCity"/>
    <result column="addressCounty" jdbcType="VARCHAR" property="addressCounty"/>
    <result column="addressDetail" jdbcType="VARCHAR" property="addressDetail"/>
    <result column="mangerName" jdbcType="VARCHAR" property="mangerName"/>
    <result column="mangerTel" jdbcType="VARCHAR" property="mangerTel"/>
    <result column="cameraSiteTel" jdbcType="VARCHAR" property="cameraSiteTel"/>
    <result column="cameraSiteNo" jdbcType="VARCHAR" property="cameraSiteNo"/>
    <result column="cameraSiteLng" jdbcType="VARCHAR" property="cameraSiteLng"/>
    <result column="cameraSiteLat" jdbcType="VARCHAR" property="cameraSiteLat"/>
    <result column="cameraSiteRemark" jdbcType="VARCHAR" property="cameraSiteRemark"/>
    <result column="areaPath" jdbcType="VARCHAR" property="areaPath"/>
    <result column="insertTime" jdbcType="VARCHAR" property="insertTime"/>
    <result column="updateTime" jdbcType="VARCHAR" property="updateTime"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="accessToken" jdbcType="VARCHAR" property="accessToken"/>
    <result column="accessTokenExpires" jdbcType="TIMESTAMP" property="accessTokenExpires"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateAt" jdbcType="TIMESTAMP" property="updateAt"/>
  </resultMap>


  <resultMap id="RESULT_CAMERASITE_DTO" extends="RESULT_CAMERASITE_PO"
    type="com.cdz360.iot.model.camera.dto.CameraSiteDto">
    <result column="siteDistrictCode" jdbcType="VARCHAR" property="siteDistrictCode"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_CAMERASITE_PO">
    select * from t_camera_site where id = #{id}
  </select>

  <select id="getBySiteId"
    resultMap="RESULT_CAMERASITE_PO">
    select * from t_camera_site where siteId = #{siteId}
  </select>

  <select id="getStoreList"
    resultMap="RESULT_CAMERASITE_PO">
    SELECT
    cs.*
    FROM
    t_camera_site cs
    LEFT JOIN t_site s ON s.dzId = cs.siteId
    left join t_r_commercial c on c.id=s.commId
    <where>
      c.idChain like concat('%', #{idChain}, '%')
      and cs.`enable`=true
    </where>
  </select>

  <select id="getStoreListHasCamera"
    resultMap="RESULT_CAMERASITE_DTO">
    select
    css.id,
    css.siteId,
    css.accountId,
    css.cameraSiteId,
    <!--			css.cameraSiteName,-->
    ss.`name` as cameraSiteName,
    ss.areaCode as siteDistrictCode,
    css.cameraSiteMeasure,
    css.cameraSiteDetailAddress,
    css.addressProvince,
    css.addressCity,
    css.addressCounty,
    css.addressDetail,
    css.mangerName,
    css.mangerTel,
    css.cameraSiteTel,
    css.cameraSiteNo,
    css.cameraSiteLng,
    css.cameraSiteLat,
    css.cameraSiteRemark,
    css.areaPath,
    css.insertTime,
    css.updateTime,
    css.enable,
    css.accessToken,
    css.accessTokenExpires,
    css.createTime,
    css.updateAt
    from
    (
    SELECT
    cs.*
    FROM
    t_camera_site cs
    LEFT JOIN t_camera cam ON cs.id = cam.cameraSiteId
    LEFT JOIN t_site s ON s.dzId = cs.siteId
    left join t_r_commercial c on c.id=s.commId
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( gids )">
      left join t_r_site_group_site_ref sg on sg.siteId = s.dzId
    </if>
    <where>
      1=1
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( idChain )">
        and c.idChain like concat('%', #{idChain}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteName )">
        and s.name like concat('%', #{siteName}, '%')
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( gids )">
        <foreach item="gid" collection="gids"
          open="and sg.gid in (" close=")" separator=",">
          #{gid}
        </foreach>
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">
        <foreach item="siteId" collection="siteIdList"
          open="and cs.siteId in (" close=")" separator=",">
          #{siteId}
        </foreach>
      </if>
      and cs.`enable`=true
      and cam.`enable`=true
    </where>
    GROUP BY
    cs.id
    HAVING
    count(cam.id) > 0
    ) css left join t_site ss on ss.dzId = css.siteId
  </select>

  <select id="getAccessTokenExpireByTime"
    resultMap="RESULT_CAMERASITE_PO">
    select site.* from t_camera_site site
    left join t_camera_account acc
    on acc.id = site.accountId
    <where>
      site.`enable` = true
      and acc.`enable` = true
      <if test="type != null">
        and acc.type = #{type}
      </if>
      and
      (
      site.accessTokenExpires <![CDATA[ <= ]]> #{expire}
      or site.accessTokenExpires is null
      )
    </where>
  </select>

</mapper>
