<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.UpgradeTaskQueryMapper">

    <select id="select" resultType="com.cdz360.iot.model.evse.upgrade.UpgradeTaskVo">
        select

        tut.id As id,
        tut.siteId As siteId,
        tut.bundleId As bundleId,
        tut.evseCount As evseCount,
        tut.opId As opId,
        tut.opName As opName,
        tut.createTime As createTime,
        teb.fileName As bundleName,
        case when (select count(1) from t_upgrade_task_detail where taskId=tut.id)=0 then NULL else concat(round(( (select count(1) from t_upgrade_task_detail where taskId=tut.id and `status`='UPDATED') / (select count(1) from t_upgrade_task_detail where taskid=tut.id) * 100 ),2),'%') end AS updateProgress,
        teb.version AS bundleVersion

        from t_upgrade_task tut left join t_evse_bundle teb on tut.bundleId=teb.id
        <where>
            <if test="siteId != null and siteId != ''">
                and tut.siteId=#{siteId}
            </if>
            <if test="taskId != null and taskId != 0">
                and tut.id=#{taskId}
            </if>
            <if test="bundleKeyword != null and bundleKeyword != ''">
                and (UPPER(CONCAT(teb.version)) like UPPER(CONCAT('%', #{bundleKeyword}, '%')) or UPPER(teb.fileName) like UPPER(CONCAT('%', #{bundleKeyword}, '%')))
            </if>
        </where>
        order by tut.id desc
        <if test="start != null and end != null">
            limit #{start}, #{end}
        </if>
    </select>
    <select id="selectCount" resultType="java.lang.Long">
        select count(1) from t_upgrade_task tut left join t_evse_bundle teb on tut.bundleId=teb.id
        <where>
            <if test="siteId != null and siteId != ''">
                and tut.siteId=#{siteId}
            </if>
            <if test="taskId != null and taskId != 0">
                and tut.id=#{taskId}
            </if>
            <if test="bundleKeyword != null and bundleKeyword != ''">
                and (UPPER(CONCAT(teb.version)) like UPPER(CONCAT('%', #{bundleKeyword}, '%')) or UPPER(teb.fileName) like UPPER(CONCAT('%', #{bundleKeyword}, '%')))
            </if>
        </where>
    </select>
    <select id="selectById" resultType="com.cdz360.iot.model.evse.upgrade.UpgradeTaskVo">
        select * from t_upgrade_task
        <where>
            id=#{id}
        </where>
    </select>
    <select id="getUpgradeTaskInfo" resultType="com.cdz360.iot.model.evse.upgrade.UpgradeTaskInfoVo">
        select

        tut.createTime AS upgradeTime,
        teb.version AS bundleVersion,
        teb.fileName AS bundleName,
        teb.id AS bundleId

        from t_upgrade_task tut left join t_evse_bundle teb
        on tut.bundleId=teb.id
        <where>
            tut.id=#{taskId}
        </where>
    </select>

    <select id="getUpgradeRecordVo" resultType="com.cdz360.iot.model.evse.upgrade.UpgradeRecordVo">
        select
            t.id,
            t.createTime,
            teb.`version` as bundleVersion,
            teb.fileName,
            td.pc01Ver,
            td.pc02Ver,
            td.pc03Ver,
            td.status,
            t.opName,
            t.siteId,
            site.name siteName
        from
            t_upgrade_task_detail td
        inner join t_upgrade_task t on
            td.taskId = t.id
        left join t_evse_bundle teb on
            t.bundleId = teb.id
        left join t_site site on
            site.dzId = t.siteId
        where
            td .evseId = #{evseNo}
        <if test="taskId != null and taskId != 0">
            and td.taskId = #{taskId}
        </if>
        <if test="bundleKeyword != null and bundleKeyword != ''">
            and (UPPER(CONCAT(teb.version)) like UPPER(CONCAT('%', #{bundleKeyword}, '%')) or UPPER(teb.fileName) like UPPER(CONCAT('%', #{bundleKeyword}, '%')))
        </if>
        order by
            t.id desc
        limit #{start}, #{size}
    </select>

    <select id="getUpgradeRecordVoCount" resultType="java.lang.Long">
        select
            count(t.id)
        from
            t_upgrade_task_detail td
        inner join t_upgrade_task t on
            td.taskId = t.id
        left join t_evse_bundle teb on
            t.bundleId = teb.id
        where
            td.evseId = #{evseNo}
        <if test="taskId != null and taskId != 0">
            and td.taskId = #{taskId}
        </if>
        <if test="bundleKeyword != null and bundleKeyword != ''">
            and (UPPER(CONCAT(teb.version)) like UPPER(CONCAT('%', #{bundleKeyword}, '%')) or UPPER(teb.fileName) like UPPER(CONCAT('%', #{bundleKeyword}, '%')))
        </if>
    </select>

</mapper>
