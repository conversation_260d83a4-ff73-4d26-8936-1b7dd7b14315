<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.EvseReportModuleRoMapper">

	<select id="getByEvseNo"
			resultType="com.cdz360.iot.model.evse.po.EvseReportModulePo">
		select *
		from t_evse_report_module
		where evseNo = #{evseNo}
		order by createTime desc
		limit 1
	</select>

	<select id="getEvseModuleVoList" resultType="com.cdz360.iot.model.evse.vo.EvseModuleVo">
		select
			d.idx,
			m.moduleType,
			d.plugId,
			d.deviceNo,
			d.intakeTemp,
			d.voltage,
			d.actualVoltage,
			d.`current`,
			d.actualCurrent,
			d.status
		from
			t_evse_report_module m
		inner join t_evse_report_module_detail d on
			m.id = d.moduleId
		where m.evseNo = #{evseNo}
		and d.enable = true
	</select>

</mapper>
