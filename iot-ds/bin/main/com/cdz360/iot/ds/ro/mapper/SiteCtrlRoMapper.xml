<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.SiteCtrlRoMapper">
    <select id="selectByNum" resultType="com.cdz360.iot.model.site.po.SiteCtrlPo">
        select * from t_site_ctrl
        where num = #{ctrlNum} and enable = 1
    </select>

    <select id="list" resultType="com.cdz360.iot.model.site.dto.SiteCtrlDto">
        select
            ctrl.id,
            ctrl.num,
            ctrl.name,
            ctrl.status,
            ctrl.passcode,
            ctrl.`loginTime`,
            ctrl.`createTime`,
            ctrl.`swVer`,
            ctrl.`protocolVer`,
            ctrl.`loadRatio`,
            ctrl.`pwrTemp`,
            cfg.`pwrCap`,
            cfg.`pwrLoadLmt`,
            cfg.`pwrTempLmt`,
            log.status as cfgStatus
        from
            t_site_ctrl ctrl
        left join t_site_ctrl_cfg cfg on
            ctrl.num = cfg.`ctrlNum`
        left join t_site_ctrl_cfg_log log on
            ctrl.num = log.`ctrlNum`
        <where>
            ctrl.enable = 1
            and ctrl.siteId = #{siteId}
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(keyword) ">
                and (ctrl.num like concat('%',#{keyword},'%')
                or ctrl.name like concat('%',#{keyword},'%'))
            </if>
        </where>
        limit #{start}, #{size}

    </select>

    <select id="listCount" resultType="java.lang.Long">
        select
            count(*)
<!--            ctrl.num,-->
<!--            ctrl.name,-->
<!--            ctrl.status,-->
<!--            ctrl.`createTime`,-->
<!--            ctrl.`swVer`,-->
<!--            ctrl.`protocolVer`,-->
<!--            ctrl.`loadRatio`,-->
<!--            ctrl.`pwrTemp`,-->
<!--            cfg.`pwrCap`,-->
<!--            cfg.`pwrLoadLmt`,-->
<!--            cfg.`pwrTempLmt`,-->
<!--            log.status as cfgStatus-->
        from
            iot.t_site_ctrl ctrl
<!--        left join iot.t_site_ctrl_cfg cfg on-->
<!--            ctrl.num = cfg.`ctrlNum`-->
<!--        left join iot.t_site_ctrl_cfg_log log on-->
<!--            ctrl.num = log.`ctrlNum`-->
        <where>
            ctrl.enable = 1
            and ctrl.siteId = #{siteId}
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(keyword) ">
                and (ctrl.num like concat('%',#{keyword},'%')
                or ctrl.name like concat('%',#{keyword},'%'))
            </if>
        </where>

    </select>
</mapper>