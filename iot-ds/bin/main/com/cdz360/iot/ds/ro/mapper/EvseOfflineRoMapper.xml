<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.EvseOfflineRoMapper">

  <resultMap id="RESULT_MAP_EVSE_PO" type="com.cdz360.iot.model.evse.EvsePo">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <id column="gwno" property="gwno" jdbcType="VARCHAR"/>
    <id column="evseId" property="evseId" jdbcType="VARCHAR"/>
    <id column="name" property="name" jdbcType="VARCHAR"/>
    <id column="power" property="power" jdbcType="INTEGER"/>
    <id column="evseStatus" property="evseStatus" jdbcType="VARCHAR"/>
    <id column="supply" property="supply" jdbcType="VARCHAR"/>
    <id column="net" property="net" jdbcType="VARCHAR"/>
    <id column="dtuType" property="dtuType" jdbcType="INTEGER"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <id column="ip" property="ip" jdbcType="VARCHAR"/>
    <id column="model" property="model" jdbcType="VARCHAR"/>
    <id column="plugNum" property="plugNum" jdbcType="INTEGER"/>
    <id column="siteId" property="siteId" jdbcType="VARCHAR"/>
    <id column="commId" property="commId" jdbcType="BIGINT"/>
    <id column="priceCode" property="priceCode" jdbcType="BIGINT"/>
    <id column="protocolVer" property="protocolVer" jdbcType="INTEGER"/>
    <id column="firmwareVer" property="firmwareVer" jdbcType="VARCHAR"/>
    <id column="pc01Ver" property="pc01Ver" jdbcType="VARCHAR"/>
    <id column="pc02Ver" property="pc02Ver" jdbcType="VARCHAR"/>
    <id column="pc03Ver" property="pc03Ver" jdbcType="VARCHAR"/>
    <id column="protocol" property="protocol" jdbcType="VARCHAR"/>
    <id column="modelName" property="modelName" jdbcType="VARCHAR"/>
    <id column="voltage" property="voltage" jdbcType="DECIMAL"/>
    <id column="current" property="current" jdbcType="DECIMAL"/>
    <id column="passcodeVer" property="passcodeVer" jdbcType="BIGINT"/>
    <id column="debugTag" property="debugTag" jdbcType="BOOLEAN"/>
    <id column="iccid" property="iccid" jdbcType="VARCHAR"/>
    <id column="imsi" property="imsi" jdbcType="VARCHAR"/>
    <id column="imei" property="imei" jdbcType="VARCHAR"/>
    <id column="produceNo" property="produceNo" jdbcType="VARCHAR"/>
    <id column="produceDate" property="produceDate" jdbcType="TIMESTAMP"/>
    <id column="expireDate" property="expireDate" jdbcType="TIMESTAMP"/>
    <id column="createTime" property="createTime" jdbcType="TIMESTAMP"/>
    <id column="updateTime" property="updateTime" jdbcType="TIMESTAMP"/>
  </resultMap>

  <resultMap id="EVSEINFODTO" type="com.cdz360.iot.model.evse.dto.EvseInfoDto">
    <id column="evseNo" property="evseNo" jdbcType="VARCHAR"/>
    <id column="gwno" property="gwno" jdbcType="VARCHAR"/>
    <id column="status" property="status" jdbcType="VARCHAR"/>
    <id column="name" property="name" jdbcType="VARCHAR"/>
    <id column="plugNum" property="plugNum" jdbcType="INTEGER"/>
    <id column="priceCode" property="priceCode" jdbcType="BIGINT"/>
    <id column="supplyType" property="supplyType" jdbcType="VARCHAR"/>
    <id column="modelName" property="modelName" jdbcType="VARCHAR"/>
    <id column="net" property="net" jdbcType="VARCHAR"/>
    <id column="dtuType" property="dtuType" jdbcType="INTEGER"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <id column="evseIp" property="evseIp" jdbcType="VARCHAR"/>
    <id column="iccid" property="iccid" jdbcType="VARCHAR"/>
    <id column="imsi" property="imsi" jdbcType="VARCHAR"/>
    <id column="imei" property="imei" jdbcType="VARCHAR"/>
    <id column="power" property="power" jdbcType="INTEGER"/>
    <id column="voltage" property="voltage" jdbcType="DECIMAL"/>
    <id column="current" property="current" jdbcType="DECIMAL"/>
    <id column="protocolVer" property="protocolVer" jdbcType="INTEGER"/>
    <id column="protocol" property="protocol" jdbcType="VARCHAR"/>
    <id column="firmwareVer" property="firmwareVer" jdbcType="VARCHAR"/>
    <id column="pc01Ver" property="pc01Ver" jdbcType="VARCHAR"/>
    <id column="pc02Ver" property="pc02Ver" jdbcType="VARCHAR"/>
    <id column="pc03Ver" property="pc03Ver" jdbcType="VARCHAR"/>
    <id column="produceNo" property="produceNo" jdbcType="VARCHAR"/>
    <id column="produceDate" property="produceDate" jdbcType="TIMESTAMP"/>
    <id column="expireDate" property="expireDate" jdbcType="TIMESTAMP"/>
    <id column="updateTime" property="updateTime" jdbcType="TIMESTAMP"/>
    <id column="siteCommId" property="siteCommId" jdbcType="BIGINT"/>
    <id column="siteId" property="siteId" jdbcType="VARCHAR"/>
    <id column="siteName" property="siteName" jdbcType="VARCHAR"/>
    <id column="gwName" property="gwName" jdbcType="VARCHAR"/>
  </resultMap>

  <resultMap id="EVSE_MODEL_VO" extends="RESULT_MAP_EVSE_PO"
    type="com.cdz360.iot.model.evse.vo.EvseModelVo">
    <!-- nothing to do -->
  </resultMap>

  <sql id="EVSE_INFO_COLUMNS">
    e.evseId as evseNo,
    e.gwno,
    e.evseStatus as status,
    e.name,
    e.plugNum,
    e.priceCode,
    e.supply as supplyType,
    e.model as modelName,
    e.net,
    e.dtuType,
    e.ip as evseIp,
    e.iccid,
    e.imsi,
    e.imei,
    e.`power`,
    e.voltage,
    e.`current`,
    e.protocolVer,
    e.protocol,
    e.firmwareVer,
    e.pc01Ver,
    e.pc02Ver,
    e.pc03Ver,
    e.produceNo,
    e.produceDate,
    e.expireDate,
    e.updateTime,
    s.commId as siteCommId,
    s.dzId as siteId,
    s.name as siteName,
    gw.name as gwName
  </sql>

  <select id="getEvseInfoList" parameterType="com.cdz360.iot.model.evse.param.ListEvseParam"
    resultMap="EVSEINFODTO">
    select
    <include refid="EVSE_INFO_COLUMNS"/>
    from t_evse_offline e
    left join t_site s on e.siteId = s.dzId
    left join t_gw_info as gw on e.gwno = gw.gwno
    <where>
      1=1
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( iccid )">
        and e.iccid like concat('%', #{iccid}, '%')
      </if>
      <if test="topCommId != null">
        and s.topCommId = #{topCommId}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
        and s.dzId=#{siteId}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteName )">
        and s.name like CONCAT('%', #{siteName}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( evseNo )">
        and e.evseId like CONCAT('%', #{evseNo}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( evseName )">
        and e.name like CONCAT('%', #{evseName}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( modelName )">
        and e.model like CONCAT('%', #{modelName}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( firmwareVer )">
        and e.firmwareVer like CONCAT('%', #{firmwareVer}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( protocol )">
        and e.protocolVer like CONCAT('%', #{protocol}, '%')
      </if>
      <if test="supplyType != null">
        and e.supply = #{supplyType}
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseStatusList )">
        <foreach item="evseStatus" collection="evseStatusList"
          open="and e.evseStatus in (" close=")" separator=",">
          #{evseStatus}
        </foreach>
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">
        and s.`dzId` in
        <foreach item="siteId" collection="siteIdList"
          open="(" close=")" separator=",">
          #{siteId}
        </foreach>
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( flags )">
        and false
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
        and e.`evseId` in
        <foreach item="evseNo" collection="evseNoList"
          open="(" close=")" separator=",">
          #{evseNo}
        </foreach>
      </if>
    </where>
    order by e.createTime desc
  </select>

  <select id="count" resultType="java.lang.Long">
    select
    count(*)
    from
    t_evse_offline
    where
    evseId = #{evseNo}
    and siteId = #{siteId}
  </select>

  <select id="getEvseRecordInfo" resultType="com.cdz360.iot.model.evse.po.EvsePlugRecordPo">
    select
    supply,
    count(*) as evseTotalNum,
    coalesce(sum(power), 0) as totalPower
    from
    t_evse_offline
    where
    `siteId` = #{siteId}
    group by
    supply
  </select>

  <select id="getEvseList" resultMap="RESULT_MAP_EVSE_PO">
    select
    e.*
    from
    t_evse_offline e
    where 1=1
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and e.siteId=#{siteId}
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and e.siteId in
      <foreach item="siteId" collection="siteIdList"
        open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
      and e.`evseId` in
      <foreach item="evseNo" collection="evseNoList"
        open="(" close=")" separator=",">
        #{evseNo}
      </foreach>
    </if>
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          ${sort.columnsString} ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by e.id desc
      </otherwise>
    </choose>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <when test="size != null">
        limit #{size}
      </when>
    </choose>
  </select>

  <select id="getEvseTinyList" parameterType="com.cdz360.iot.model.evse.param.EvseTinyParam"
    resultType="com.cdz360.iot.model.evse.dto.EvseTinyDto">
    select
    e.evseId as evseNo,
    e.name
    from
    t_evse_offline e
    where 1=1
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and e.siteId=#{siteId}
    </if>
    <!--        <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">-->
    <!--            and e.siteId in-->
    <!--            <foreach item="siteId" collection="siteIdList"-->
    <!--                     open="(" close=")" separator=",">-->
    <!--                #{siteId}-->
    <!--            </foreach>-->
    <!--        </if>-->
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
      and e.`evseId` in
      <foreach item="evseNo" collection="evseNoList"
        open="(" close=")" separator=",">
        #{evseNo}
      </foreach>
    </if>
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          ${sort.columnsString} ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by e.id desc
      </otherwise>
    </choose>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <when test="size != null">
        limit #{size}
      </when>
    </choose>
  </select>

  <select id="getEvseModelVoList" resultMap="EVSE_MODEL_VO">
    select
    e.*
    from
    t_evse_offline e
    where 1=1
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and e.siteId=#{siteId}
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and e.siteId in
      <foreach item="siteId" collection="siteIdList"
        open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
      and e.`evseId` in
      <foreach item="evseNo" collection="evseNoList"
        open="(" close=")" separator=",">
        #{evseNo}
      </foreach>
    </if>
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          ${sort.columnsString} ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by e.id desc
      </otherwise>
    </choose>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <when test="size != null">
        limit #{size}
      </when>
    </choose>
  </select>

</mapper>