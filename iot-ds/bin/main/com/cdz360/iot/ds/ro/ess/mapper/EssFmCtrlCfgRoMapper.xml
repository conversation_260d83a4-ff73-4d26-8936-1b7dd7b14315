<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssFmCtrlCfgRoMapper">



	<resultMap id="RESULT_ESSFMCTRLCFG_PO" type="com.cdz360.iot.model.ess.po.EssFmCtrlCfgPo">

		<result column="cfgId" jdbcType="BIGINT" property="cfgId" />

		<result column="fmCtrlEnable" jdbcType="BOOLEAN" property="fmCtrlEnable" />

		<result column="freqDeviation" jdbcType="INTEGER" property="freqDeviation" />

		<result column="scope" jdbcType="INTEGER" property="scope" />

		<result column="stepLength" jdbcType="INTEGER" property="stepLength" />

		<result column="inOutPower" jdbcType="DECIMAL" property="inOutPower" />

		<result column="climbingTime" jdbcType="INTEGER" property="climbingTime" />

		<result column="droop" jdbcType="INTEGER" property="droop" />

		<result column="deadZone" jdbcType="INTEGER" property="deadZone" />

		<result column="negFreqRespError" jdbcType="INTEGER" property="negFreqRespError" />

		<result column="posFreqRespError" jdbcType="INTEGER" property="posFreqRespError" />

		<result column="innerStrDeadZone" jdbcType="INTEGER" property="innerStrDeadZone" />

		<result column="accumTime" jdbcType="INTEGER" property="accumTime" />

		<result column="accumThreshold" jdbcType="INTEGER" property="accumThreshold" />

		<result column="settlingTime" jdbcType="INTEGER" property="settlingTime" />

		<result column="waitingTime" jdbcType="INTEGER" property="waitingTime" />

		<result column="powerResetSlope" jdbcType="INTEGER" property="powerResetSlope" />

	</resultMap>
    <select id="getById" resultType="com.cdz360.iot.model.ess.po.EssFmCtrlCfgPo">
		select * from t_ess_fm_ctrl_cfg where cfgId = #{id}
	</select>


</mapper>

