<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssEquipRoMapper">

  <resultMap id="RESULT_ESSEQUIP_PO" type="com.cdz360.iot.model.ess.po.EssEquipPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="dno" jdbcType="VARCHAR" property="dno"/>
    <result column="essDno" jdbcType="VARCHAR" property="essDno"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="status" jdbcType="INTEGER" property="status"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="alertStatus" jdbcType="INTEGER" property="alertStatus"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="equipId" jdbcType="BIGINT" property="equipId"/>
    <result column="equipTypeId" jdbcType="INTEGER" property="equipTypeId"/>
    <result column="equipType" jdbcType="INTEGER" property="equipType"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="nameplate" property="nameplateInfo"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="vendor" jdbcType="VARCHAR" property="vendor"/>
    <result column="netType" jdbcType="VARCHAR" property="netType"/>
    <result column="netCfg" jdbcType="VARCHAR" property="netCfg"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="modbusAddrCfg" jdbcType="VARCHAR" property="modbusAddrCfg"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="modbusTvCfg" jdbcType="VARCHAR" property="modbusTvCfg"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="logCfg" jdbcType="VARCHAR" property="logCfg"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="equipNameCn" jdbcType="VARCHAR" property="equipNameCn"/>
    <result column="equipNameEn" jdbcType="VARCHAR" property="equipNameEn"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
  </resultMap>

  <resultMap id="RESULT_EQUIP_TINY_DTO" type="com.cdz360.iot.model.ess.dto.EssEquipTinyDto">
    <result column="equipType" jdbcType="INTEGER" property="equipType"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="dno" jdbcType="VARCHAR" property="dno"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="status" jdbcType="INTEGER" property="status"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="essDno" jdbcType="VARCHAR" property="essDno"/>
  </resultMap>

  <resultMap id="RESULT_ESSEQUIP_VO" type="com.cdz360.iot.model.ess.vo.EssEquipBatteryClusterVo">
    <result column="gwno" jdbcType="VARCHAR" property="gwno"/>
    <result column="gwName" jdbcType="VARCHAR" property="gwName"/>
    <result column="essName" jdbcType="VARCHAR" property="essName"/>
    <result column="essDno" jdbcType="VARCHAR" property="essDno"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="status" jdbcType="INTEGER" property="status"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="alertStatus" jdbcType="INTEGER" property="alertStatus"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="equipId" jdbcType="BIGINT" property="equipId"/>
    <result column="equipType" jdbcType="INTEGER" property="equipType"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="nameplate" property="nameplateInfo"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>

    <result column="clusterNo" jdbcType="VARCHAR" property="clusterNo"/>
  </resultMap>


  <select id="getByDno"
    resultMap="RESULT_ESSEQUIP_PO">
    select * from t_ess_equip where dno = #{dno}
  </select>

  <select id="getById"
    resultMap="RESULT_ESSEQUIP_PO">
    select * from t_ess_equip where id = #{id}
  </select>

  <select id="findEquipList" resultMap="RESULT_ESSEQUIP_PO">
    select *
    from t_ess_equip
    <where>
      enable = true
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( essDno )">
        and essDno = #{essDno}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
        and essDno in (select dno from t_ess where siteId = #{siteId})
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( devNo )">
        and id = #{devNo}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( devName )">
        and name = #{devName}
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( equipTypeList )">
        and equipType IN
        <foreach collection="equipTypeList" index="index" item="item"
          open="(" close=")" separator=",">
          #{item.code}
        </foreach>
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( dnoList )">
        AND `dno` IN
        <foreach collection="dnoList" index="index" item="dno"
          open="(" close=")" separator=",">
          #{dno}
        </foreach>
      </if>
    </where>
  </select>

  <select id="findEquipListBySiteId" resultMap="RESULT_ESSEQUIP_PO">
    select
    tee.*
    from
    t_ess_equip tee
    left join t_ess te on tee.essDno = te.dno
    where
    te.siteId = #{siteId}
    <if test="equipType != null">
      and tee.equipType = #{equipType.code}
    </if>
  </select>
  <select id="getByEssDnoAndEquipId"
    resultMap="RESULT_ESSEQUIP_PO">
    select * from t_ess_equip where essDno = #{essDno} and equipId = #{equipId}
  </select>

  <select id="getEssEquipListByDno" resultType="com.cdz360.iot.model.ess.vo.EssEquipVo">
    select * from t_ess_equip
    where essDno in
    <foreach item="item" index="index" collection="dnoList"
      open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <sql id="find_cluster_list">
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and ess.siteId in
      <foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( essDno )">
      and equip.essDno = #{essDno}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( stackEquipName )">
      and stack.name = #{stackEquipName}
    </if>
    <if test="null != stackEquipId">
      and stack.equipId = #{stackEquipId}
    </if>
    <if test="null != clusterNo">
      and cluster.clusterNo = #{clusterNo}
    </if>
    <if test="null != clusterEquipId">
      and equip.equipId = #{clusterEquipId}
    </if>
  </sql>

  <sql id="query_essEquipList_condition">
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( essDno )">
      and essEquip.essDno = #{essDno}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and ess.siteId = #{siteId}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and trc.idChain like concat(#{commIdChain},"%")
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( essEquipName )">
      and essEquip.name = #{essEquipName}
    </if>
    <if test="null !=  batteryClusterEquipId">
      and essEquip2.equipId = #{batteryClusterEquipId}
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( statusList )">
      AND essEquip2.`status` IN
      <foreach collection="statusList" index="index" item="item"
        open="(" close=")" separator=",">
        #{item.code}
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( dnoList )">
      AND essEquip.`dno` IN
      <foreach collection="dnoList" index="index" item="dno"
        open="(" close=")" separator=",">
        #{dno}
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( equipTypes )">
      AND essEquip.`equipType` IN
      <foreach collection="equipTypes" index="index" item="item"
        open="(" close=")" separator=",">
        #{item.code}
      </foreach>
    </if>
  </sql>
  <select id="findEssEquipList" resultType="com.cdz360.iot.model.ess.vo.EssEquipVo">
    SELECT distinct
    essEquip.id,
    essEquip.essDno,
    essEquip.name,
    essEquip.status,
    essEquip.alertStatus,
    essEquip.equipId,
    essEquip.equipType,
    essEquip.equipNameCn,
    gwInfo.name as controllerName,
    site.name as siteName,
    ess.name as essName
    FROM
    t_ess_equip essEquip
    LEFT JOIN (select * from iot.t_ess_equip where equipType=3100) essEquip2 on essEquip2.essDno =
    essEquip.essDno
    LEFT JOIN t_ess ess ON ess.dno = essEquip.essDno
    LEFT JOIN t_gw_info gwInfo ON ess.gwno = gwInfo.gwno
    LEFT JOIN t_site site ON site.dzId = ess.siteId
    LEFT JOIN t_r_commercial trc ON site.commId = trc.id
    WHERE
    1 =1
    <include refid="query_essEquipList_condition"/>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( gwno )">
      and gwInfo.gwno = #{gwno}
    </if>
    order by essEquip.id DESC
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <when test="size != null">
        limit #{size}
      </when>
    </choose>
  </select>


  <select id="getEssEquipTinyList"
    parameterType="com.cdz360.iot.model.ess.param.ListEssEquipParam"
    resultMap="RESULT_EQUIP_TINY_DTO">
    select * from t_ess_equip equip
    where 1=1
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( essDno )">
      and equip.essDno = #{essDno}
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( equipTypes )">
      and equip.`equipType` IN
      <foreach collection="equipTypes" index="index" item="item"
        open="(" close=")" separator=",">
        #{item.code}
      </foreach>
    </if>
    and equip.`enable` = true
  </select>

  <select id="getEssEquipCount" resultType="java.lang.Long">
    SELECT
    count(distinct essEquip.id)
    FROM
    t_ess_equip essEquip
    LEFT JOIN (select * from iot.t_ess_equip where equipType=3100) essEquip2 on essEquip2.essDno =
    essEquip.essDno
    LEFT JOIN t_ess ess ON ess.dno = essEquip.essDno
    LEFT JOIN t_site site ON site.dzId = ess.siteId
    LEFT JOIN t_r_commercial trc ON site.commId = trc.id
    WHERE
    1 =1
    <include refid="query_essEquipList_condition"/>
  </select>

  <select id="getEssStack" resultMap="RESULT_ESSEQUIP_PO">
    select * from t_ess_equip where essDno = #{essDno} and equipType = 3000
    limit 1
  </select>

  <select id="findBatteryCluster"
    parameterType="com.cdz360.iot.model.ess.param.ListEssBatteryClusterParam"
    resultMap="RESULT_ESSEQUIP_VO">
    select equip.*, cluster.clusterNo clusterNo,
    gw.gwno, gw.name gwName,
    ess.siteId siteId, site.name siteName, ess.name essName,
    stack.name stackEquipName, cluster.stackEquipId stackEquipId
    from t_ess_equip equip
    left join t_ess ess on ess.dno = equip.essDno
    left join t_gw_info gw on gw.gwno = ess.gwno
    left join t_site site on site.dzId = ess.siteId
    left join t_ess_battery_bundle cluster on cluster.essDno = equip.essDno and cluster.equipId =
    equip.equipId
    left join t_ess_equip stack on stack.essDno = cluster.essDno and stack.equipId =
    cluster.stackEquipId
    where equip.equipType = 3100
    <include refid="find_cluster_list"/>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>

  <select id="countBatteryCluster" resultType="java.lang.Long">
    select count(equip.id)
    from t_ess_equip equip
    left join t_ess ess on ess.dno = equip.essDno
    left join t_ess_battery_bundle cluster on cluster.essDno = equip.essDno and cluster.equipId =
    equip.equipId
    left join t_ess_equip stack on stack.essDno = cluster.essDno and stack.equipId =
    cluster.stackEquipId
    where 1=1
    <include refid="find_cluster_list"/>
  </select>


  <select id="getEquipList"
    parameterType="com.cdz360.iot.model.ess.param.ListEssEquipParam"
    resultMap="RESULT_ESSEQUIP_PO">
    select * from t_ess_equip equip
    where 1=1
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( essDno )">
      and equip.essDno = #{essDno}
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( equipTypes )">
      and equip.`equipType` IN
      <foreach collection="equipTypes" index="index" item="item"
        open="(" close=")" separator=",">
        #{item.code}
      </foreach>
    </if>
    <if test="null != equipType">
      and equip.`equipType` = #{equipType}
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( dnoList )">
      AND equip.`dno` IN
      <foreach collection="dnoList" index="index" item="dno"
        open="(" close=")" separator=",">
        #{dno}
      </foreach>
    </if>
    and equip.`enable` = true
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>

  <resultMap id="RESULT_COUNT_EQUIP" type="com.cdz360.base.model.es.vo.EssDeviceBiVo">
    <result column="equipType" jdbcType="INTEGER" property="equipType"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
  </resultMap>

  <select id="equipCountByEssDno"
    resultMap="RESULT_COUNT_EQUIP">
    select equipType, count(*) `count`
    from iot.t_ess_equip tee
    where 1 = 1
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( essDnoList )">
      and essDno in
      <foreach collection="essDnoList" index="index" item="essDno"
        open="(" close=")" separator=",">
        #{essDno}
      </foreach>
    </if>
    and enable = 1
    group by equipType
  </select>
  <select id="getEquipStatusBi" resultType="com.cdz360.iot.model.ess.vo.EssStatusBi">
    select
    equip.status ,
    count(*) as num
    from t_ess_equip equip
    inner join t_ess ess on ess.dno = equip.essDno
    inner join t_site s on ess.siteId = s.dzId
    <!--    where equip.status in (1,2)-->
    <where>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
        and ess.siteId = #{siteId}
      </if>
    </where>
    group by equip.status
  </select>
  <select id="getAbnormalEquipNum" resultType="java.lang.Long">
    select
    count(*) as num
    from t_ess_equip equip
    inner join t_ess ess on ess.dno = equip.essDno
    inner join t_site s on ess.siteId = s.dzId
    where equip.status = 1 and equip.alertStatus = 2
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and ess.siteId = #{siteId}
    </if>
  </select>


  <resultMap id="RESULT_ESS_VO" type="com.cdz360.iot.model.pv.dto.UpdateCtrlDeviceDto">
    <result column="equipType" jdbcType="INTEGER" property="emuDeviceType"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="dno" jdbcType="VARCHAR" property="dno"/>
    <result column="equipId" jdbcType="INTEGER" property="equipId"/>
    <result column="essDno" jdbcType="VARCHAR" property="essDno"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="netType" jdbcType="VARCHAR" property="netType"/>
    <result column="netCfg" jdbcType="VARCHAR" property="netCfg"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="vendor" jdbcType="VARCHAR" property="emuVendor"/>
    <result column="status" jdbcType="VARCHAR" property="status"/>
  </resultMap>


  <select id="getEquipListByEssDnoList" resultMap="RESULT_ESS_VO">
    SELECT equipType, dno,essDno, `name`, netType, netCfg, vendor,equipId, status
    FROM
    t_ess_equip
    WHERE
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( essDnoList )">
      essDno in
      <foreach collection="essDnoList" index="index" item="essDno"
        open="(" close=")" separator=",">
        #{essDno}
      </foreach>
    </if>
    AND `enable` = 1
  </select>

  <select id="getMaxEquipIdByDno" resultType="int">
    SELECT
    ifnull(max( equipId ),1)
    FROM
    t_ess_equip
    WHERE
    essDno = #{essDno} limit 1;
  </select>
</mapper>

