<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.BmsRoMapper">


  <resultMap id="RESULT_BMS_PO" type="com.cdz360.iot.model.bms.po.BmsPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="dno" jdbcType="VARCHAR" property="dno"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="essDno" jdbcType="VARCHAR" property="essDno"/>
    <result column="pcsDno" jdbcType="VARCHAR" property="pcsDno"/>
    <result column="liquidDno" jdbcType="VARCHAR" property="liquidDno"/>
    <result column="vendor" jdbcType="VARCHAR" property="vendor"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getByDno"
    resultMap="RESULT_BMS_PO">
    select * from t_bms bms
    where bms.dno = #{bmsDno}
  </select>

  <select id="getBmsListByEssDno"
    resultMap="RESULT_BMS_PO">
    select * from t_bms bms
    where bms.essDno = #{essDno}
    and bms.`enable` = true
  </select>


  <select id="getBmsList"
    parameterType="com.cdz360.iot.model.ess.param.ListBmsParam"
    resultMap="RESULT_BMS_PO">
    select bms.* from t_bms bms
    left join t_ess ess on bms.essDno = ess.dno
    where bms.enable = true
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( bmsDnoList )">
      and bms.dno in
      <foreach collection="bmsDnoList" item="bmsDno" open="(" close=")" separator=",">
        #{bmsDno}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( essDnoList )">
      and ess.dno in
      <foreach collection="essDnoList" item="essDno" open="(" close=")" separator=",">
        #{essDno}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and ess.siteId in
      <foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
  </select>
</mapper>

