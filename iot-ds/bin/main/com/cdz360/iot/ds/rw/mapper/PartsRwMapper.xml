<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.PartsRwMapper">

  <resultMap id="RESULT_PARTS_PO" type="com.cdz360.iot.model.parts.po.PartsPo">
    <result column="code" jdbcType="VARCHAR" property="code"/>
    <result column="status" property="status"/>
    <result column="locationStatus" property="locationStatus"/>
    <result column="typeId" jdbcType="VARCHAR" property="typeId"/>
    <result column="storeCode" jdbcType="VARCHAR" property="storeCode"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="createBy" jdbcType="BIGINT" property="createBy"/>
    <result column="applyTime" jdbcType="TIMESTAMP" property="applyTime"/>
    <result column="applyBy" jdbcType="BIGINT" property="applyBy"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
  </resultMap>

  <select id="getByCode" resultMap="RESULT_PARTS_PO">
    select * from t_parts where `code` = #{code}
    <if test="lock">
      for update
    </if>
  </select>

  <insert id="insertParts" parameterType="com.cdz360.iot.model.parts.po.PartsPo">
    insert into t_parts (`code`,
    `status`,
    `locationStatus`,
    `typeId`,
    `storeCode`,
    `createTime`,
    `createBy`,
    <if test="null != applyBy">
      `applyBy`,
      `applyTime`,
    </if>
    `remark`)
    values (#{code},
    #{status},
    #{locationStatus},
    #{typeId},
    #{storeCode},
    now(),
    #{createBy},
    <if test="null != applyBy">
      #{applyBy},
      now(),
    </if>
    #{remark})
  </insert>

  <insert id="batchInsert">
    insert into t_parts(`code`, `status`, `locationStatus`, `typeId`, `storeCode`,
    `createTime`, `createBy`, `remark`)
    <foreach collection="poList" open="values" separator="," item="po">
      (#{po.code}, #{po.status}, #{po.locationStatus}, #{po.typeId}, #{po.storeCode},
      now(), #{po.createBy}, #{po.remark})
    </foreach>
  </insert>

  <update id="updateParts" parameterType="com.cdz360.iot.model.parts.po.PartsPo">
    update t_parts
    <set>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( storeCode )">
        storeCode = #{storeCode},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( remark )">
        remark = #{remark},
      </if>
    </set>
    where `code`=#{code}
  </update>

  <update id="updatePartsLocationStatus">
    update t_parts
    set locationStatus=#{toStatus}
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( storageCode )">
      , storeCode = #{storageCode}
    </if>
    where locationStatus=#{fromStatus} and `code`=#{code}
  </update>
  <update id="updatePartsBroken">
    update t_parts
    set locationStatus='DISCARD', `status`='DISCARD'
    where `code` in
    <foreach collection="codeList" open="(" close=")" separator="," item="code">
      #{code}
    </foreach>
  </update>
  <update id="batchUpdatePartsStatus">
    update t_parts
    set `status`=#{toStatus}
    <if test="null != typeId">
      , `typeId`=#{typeId}
    </if>
    where `code` in
    <foreach collection="codeList" open="(" close=")" separator="," item="code">
      #{code}
    </foreach>
  </update>
  <update id="updatePartsTransReview">
    update t_parts
    set
    <choose>
      <when test="agree">
        locationStatus = 'POST',
        storeCode = #{storageCode},
        remark = #{remark}
      </when>
      <otherwise>
        locationStatus = 'STORAGE',
        applyBy = 0,
        applyTime = null
      </otherwise>
    </choose>
    where `code`=#{code}
  </update>
  <update id="updatePartsTransApply">
    update t_parts
    set locationStatus = 'APPLY', applyBy = #{uid}, applyTime = now()
    where `code`=#{code}
  </update>


</mapper>

