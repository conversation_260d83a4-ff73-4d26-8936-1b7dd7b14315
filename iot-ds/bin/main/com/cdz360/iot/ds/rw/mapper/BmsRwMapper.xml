<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.BmsRwMapper">

  <insert id="batchUpsetBms">
    insert into t_bms
    (`name`,`dno`, `vendor`, `essDno`, `pcsDno`, `liquidDno`, `createTime`, `updateTime`)
    <foreach collection="poList" open="values" close=""
      separator="," item="po">
      (#{po.name},#{po.dno}, #{po.vendor}, #{po.essDno}, #{po.pcsDno}, #{po.liquidDno}, now(),
      now())
    </foreach>
    ON DUPLICATE KEY UPDATE
    `name` = values(name),
    `vendor` = values(vendor),
    `enable` = true,
    updateTime=now()
  </insert>

  <insert id="upsetBms">
    insert into t_bms
    (`name`,`dno`, `vendor`, `essDno`,
    <if test="null != pcsDno">
      `pcsDno`,
    </if>
    <if test="null != liquidDno">
      `liquidDno`,
    </if>
    `createTime`, `updateTime`)
    values
    (#{name}, #{dno}, #{vendor}, #{essDno},
    <if test="null != pcsDno">
      #{pcsDno},
    </if>
    <if test="null != liquidDno">
      #{liquidDno},
    </if>
    now(), now())
    ON DUPLICATE KEY UPDATE
    <if test="null != name">
      `name` = #{name},
    </if>
    <if test="null != vendor">
      `vendor` = #{vendor},
    </if>
    <if test="null != enable">
      `enable` = #{enable},
    </if>
    updateTime=now()
  </insert>

  <update id="updateBms" parameterType="com.cdz360.iot.model.bms.po.BmsPo">
    update t_bms set
    <if test="name != null">
      name = #{name},
    </if>
    <if test="vendor != null">
      vendor = #{vendor},
    </if>
    updateTime = now()
    where dno = #{dno}
  </update>

  <update id="offlineBms">
    update t_bms set
    enable = false
    where essDno = #{essDno}
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( remainDnoList )">
      and dno not in
      <foreach collection="remainDnoList" open="(" close=")"
        separator="," item="dno">
        #{dno}
      </foreach>
    </if>
  </update>


</mapper>

