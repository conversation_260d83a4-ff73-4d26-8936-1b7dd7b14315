<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.UpgradeLogRwMapper">

  <resultMap id="RESULT_UPGRADELOG_PO" type="com.cdz360.iot.model.gw.po.UpgradeLogPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="deviceNo" jdbcType="VARCHAR" property="deviceNo"/>
    <result column="bundleId" jdbcType="BIGINT" property="bundleId"/>
    <result column="bundleType" property="bundleType"/>
    <result column="upgradeStatus" property="upgradeStatus"/>
    <result column="progress" property="progress"/>
    <result column="downTime" jdbcType="TIMESTAMP" property="downTime"/>
    <result column="receiveTime" jdbcType="TIMESTAMP" property="receiveTime"/>
    <result column="finishTime" jdbcType="TIMESTAMP" property="finishTime"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_UPGRADELOG_PO">
    select * from t_upgrade_log where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertUpgradeLog" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.gw.po.UpgradeLogPo">
    insert into t_upgrade_log (`deviceNo`,
    `bundleId`,
    `bundleType`,
    `upgradeStatus`,
    `downTime`,
    `receiveTime`,
    `finishTime`,
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( remark )">
      `remark`,
    </if>
    `updateTime`)
    values (#{deviceNo},
    #{bundleId},
    #{bundleType},
    #{upgradeStatus},
    #{downTime},
    #{receiveTime},
    #{finishTime},
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( remark )">
      #{remark},
    </if>
    now())
  </insert>

  <update id="updateUpgradeLog" parameterType="com.cdz360.iot.model.gw.po.UpgradeLogPo">
    update t_upgrade_log set
    <if test="deviceNo != null">
      deviceNo = #{deviceNo},
    </if>
    <if test="bundleId != null">
      bundleId = #{bundleId},
    </if>
    <if test="bundleType != null">
      bundleType = #{bundleType},
    </if>
    <if test="upgradeStatus != null">
      upgradeStatus = #{upgradeStatus},
    </if>
    <if test="progress != null">
      progress = #{progress},
    </if>
    <if test="downTime != null">
      downTime = #{downTime},
    </if>
    <if test="receiveTime != null">
      receiveTime = #{receiveTime},
    </if>
    <if test="finishTime != null">
      finishTime = #{finishTime},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( remark )">
      remark = #{remark},
    </if>
    updateTime = now()
    where id = #{id}
  </update>

</mapper>

