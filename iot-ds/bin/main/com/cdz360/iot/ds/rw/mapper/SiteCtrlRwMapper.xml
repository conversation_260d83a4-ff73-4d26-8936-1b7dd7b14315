<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.SiteCtrlRwMapper">

    <insert id="insertOrUpdate" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.iot.model.site.po.SiteCtrlPo">
        insert into t_site_ctrl(
        `num`
        <if test="name != null and name != ''">
            ,`name`
        </if>
        <if test="siteId != null and siteId != ''">
            ,`siteId`
        </if>
        <if test="status != null">
            ,`status`
        </if>
        <if test="passcode != null and passcode != ''">
            ,`passcode`
        </if>
        <if test="protocolVer != null">
            ,`protocolVer`
        </if>
        <if test="swVer != null and swVer != ''">
            ,`swVer`
        </if>
        <if test="lanIp != null and lanIp != ''">
            ,`lanIp`
        </if>
        <if test="mac != null and mac != ''">
            ,`mac`
        </if>
        <if test="enable != null">
            ,`enable`
        </if>
        <if test="loadRatio != null">
            ,`loadRatio`
        </if>
        <if test="pwrTemp != null">
            ,`pwrTemp`
        </if>
        )
        values(
        #{num}
        <if test="name != null and name != ''">
            ,#{name}
        </if>
        <if test="siteId != null and siteId != ''">
            ,#{siteId}
        </if>
        <if test="status != null">
            ,#{status}
        </if>
        <if test="passcode != null and passcode != ''">
            ,#{passcode}
        </if>
        <if test="protocolVer != null">
            ,#{protocolVer}
        </if>
        <if test="swVer != null and swVer != ''">
            ,#{swVer}
        </if>
        <if test="lanIp != null and lanIp != ''">
            ,#{lanIp}
        </if>
        <if test="mac != null and mac != ''">
            ,#{mac}
        </if>
        <if test="enable != null">
            ,#{enable}
        </if>
        <if test="loadRatio != null">
            ,#{loadRatio}
        </if>
        <if test="pwrTemp != null">
            ,#{pwrTemp}
        </if>
        )
        on DUPLICATE key UPDATE
        <if test="name != null and name != ''">
            `name` = #{name},
        </if>
        <if test="siteId != null and siteId != ''">
            `siteId` = #{siteId},
        </if>
        <if test="status != null">
            `status` = #{status},
        </if>
        <if test="passcode != null and passcode != ''">
            `passcode` = #{passcode},
        </if>
        <if test="protocolVer != null">
            `protocolVer` = #{protocolVer},
        </if>
        <if test="swVer != null and swVer != ''">
            `swVer` = #{swVer},
        </if>
        <if test="lanIp != null and lanIp != ''">
            `lanIp` = #{lanIp},
        </if>
        <if test="mac != null and mac != ''">
            `mac` = #{mac},
        </if>
        <if test="enable != null">
            `enable` = #{enable},
        </if>
        <if test="loadRatio != null">
            `loadRatio` = #{loadRatio},
        </if>
        <if test="pwrTemp != null">
            `pwrTemp` = #{pwrTemp},
        </if>
        updateTime = now()
    </insert>

    <update id="updateLoginTime">
        update t_site_ctrl set loginTime=now() where num=#{ctrlNum}
    </update>

    <update id="disable">
        update t_site_ctrl set enable=0 where num = #{num}
    </update>

</mapper>