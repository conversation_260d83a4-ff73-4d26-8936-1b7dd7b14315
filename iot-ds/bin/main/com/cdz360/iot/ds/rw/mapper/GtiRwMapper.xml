<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.GtiRwMapper">

  <resultMap id="RESULT_GTI_PO" type="com.cdz360.iot.model.pv.po.GtiPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="dno" jdbcType="VARCHAR" property="dno"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="sid" jdbcType="INTEGER" property="sid"/>
    <result column="serialNo" jdbcType="VARCHAR" property="serialNo"/>
    <result column="vendor" jdbcType="VARCHAR" property="vendor"/>
    <result column="power" jdbcType="BIGINT" property="power"/>
    <result column="gwno" jdbcType="VARCHAR" property="gwno"/>
    <result column="siteId" jdbcType="VARCHAR" property="siteId"/>
    <result column="deviceModel" jdbcType="VARCHAR" property="deviceModel"/>
    <result column="status" property="status" typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="cfgId" jdbcType="BIGINT" property="cfgId"/>
    <result column="cfgStatus" property="cfgStatus"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="cfgSuccessId" jdbcType="BIGINT" property="cfgSuccessId"/>
    <result column="cfgSuccessTime" jdbcType="TIMESTAMP" property="cfgSuccessTime"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_GTI_PO">
    select * from t_gti where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>

  <select id="getBySiteIdAndGwnoAndSid"
    resultMap="RESULT_GTI_PO">
    select * from t_gti where
    status != 99 and
    siteId = #{siteId} and
    gwno = #{gwno} and
    sid = #{sid}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="batchUpsetGti">
    insert into t_gti
    (`dno`, `name`, `sid`, `vendor`, `gwno`, `siteId`, `createTime`, `updateTime`)
    <foreach collection="poList" open="values" close=""
      separator="," item="po">
      (#{po.dno}, #{po.name}, #{po.sid}, #{po.vendor}, #{po.gwno}, #{po.siteId}, now(), now())
    </foreach>
    ON DUPLICATE KEY UPDATE
    `name` = values(name),
    `vendor` = values(vendor),
    `sid` = values(sid),
    status = 0,
    updateTime=now()
  </insert>

  <insert id="upsetGti" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.pv.po.GtiPo">
    insert into t_gti
    (`dno`, `name`,
    <if test="null != sid">
      `sid`,
    </if>
    `vendor`, `gwno`,
    <if test="null != siteId">
      `siteId`,
    </if>
    <if test="null != essEquipId">
      `essEquipId`,
    </if>
    `createTime`, `updateTime`)
    values
    (#{dno}, #{name},
    <if test="null != sid">
      #{sid},
    </if>
    #{vendor}, #{gwno},
    <if test="null != siteId">
      #{siteId},
    </if>
    <if test="null != essEquipId">
      #{essEquipId},
    </if>
    now(), now())
    ON DUPLICATE KEY UPDATE
    <if test="null != name">
      `name` = #{name},
    </if>
    <if test="null != sid">
      `sid` = #{sid},
    </if>
    <if test="null != serialNo">
      `serialNo` = #{serialNo},
    </if>
    <if test="null != vendor">
      `vendor` = #{vendor},
    </if>
    <if test="null != power">
      `power` = #{power},
    </if>
    <if test="null != gwno">
      `gwno` = #{gwno},
    </if>
    <if test="null != siteId">
      `siteId` = #{siteId},
    </if>
    <if test="null != deviceModel">
      `deviceModel` = #{deviceModel},
    </if>
    <if test="null != partNo">
      `partNo` = #{partNo},
    </if>
    <if test="null != groupNum">
      `groupNum` = #{groupNum},
    </if>
    <if test="null != mpptNum">
      `mpptNum` = #{mpptNum},
    </if>
    <if test="null != status">
      `status` = #{status.code},
    </if>
    <if test="null != alertStatus">
      `alertStatus` = #{alertStatus.code},
    </if>
    <if test="null != cfgId">
      `cfgId` = #{cfgId},
    </if>
    <if test="null != cfgStatus">
      `cfgStatus` = #{cfgStatus.code},
    </if>
    <if test="null != cfgSuccessId">
      `cfgSuccessId` = #{cfgSuccessId},
    </if>
    <if test="null != cfgSuccessTime">
      `cfgSuccessTime` = #{cfgSuccessTime},
    </if>
    updateTime=now()
  </insert>

  <update id="updateGtiByDno" parameterType="com.cdz360.iot.model.pv.po.GtiPo">
    update t_gti set
    <if test="name != null">
      name = #{name},
    </if>
    <if test="sid != null">
      sid = #{sid},
    </if>
    <if test="serialNo != null">
      serialNo = #{serialNo},
    </if>
    <if test="vendor != null">
      vendor = #{vendor},
    </if>
    <if test="power != null">
      power = #{power},
    </if>
    <if test="gwno != null">
      gwno = #{gwno},
    </if>
    <if test="siteId != null">
      siteId = #{siteId},
    </if>
    <if test="deviceModel != null">
      deviceModel = #{deviceModel},
    </if>
    <if test="partNo != null">
      partNo = #{partNo},
    </if>
    <if test="groupNum != null">
      groupNum = #{groupNum},
    </if>
    <if test="mpptNum != null">
      mpptNum = #{mpptNum},
    </if>
    <if test="status != null">
      status = #{status.code},
    </if>
    <if test="alertStatus != null">
      alertStatus = #{alertStatus.code},
    </if>
    <if test="essEquipId != null">
      essEquipId = #{essEquipId},
    </if>
    <if test="cfgId != null">
      cfgId = #{cfgId},
    </if>
    <if test="cfgStatus != null">
      cfgStatus = #{cfgStatus.code},
    </if>
    <if test="cfgSuccessId != null">
      cfgSuccessId = #{cfgSuccessId},
    </if>
    <if test="cfgSuccessTime != null">
      cfgSuccessTime = #{cfgSuccessTime},
    </if>
    updateTime = now()
    where dno = #{dno}
  </update>

  <update id="offlineGti">
    update t_gti set
    status = 99
    where gwno = #{gwno}
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( remainDnoList )">
      and dno not in
      <foreach collection="remainDnoList" open="(" close=")"
        separator="," item="dno">
        #{dno}
      </foreach>
    </if>
  </update>

</mapper>

