<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.CameraRwMapper">

	<resultMap id="RESULT_CAMERA_PO" type="com.cdz360.iot.model.camera.po.CameraPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="cameraSiteId" jdbcType="BIGINT" property="cameraSiteId" />
		<result column="deviceId" jdbcType="VARCHAR" property="deviceId" />
		<result column="deviceName" jdbcType="VARCHAR" property="deviceName" />
		<result column="deviceModel" jdbcType="VARCHAR" property="deviceModel" />
		<result column="deviceSerial" jdbcType="VARCHAR" property="deviceSerial" />
		<result column="channelId" jdbcType="VARCHAR" property="channelId" />
		<result column="channelName" jdbcType="VARCHAR" property="channelName" />
		<result column="channelNo" jdbcType="INTEGER" property="channelNo" />
		<result column="channelStatus" jdbcType="INTEGER" property="channelStatus" />
		<result column="channelPicUrl" jdbcType="VARCHAR" property="channelPicUrl" />
		<result column="liveAddress" jdbcType="VARCHAR" property="liveAddress" />
		<result column="liveAddressHD" jdbcType="VARCHAR" property="liveAddressHD" />
		<result column="liveExpireTime" jdbcType="TIMESTAMP" property="liveExpireTime" />
		<result column="cameraCaptureUrl" jdbcType="VARCHAR" property="cameraCaptureUrl" />
		<result column="enable" jdbcType="BOOLEAN" property="enable" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
		<result column="recorderId" jdbcType="BIGINT" property="recorderId" />
		<result column="cameraSerial" jdbcType="VARCHAR" property="cameraSerial" />
		<result column="validateCode" jdbcType="VARCHAR" property="validateCode" />
		<result column="password" jdbcType="VARCHAR" property="password" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_CAMERA_PO">	
		select * from t_camera where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<sql id="insert-sub">
			(`cameraSiteId`,
			`deviceId`,
			`deviceName`,
			`deviceModel`,
			`deviceSerial`,
			`channelId`,
			`channelName`,
			`channelNo`,
			`channelStatus`,
			`channelPicUrl`,
			`liveAddress`,
			`liveAddressHD`,
			`liveExpireTime`,
			`cameraCaptureUrl`,
			`enable`,
			`createTime`,
			`updateTime`,
			`recorderId`,
			`cameraSerial`,
			`validateCode`,
			`password`)
		values (#{cameraSiteId},
			#{deviceId},
			#{deviceName},
			#{deviceModel},
			#{deviceSerial},
			#{channelId},
			#{channelName},
			#{channelNo},
			#{channelStatus},
			#{channelPicUrl},
			#{liveAddress},
			#{liveAddressHD},
			#{liveExpireTime},
			#{cameraCaptureUrl},
			#{enable},
			now(),
			now(),
			#{recorderId},
			#{cameraSerial},
			#{validateCode},
			#{password})
	</sql>
	<sql id="update-sub">
		<if test="cameraSiteId != null">
			cameraSiteId = #{cameraSiteId},
		</if>
		<if test="deviceId != null">
			deviceId = #{deviceId},
		</if>
		<if test="deviceName != null">
			deviceName = #{deviceName},
		</if>
		<if test="deviceModel != null">
			deviceModel = #{deviceModel},
		</if>
		<if test="deviceSerial != null">
			deviceSerial = #{deviceSerial},
		</if>
		<if test="channelId != null">
			channelId = #{channelId},
		</if>
		<if test="channelName != null">
			channelName = #{channelName},
		</if>
		<if test="channelNo != null">
			channelNo = #{channelNo},
		</if>
		<if test="channelStatus != null">
			channelStatus = #{channelStatus},
		</if>
		<if test="channelPicUrl != null">
			channelPicUrl = #{channelPicUrl},
		</if>
		<if test="liveAddress != null">
			liveAddress = #{liveAddress},
		</if>
		<if test="liveAddressHD != null">
			liveAddressHD = #{liveAddressHD},
		</if>
		<if test="liveExpireTime != null">
			liveExpireTime = #{liveExpireTime},
		</if>
		<if test="cameraCaptureUrl != null">
			cameraCaptureUrl = #{cameraCaptureUrl},
		</if>
		<if test="enable != null">
			enable = #{enable},
		</if>
		<if test="recorderId != null">
			recorderId = #{recorderId},
		</if>
		<if test="cameraSerial != null">
			cameraSerial = #{cameraSerial},
		</if>
		<if test="validateCode != null">
			validateCode = #{validateCode},
		</if>
		<if test="password != null">
			password = #{password},
		</if>
		<if test="aspectRatio != null">
			aspectRatio = #{aspectRatio},
		</if>
		updateTime = now()
	</sql>

	<insert id="insertCamera" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.iot.model.camera.po.CameraPo">
		insert into t_camera
		<include refid="insert-sub"></include>
	</insert>

	<update id="updateCamera" parameterType="com.cdz360.iot.model.camera.po.CameraPo">
		update t_camera set
		<include refid="update-sub"></include>
		<where>
			id = #{id}
		</where>
	</update>

	<update id="disableSiteByCameraSiteId">
		update t_camera set
			enable = 0,
			updateTime = now()
		where cameraSiteId = #{cameraSiteId}
		and enable = 1
	</update>

	<update id="disableSiteByDeviceSerial">
		update t_camera set
			enable = 0,
			validateCode = null,
			password = null,
			deviceName = null,
			updateTime = now()
		where deviceSerial = #{deviceSerial}
		and enable = 1
	</update>

	<update id="disableSiteByDeviceId">
		update t_camera set
			enable = 0,
			validateCode = null,
			password = null,
			deviceName = null,
			updateTime = now()
		where recorderId = #{recorderId}
		and enable = 1
	</update>

	<insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.cdz360.iot.model.camera.po.CameraPo"
			useGeneratedKeys="true">
		insert into t_camera
		<include refid="insert-sub"></include>
		on DUPLICATE key UPDATE
		<include refid="update-sub"></include>
	</insert>

	<update id="flushLiveAddress" parameterType="com.cdz360.iot.model.camera.po.CameraPo">
		update t_camera set
		liveAddress = #{liveAddress},
		liveAddressHD = #{liveAddressHD},
		hlsLiveAddress = #{hlsLiveAddress},
		hlsLiveAddressHD = #{hlsLiveAddressHD},
		liveExpireTime = #{liveExpireTime}
		<where>
			id = #{id}
		</where>
	</update>

	<update id="flushChannelPicUrl" parameterType="com.cdz360.iot.model.camera.po.CameraPo">
		update t_camera set
		cameraCaptureUrl = #{cameraCaptureUrl}
		<where>
			id = #{id}
		</where>
	</update>

	<update id="updateAspectRatioByRecordId">
		update t_camera set
			aspectRatio = #{aspectRatio}
		where recorderId = #{recorderId}
	</update>
</mapper>
