<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.MeterRwMapper">

	<resultMap id="RESULT_METER_PO" type="com.cdz360.iot.model.meter.po.MeterPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="no" jdbcType="VARCHAR" property="no" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="name" jdbcType="VARCHAR" property="name" />
		<result column="status" jdbcType="JAVA_OBJECT" property="status" />
		<result column="net" jdbcType="JAVA_OBJECT" property="net" />
		<result column="vendor" jdbcType="VARCHAR" property="vendor" />
		<result column="ctr" jdbcType="INTEGER" property="ctr" />
		<result column="vtr" jdbcType="INTEGER" property="vtr" />
		<result column="otherDevice" jdbcType="BOOLEAN" property="otherDevice" />
		<result column="comment" jdbcType="VARCHAR" property="comment" />
		<result column="lastActiveTime" jdbcType="TIMESTAMP" property="lastActiveTime" />
		<result column="gwno" jdbcType="VARCHAR" property="gwno" />
		<result column="dno" jdbcType="VARCHAR" property="dno" />
		<result column="protocol" jdbcType="VARCHAR" property="protocol" />
		<result column="sid" jdbcType="INTEGER" property="sid" />
		<result column="powerLoss" jdbcType="BOOLEAN" property="powerLoss" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_METER_PO">
		select * from t_meter where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertMeter" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.iot.model.meter.po.MeterPo">
		insert into t_meter ( dno, `no`,
			`siteId`,
			`name`,
			`status`,
			`net`,
			`vendor`,
		<if test="ctr != null">
			`ctr`,
		</if>
		<if test="vtr != null">
			`vtr`,
		</if>
			`otherDevice`,
		<if test="comment != null">
			`comment`,
		</if>
		<if test="lastActiveTime != null">
			`lastActiveTime`,
		</if>
		<if test="gwno != null">
			`gwno`,
		</if>
		<if test="sid != null">
			`sid`,
		</if>
		<if test="protocol != null">
			`protocol`,
		</if>
		<if test="powerLoss != null">
			`powerLoss`,
		</if>
			`createTime`,
			`updateTime`)
		values (#{dno}, #{no},
			#{siteId},
			#{name},
			#{status},
			#{net},
			#{vendor},
		<if test="ctr != null">
			#{ctr},
		</if>
		<if test="vtr != null">
			#{vtr},
		</if>
			#{otherDevice},
		<if test="comment != null">
			#{comment},
		</if>
		<if test="lastActiveTime != null">
			#{lastActiveTime},
		</if>
		<if test="gwno != null">
			#{gwno},
		</if>
		<if test="sid != null">
			#{sid},
		</if>
		<if test="protocol != null">
			#{protocol},
		</if>
		<if test="powerLoss != null">
			#{powerLoss},
		</if>
			now(),
			now())
	</insert>

	<update id="updateMeter" parameterType="com.cdz360.iot.model.meter.po.MeterPo">
		update t_meter set
		<if test="no != null">
			no = #{no},
		</if>
		<if test="siteId != null">
			siteId = #{siteId},
		</if>
		<if test="name != null">
			name = #{name},
		</if>
		<if test="status != null">
			status = #{status},
		</if>
		<if test="net != null">
			net = #{net},
		</if>
		<if test="vendor != null">
			vendor = #{vendor},
		</if>
		<if test="ctr != null">
			ctr = #{ctr},
		</if>
		<if test="vtr != null">
			vtr = #{vtr},
		</if>
		<if test="otherDevice != null">
			otherDevice = #{otherDevice},
		</if>
		<if test="comment != null">
			comment = #{comment},
		</if>
		<if test="lastActiveTime != null">
			lastActiveTime = #{lastActiveTime},
		</if>
		<if test="gwno != null">
			gwno = #{gwno},
		</if>
		<if test="dno != null">
			dno = #{dno},
		</if>
		<if test="sid != null">
			sid = #{sid},
		</if>
		<if test="protocol != null">
			protocol = #{protocol},
		</if>
		<if test="powerLoss != null">
			powerLoss = #{powerLoss},
		</if>
		updateTime = now()
		where id = #{id}
	</update>

    <update id="refreshMeterStatus">
        update t_meter set
            status = 'OFFLINE'
        where
            status = 'ONLINE'
            and lastActiveTime is not null
            and UNIX_TIMESTAMP(now()) - UNIX_TIMESTAMP(lastActiveTime) <![CDATA[ >= ]]> #{ttl}
    </update>


	<insert id="batchInsertMeter">
		insert into t_meter
		(dno, `no`, siteId, name, net, vendor, gwno, createTime, updateTime)
		<foreach collection="poList" open="values" close=""
			separator="," item="po">
			(#{po.dno}, #{po.no}, #{po.siteId}, #{po.name}, #{po.net}, #{po.vendor}, #{po.gwno}, now(), now())
		</foreach>
		ON DUPLICATE KEY UPDATE
		`name` = values(name),
		`vendor` = values(vendor),
		siteId = values(siteId),
		gwno = values(gwno),
		updateTime = now()
	</insert>

</mapper>
