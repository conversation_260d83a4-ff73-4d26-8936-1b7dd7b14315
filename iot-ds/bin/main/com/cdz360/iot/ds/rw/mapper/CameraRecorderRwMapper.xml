<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.CameraRecorderRwMapper">

	<resultMap id="RESULT_CAMERARECORDER_PO" type="com.cdz360.iot.model.camera.po.CameraRecorderPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="cameraSiteId" jdbcType="BIGINT" property="cameraSiteId" />
		<result column="deviceSerial" jdbcType="VARCHAR" property="deviceSerial" />
		<result column="validateCode" jdbcType="VARCHAR" property="validateCode" />
		<result column="deviceName" jdbcType="VARCHAR" property="deviceName" />
		<result column="model" jdbcType="VARCHAR" property="model" />
		<result column="status" jdbcType="INTEGER" property="status" />
		<result column="password" jdbcType="VARCHAR" property="password" />
		<result column="ip" jdbcType="VARCHAR" property="ip" />
		<result column="type" jdbcType="JAVA_OBJECT" property="type" />
		<result column="enable" jdbcType="BOOLEAN" property="enable" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_CAMERARECORDER_PO">	
		select * from t_camera_recorder where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertCameraRecorder" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.iot.model.camera.po.CameraRecorderPo">
		insert into t_camera_recorder (`cameraSiteId`,
			`deviceSerial`,
			`validateCode`,
			`deviceName`,
			`model`,
			`status`,
			`password`,
			`ip`,
			`type`,
			`enable`,
			`createTime`,
			`updateTime`)
		values (#{cameraSiteId},
			#{deviceSerial},
			#{validateCode},
			#{deviceName},
			#{model},
			#{status},
			#{password},
			#{ip},
			#{type},
			#{enable},
			now(),
			now())
	</insert>

	<update id="updateCameraRecorder" parameterType="com.cdz360.iot.model.camera.po.CameraRecorderPo">
		update t_camera_recorder set
		<if test="cameraSiteId != null">
			cameraSiteId = #{cameraSiteId},
		</if>
		<if test="deviceSerial != null">
			deviceSerial = #{deviceSerial},
		</if>
		<if test="validateCode != null">
			validateCode = #{validateCode},
		</if>
		<if test="deviceName != null">
			deviceName = #{deviceName},
		</if>
		<if test="model != null">
			model = #{model},
		</if>
		<if test="status != null">
			status = #{status},
		</if>
		<if test="password != null">
			password = #{password},
		</if>
		<if test="ip != null">
			ip = #{ip},
		</if>
		<if test="type != null">
			type = #{type},
		</if>
		<if test="enable != null">
			enable = #{enable},
		</if>
		updateTime = now()
		where id = #{id}
	</update>

	<update id="disableById">
		update t_camera_recorder set
		enable = false
		<where>
			id = #{id}
		</where>
	</update>

</mapper>
