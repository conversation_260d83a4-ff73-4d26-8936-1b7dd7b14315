<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.GwSiteRefRwMapper">

    <insert id="upset" useGeneratedKeys="true" keyProperty="id">
        insert into
        t_gw_site_ref(gwno, siteId, enable, createTime, updateTime)
        values(#{gwno}, #{siteId}, true, now(), now())
        on DUPLICATE key UPDATE
        enable = #{enable},
        updateTime = now()
    </insert>
</mapper>