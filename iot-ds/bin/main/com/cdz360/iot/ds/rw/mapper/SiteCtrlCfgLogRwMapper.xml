<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.SiteCtrlCfgLogRwMapper">
    <insert id="insertOrUpdate" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.iot.model.site.po.SiteCtrlCfgLogPo">
        insert into t_site_ctrl_cfg_log
        (
        `ctrlNum`
        <if test="status != null">
            ,status
        </if>
        <if test="triggerRst != null">
            ,triggerRst
        </if>
        <if test="pwrCtrlRst != null">
            ,pwrCtrlRst
        </if>
        <if test="infoUpRst != null">
            ,infoUpRst
        </if>
        <if test="loadAlmRst != null">
            ,loadAlmRst
        </if>
        <if test="pwrTempRst != null">
            ,pwrTempRst
        </if>
        <if test="createTime != null">
            ,createTime
        </if>
        <if test="updateTime != null">
            ,updateTime
        </if>
        ) values(
        #{ctrlNum}
        <if test="status != null">
            ,#{status}
        </if>
        <if test="triggerRst != null">
            ,#{triggerRst}
        </if>
        <if test="pwrCtrlRst != null">
            ,#{pwrCtrlRst}
        </if>
        <if test="infoUpRst != null">
            ,#{infoUpRst}
        </if>
        <if test="loadAlmRst != null">
            ,#{loadAlmRst}
        </if>
        <if test="pwrTempRst != null">
            ,#{pwrTempRst}
        </if>
        <if test="createTime != null">
            ,#{createTime}
        </if>
        <if test="updateTime != null">
            ,#{updateTime}
        </if>
        )
        on DUPLICATE key UPDATE
        <if test="status != null">
            status=#{status},
        </if>
        <if test="triggerRst != null">
            triggerRst=#{triggerRst},
        </if>
        <if test="pwrCtrlRst != null">
            pwrCtrlRst=#{pwrCtrlRst},
        </if>
        <if test="infoUpRst != null">
            infoUpRst=#{infoUpRst},
        </if>
        <if test="loadAlmRst != null">
            loadAlmRst=#{loadAlmRst},
        </if>
        <if test="pwrTempRst != null">
            pwrTempRst=#{pwrTempRst},
        </if>
        <if test="createTime != null">
            createTime=#{createTime},
        </if>
        <if test="updateTime != null">
            updateTime=#{updateTime},
        </if>
        updateTime = now()
    </insert>

    <update id="checkTimeout">
        update t_site_ctrl_cfg_log
        set status=#{status}
        <where>
            status='SENDING'
            and now()-updateTime>#{bufferTime}
        </where>

    </update>

</mapper>