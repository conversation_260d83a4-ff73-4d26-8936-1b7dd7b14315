<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvseRwQueryMapper">

  <resultMap id="RESULT_MAP_EVSE_PO" type="com.cdz360.iot.model.evse.EvsePo">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <id column="gwno" property="gwno" jdbcType="VARCHAR"/>
    <id column="evseId" property="evseId" jdbcType="VARCHAR"/>
    <id column="name" property="name" jdbcType="VARCHAR"/>
    <id column="power" property="power" jdbcType="INTEGER"/>
    <id column="bizStatus" property="bizStatus" jdbcType="INTEGER"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <id column="evseStatus" property="evseStatus" jdbcType="VARCHAR"/>
    <id column="supply" property="supply" jdbcType="VARCHAR"/>
    <id column="net" property="net" jdbcType="VARCHAR"/>
    <id column="dtuType" property="dtuType" jdbcType="INTEGER"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <id column="ip" property="ip" jdbcType="VARCHAR"/>
    <id column="iccid" property="iccid" jdbcType="VARCHAR"/>
    <id column="imsi" property="imsi" jdbcType="VARCHAR"/>
    <id column="imei" property="imei" jdbcType="VARCHAR"/>
    <id column="model" property="model" jdbcType="VARCHAR"/>
    <id column="modelId" property="modelId" jdbcType="BIGINT"/>
    <id column="plugNum" property="plugNum" jdbcType="INTEGER"/>
    <id column="siteId" property="siteId" jdbcType="VARCHAR"/>
    <id column="commId" property="commId" jdbcType="BIGINT"/>
    <id column="priceCode" property="priceCode" jdbcType="BIGINT"/>
    <id column="protocolVer" property="protocolVer" jdbcType="INTEGER"/>
    <id column="firmwareVer" property="firmwareVer" jdbcType="VARCHAR"/>
    <id column="pc01Ver" property="pc01Ver" jdbcType="VARCHAR"/>
    <id column="pc02Ver" property="pc02Ver" jdbcType="VARCHAR"/>
    <id column="pc03Ver" property="pc03Ver" jdbcType="VARCHAR"/>
    <id column="protocol" property="protocol" jdbcType="VARCHAR"/>
    <id column="modelName" property="modelName" jdbcType="VARCHAR"/>
    <id column="voltage" property="voltage" jdbcType="DECIMAL"/>
    <id column="current" property="current" jdbcType="DECIMAL"/>
    <id column="passcodeVer" property="passcodeVer" jdbcType="BIGINT"/>
    <id column="connSupport" property="connSupport" jdbcType="BOOLEAN"/>
    <id column="debugTag" property="debugTag" jdbcType="BOOLEAN"/>
    <id column="accountNumber" property="accountNumber" jdbcType="VARCHAR"/>
    <id column="physicalNo" property="physicalNo" jdbcType="VARCHAR"/>
    <id column="produceDate" property="produceDate" jdbcType="DATE" />
    <id column="openForBusinessDate" property="openForBusinessDate" jdbcType="DATE" />
    <id column="createTime" property="createTime" jdbcType="TIMESTAMP"/>
    <id column="updateTime" property="updateTime" jdbcType="TIMESTAMP"/>
    <id column="upgradeStatus" property="upgradeStatus" jdbcType="VARCHAR"/>
  </resultMap>

  <insert id="insert" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.evse.EvsePo">
    insert into
    t_evse(gwno, evseId,
    <if test="bizStatus != null">
      bizStatus,
    </if>
    evseStatus, `name`, siteId, supply, `net`,
    `dtuType`, `ip`,
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( iccid )">
      iccid,
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( imsi )">
      imsi,
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( imei )">
      imei,
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( produceNo )">
      produceNo,
    </if>
    <if test="produceDate != null">
      produceDate,
    </if>
    <if test="expireDate != null">
      expireDate,
    </if>
    `plugNum`, power,
    protocol, protocolVer, firmwareVer, pc01Ver, pc02Ver, pc03Ver, createTime, updateTime
    )
    values(#{gwno}, #{evseId},
    <if test="bizStatus != null">
      #{bizStatus.code},
    </if>
    #{evseStatus}, #{name}, #{siteId}, #{supply},
    #{net},
    #{dtuType.code}, #{ip},
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( iccid )">
      #{iccid},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( imsi )">
      #{imsi},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( imei )">
      #{imei},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( produceNo )">
      #{produceNo},
    </if>
    <if test="produceDate != null">
      #{produceDate},
    </if>
    <if test="expireDate != null">
      #{expireDate},
    </if>
    #{plugNum}, #{power},
    #{protocol}, #{protocolVer}, #{firmwareVer}, #{pc01Ver}, #{pc02Ver}, #{pc03Ver},
    now(), now()
    )
  </insert>

  <update id="update" parameterType="com.cdz360.iot.model.evse.EvsePo">
    update t_evse set
    <if test="name != null">
      `name` = #{name},
    </if>
    <if test="power != null and power > 0">
      `power` = #{power},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      siteId = #{siteId},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( physicalNo )">
      physicalNo = #{physicalNo},
    </if>
    <if test="supply != null">
      supply = #{supply},
    </if>
    <!--        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( model )">-->
    model = #{model},
    <!--        </if>-->
    <if test="modelId != null">
      modelId = #{modelId},
    </if>
    <if test="bizStatus != null">
      bizStatus = #{bizStatus.code},
    </if>
    <if test="evseStatus != null">
      evseStatus = #{evseStatus},
    </if>
    <if test="gwno != null">
      gwno = #{gwno},
    </if>
    <if test="bizType != null">
      bizType = #{bizType},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( ip )">
      ip = #{ip},
    </if>
    <if test="iccid != null">
      iccid = #{iccid},
    </if>
    <if test="imsi != null">
      imsi = #{imsi},
    </if>
    <if test="imei != null">
      imei = #{imei},
    </if>
    <if test="plugNum != null">
      plugNum = #{plugNum},
    </if>
    <if test="priceCode != null">
      priceCode = #{priceCode},
    </if>
    <if test="protocolVer != null">
      protocolVer = #{protocolVer},
    </if>
    <if test="pc01Ver != null">
      pc01Ver=#{pc01Ver},
    </if>
    <if test="pc02Ver != null">
      pc02Ver=#{pc02Ver},
    </if>
    <if test="pc03Ver != null">
      pc03Ver=#{pc03Ver},
    </if>
    <if test="connSupport != null">
      connSupport = #{connSupport},
    </if>
    <if test="firmwareVer != null">
      firmwareVer = #{firmwareVer},
    </if>
    <if test="produceNo != null">
      produceNo = #{produceNo},
    </if>
    <!--        <if test="produceDate != null">-->
    produceDate = #{produceDate},
    <!--        </if>-->
    <!--        <if test="expireDate != null">-->
    expireDate = #{expireDate},
    <!--        </if>-->
    updateTime=now()
    where evseId = #{evseId}
  </update>

  <update id="updateById" parameterType="com.cdz360.iot.model.evse.EvsePo">
    update t_evse set
    <if test="evseId != null">
      `evseId` = #{evseId},
    </if>
    <if test="name != null">
      `name` = #{name},
    </if>
    <if test="power != null and power > 0">
      `power` = #{power},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      siteId = #{siteId},
    </if>
    <if test="supply != null">
      supply = #{supply},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( model )">
      model = #{model},
    </if>
    <if test="modelId != null">
      modelId = #{modelId},
    </if>
    <if test="bizStatus != null">
      bizStatus = #{bizStatus.code},
    </if>
    <if test="evseStatus != null">
      evseStatus = #{evseStatus},
    </if>
    <if test="gwno != null">
      gwno = #{gwno},
    </if>
    <if test="bizType != null">
      bizType = #{bizType},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( ip )">
      ip = #{ip},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( iccid )">
      iccid = #{iccid},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( imsi )">
      imsi = #{imsi},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( imei )">
      imei = #{imei},
    </if>
    <if test="plugNum != null">
      plugNum = #{plugNum},
    </if>
    <if test="priceCode != null">
      priceCode = #{priceCode},
    </if>
    <if test="protocolVer != null">
      protocolVer = #{protocolVer},
    </if>
    <if test="pc01Ver != null">
      pc01Ver=#{pc01Ver},
    </if>
    <if test="pc02Ver != null">
      pc02Ver=#{pc02Ver},
    </if>
    <if test="pc03Ver != null">
      pc03Ver=#{pc03Ver},
    </if>
    <if test="connSupport != null">
      connSupport = #{connSupport},
    </if>
    <if test="firmwareVer != null">
      firmwareVer = #{firmwareVer},
    </if>
    <if test="produceNo != null">
      produceNo = #{produceNo},
    </if>
    <if test="produceDate != null">
      produceDate = #{produceDate},
    </if>
    <if test="expireDate != null">
      expireDate = #{expireDate},
    </if>
    updateTime=now()
    where id = #{id}
  </update>

  <update id="batchUpdate" parameterType="com.cdz360.iot.model.evse.EvsePo">
    <foreach collection="list" item="p" open="" close="" separator=";">
      update t_evse set
      <if test="p.name != null">
        `name` = #{p.name},
      </if>
      <if test="p.power != null">
        `power` = #{p.power},
      </if>
      <if test="p.net != null">
        `net` = #{p.net},
      </if>
      <if test="p.supply != null">
        supply = #{p.supply},
      </if>
      <if test="p.dtuType != null">
        `dtuType` = #{p.dtuType.code},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( p.siteId )">
        siteId = #{p.siteId},
      </if>
      <if test="p.bizStatus != null">
        bizStatus = #{p.bizStatus.code},
      </if>
      <if test="p.evseStatus != null">
        evseStatus = #{p.evseStatus},
      </if>
      <if test="p.gwno != null">
        gwno = #{p.gwno},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( p.ip )">
        ip = #{p.ip},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( p.iccid )">
        iccid = #{p.iccid},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( p.imsi )">
        imsi = #{p.imsi},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( p.imei )">
        imei = #{p.imei},
      </if>
      <if test="p.plugNum != null">
        plugNum = #{p.plugNum},
      </if>
      <if test="p.priceCode != null">
        priceCode = #{p.priceCode},
      </if>
      <if test="p.protocol != null">
        protocol = #{p.protocol},
      </if>
      <if test="p.protocolVer != null">
        protocolVer = #{p.protocolVer},
      </if>
      <if test="p.firmwareVer != null">
        firmwareVer = #{p.firmwareVer},
      </if>
      <if test="p.pc01Ver != null">
        pc01Ver = #{p.pc01Ver},
      </if>
      <if test="p.pc02Ver != null">
        pc02Ver = #{p.pc02Ver},
      </if>
      <if test="p.pc03Ver != null">
        pc03Ver = #{p.pc03Ver},
      </if>
      updateTime=now()
      where evseId = #{p.evseId}
    </foreach>
  </update>

  <update id="updateByEvseId" parameterType="com.cdz360.iot.model.evse.EvsePo">
    update t_evse set
    <if test="name != null">
      `name` = #{name},
    </if>
    <if test="power != null">
      `power` = #{power},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      siteId = #{siteId},
    </if>
    <if test="bizStatus != null">
      bizStatus = #{bizStatus.code},
    </if>
    <if test="gwno != null">
      gwno=#{gwno},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( ip )">
      ip = #{ip},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( iccid )">
      iccid = #{iccid},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( imsi )">
      imsi = #{imsi},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( imei )">
      imei = #{imei},
    </if>
    <if test="priceCode != null">
      priceCode = #{priceCode},
    </if>
    <if test="protocolVer != null">
      protocolVer=#{protocolVer},
    </if>
    <if test="firmwareVer != null">
      firmwareVer=#{firmwareVer},
    </if>
    <if test="connSupport != null">
      connSupport = #{connSupport},
    </if>
    updateTime=now()
    where evseId=#{evseId}
  </update>

  <update id="unBindSite" parameterType="com.cdz360.iot.model.evse.EvsePo">
    update
    t_evse
    set
    siteId = null,
    iccid = '',
    imsi = '',
    imei = '',
    evseStatus=#{evseStatus},
    updateTime=now()
    where id=#{id}
  </update>

  <update id="updateDebugTag">
    update t_evse
    set
    debugTag=#{debugTag,jdbcType=BOOLEAN},
    updateTime=now()
    where evseId=#{evseId}
  </update>

  <update id="clearSimConfig">
    update t_evse
    set iccid = null,
    imsi = null,
    imei = null
    where evseId = #{evseId}
  </update>

  <update id="clearSimByIccid">
    update t_evse
    set iccid = null,
    imsi = null,
    imei = null
    where iccid = #{iccid}
  </update>

  <delete id="removeByEvseId">
    delete
    from t_evse
    where evseId = #{evseId}
  </delete>

  <select id="getEvsePo" resultMap="RESULT_MAP_EVSE_PO">
    select * from t_evse where evseId=#{evseId}
    <if test="lock == true">
      for update
    </if>
  </select>


  <select id="getEvsePos" resultMap="RESULT_MAP_EVSE_PO">
    select * from t_evse
    where evseId in
    <foreach collection="evseIds" open="(" close=")"
      separator="," item="id">
      #{id}
    </foreach>

    <if test="lock == true">
      for update
    </if>
  </select>


  <select id="listBySiteId" resultMap="RESULT_MAP_EVSE_PO">
    select * from t_evse
    where siteId = #{siteId} and evseStatus &lt;&gt; "OFF"
  </select>

  <select id="listEvse" resultMap="RESULT_MAP_EVSE_PO">
    select * from t_evse
    where 1=1
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( statusList )">
      and evseStatus in
      <foreach collection="statusList" open="(" close=")"
        separator="," item="status">
        #{status}
      </foreach>
    </if>
    order by id desc
    limit #{start},#{size}
  </select>

  <select id="getEvseList" parameterType="com.cdz360.iot.model.evse.ListEvseParam"
    resultMap="RESULT_MAP_EVSE_PO">
    select * from t_evse
    <where>
      1=1
      <if test="siteIdList != null and siteIdList.size() > 0">
        and siteId in
        <foreach collection="siteIdList" open="(" close=")"
          separator="," item="item" index="index">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( bizStatusList )">
        <foreach item="bizStatus" collection="bizStatusList"
          open="and bizStatus in (" close=")" separator=",">
          #{bizStatus.code}
        </foreach>
      </if>
    </where>
    <if test="start != null and size != null">
      limit #{start},#{size}
    </if>
  </select>

  <select id="getOnlineAndNotBoundEvseList"
    parameterType="com.cdz360.base.model.base.param.BaseListParam"
    resultMap="RESULT_MAP_EVSE_PO">
    SELECT * FROM t_evse
    WHERE evseStatus IN ('IDLE', 'CONNECT') and (siteId is NULL OR siteId = '')
    ORDER BY id ASC
    LIMIT #{start}, #{size}
  </select>

  <select id="getOnlineAndNotBoundEvseListCount" resultType="java.lang.Long">
    SELECT count(*) FROM t_evse
    WHERE evseStatus IN ('IDLE', 'CONNECT') and (siteId is NULL OR siteId = '')
  </select>

  <select id="listBySiteIdForUpgrade" parameterType="com.cdz360.iot.model.evse.ListEvseParam"
    resultMap="RESULT_MAP_EVSE_PO">
    select * from t_evse
    <where>
      evseStatus &lt;&gt; "OFF"
      <if test="siteId != null">
        and siteId = #{siteId}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(keywords)">
        and ( name like CONCAT('%',#{keywords},'%') or evseId = #{keywords})
      </if>
      <if test="supplyType != null">
        and supply=#{supplyType}
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(evseStatusList)">
        and evseStatus in
        <foreach collection="evseStatusList" open="(" close=")"
          separator="," item="item" index="index">
          #{item}
        </foreach>
      </if>
    </where>
    order by debugTag desc
  </select>

  <select id="getUpgradeStatus" resultMap="RESULT_MAP_EVSE_PO">
    select
    tutd.evseId,
    tutd.status as upgradeStatus
    from
    iot.t_upgrade_task_detail tutd
    inner join (
    select
    evseId,
    max(taskId) as taskId
    from
    iot.t_upgrade_task_detail
    where
    evseId in
    <foreach collection="evseIdList" open="(" separator="," close=")" item="evseId" index="index">
      #{evseId}
    </foreach>
    group by
    evseId) tmp on
    tmp.taskId = tutd .taskId
    and tmp.evseId = tutd.evseId
  </select>
</mapper>