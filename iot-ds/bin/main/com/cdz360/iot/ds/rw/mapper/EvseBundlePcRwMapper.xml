<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvseBundlePcRwMapper">
    <delete id="deleteByBundleId" parameterType="java.lang.Long">
        delete from t_evse_bundle_pc
        where bundleId = #{bundleId,jdbcType=BIGINT}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cdz360.iot.model.evse.EvseBundlePc"
            useGeneratedKeys="true">
        insert into t_evse_bundle_pc (bundleId, pcName, hwVer, swVer, vendorCode, `path`, createTime)
        values (#{bundleId,jdbcType=BIGINT}, #{pcName,jdbcType=VARCHAR}, #{hwVer,jdbcType=INTEGER},
        #{swVer,jdbcType=INTEGER}, #{vendorCode,jdbcType=INTEGER}, #{path,jdbcType=VARCHAR}, now())
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cdz360.iot.model.evse.EvseBundlePc"
            useGeneratedKeys="true">
        insert into t_evse_bundle_pc
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bundleId != null">
                bundleId,
            </if>
            <if test="pcName != null">
                pcName,
            </if>
            <if test="hwVer != null">
                hwVer,
            </if>
            <if test="swVer != null">
                swVer,
            </if>
            <if test="vendorCode != null">
                vendorCode,
            </if>
            <if test="path != null">
                `path`,
            </if>
            createTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bundleId != null">
                #{bundleId,jdbcType=BIGINT},
            </if>
            <if test="pcName != null">
                #{pcName,jdbcType=VARCHAR},
            </if>
            <if test="hwVer != null">
                #{hwVer,jdbcType=INTEGER},
            </if>
            <if test="swVer != null">
                #{swVer,jdbcType=INTEGER},
            </if>
            <if test="vendorCode != null">
                #{vendorCode,jdbcType=INTEGER},
            </if>
            <if test="path != null">
                #{path,jdbcType=VARCHAR},
            </if>
            now()
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.cdz360.iot.model.evse.EvseBundlePc">
        update t_evse_bundle_pc
        <set>
            <if test="bundleId != null">
                bundleId = #{bundleId,jdbcType=BIGINT},
            </if>
            <if test="pcName != null">
                pcName = #{pcName,jdbcType=VARCHAR},
            </if>
            <if test="hwVer != null">
                hwVer = #{hwVer,jdbcType=INTEGER},
            </if>
            <if test="swVer != null">
                swVer = #{swVer,jdbcType=INTEGER},
            </if>
            <if test="vendorCode != null">
                vendorCode = #{vendorCode,jdbcType=INTEGER},
            </if>
            <if test="path != null">
                `path` = #{path,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.cdz360.iot.model.evse.EvseBundlePc">
        update t_evse_bundle_pc
        set bundleId = #{bundleId,jdbcType=BIGINT},
        pcName = #{pcName,jdbcType=VARCHAR},
        hwVer = #{hwVer,jdbcType=INTEGER},
        swVer = #{swVer,jdbcType=INTEGER},
        vendorCode = #{vendorCode,jdbcType=INTEGER},
        `path` = #{path,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into t_evse_bundle_pc (bundleId, pcName, hwVer, swVer, vendorCode, `path`, createTime)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.bundleId,jdbcType=BIGINT}, #{item.pcName,jdbcType=VARCHAR}, #{item.hwVer,jdbcType=INTEGER},
            #{item.swVer,jdbcType=INTEGER}, #{item.vendorCode,jdbcType=INTEGER}, #{item.path,jdbcType=VARCHAR}, now())
        </foreach>
    </insert>
</mapper>