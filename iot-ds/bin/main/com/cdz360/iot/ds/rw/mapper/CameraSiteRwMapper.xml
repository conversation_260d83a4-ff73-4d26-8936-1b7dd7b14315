<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.CameraSiteRwMapper">

	<resultMap id="RESULT_CAMERASITE_PO" type="com.cdz360.iot.model.camera.po.CameraSitePo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="accountId" jdbcType="BIGINT" property="accountId" />
		<result column="cameraSiteId" jdbcType="VARCHAR" property="cameraSiteId" />
		<result column="cameraSiteName" jdbcType="VARCHAR" property="cameraSiteName" />
		<result column="cameraSiteMeasure" jdbcType="VARCHAR" property="cameraSiteMeasure" />
		<result column="cameraSiteDetailAddress" jdbcType="VARCHAR" property="cameraSiteDetailAddress" />
		<result column="addressProvince" jdbcType="VARCHAR" property="addressProvince" />
		<result column="addressCity" jdbcType="VARCHAR" property="addressCity" />
		<result column="addressCounty" jdbcType="VARCHAR" property="addressCounty" />
		<result column="addressDetail" jdbcType="VARCHAR" property="addressDetail" />
		<result column="mangerName" jdbcType="VARCHAR" property="mangerName" />
		<result column="mangerTel" jdbcType="VARCHAR" property="mangerTel" />
		<result column="cameraSiteTel" jdbcType="VARCHAR" property="cameraSiteTel" />
		<result column="cameraSiteNo" jdbcType="VARCHAR" property="cameraSiteNo" />
		<result column="cameraSiteLng" jdbcType="VARCHAR" property="cameraSiteLng" />
		<result column="cameraSiteLat" jdbcType="VARCHAR" property="cameraSiteLat" />
		<result column="cameraSiteRemark" jdbcType="VARCHAR" property="cameraSiteRemark" />
		<result column="areaPath" jdbcType="VARCHAR" property="areaPath" />
		<result column="insertTime" jdbcType="VARCHAR" property="insertTime" />
		<result column="updateTime" jdbcType="VARCHAR" property="updateTime" />
		<result column="enable" jdbcType="BOOLEAN" property="enable" />
		<result column="accessToken" jdbcType="VARCHAR" property="accessToken" />
		<result column="accessTokenExpires" jdbcType="TIMESTAMP" property="accessTokenExpires" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateAt" jdbcType="TIMESTAMP" property="updateAt" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_CAMERASITE_PO">	
		select * from t_camera_site where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<sql id="insert-sub">
		(`siteId`,
		`accountId`,
		`cameraSiteId`,
		`cameraSiteName`,
		`cameraSiteMeasure`,
		`cameraSiteDetailAddress`,
		`addressProvince`,
		`addressCity`,
		`addressCounty`,
		`addressDetail`,
		`mangerName`,
		`mangerTel`,
		`cameraSiteTel`,
		`cameraSiteNo`,
		`cameraSiteLng`,
		`cameraSiteLat`,
		`cameraSiteRemark`,
		`areaPath`,
		`insertTime`,
		`updateTime`,
		`enable`,
		`accessToken`,
		`accessTokenExpires`,
		`createTime`,
		`updateAt`)
		values (#{siteId},
		#{accountId},
		#{cameraSiteId},
		#{cameraSiteName},
		#{cameraSiteMeasure},
		#{cameraSiteDetailAddress},
		#{addressProvince},
		#{addressCity},
		#{addressCounty},
		#{addressDetail},
		#{mangerName},
		#{mangerTel},
		#{cameraSiteTel},
		#{cameraSiteNo},
		#{cameraSiteLng},
		#{cameraSiteLat},
		#{cameraSiteRemark},
		#{areaPath},
		#{insertTime},
		#{updateTime},
		#{enable},
		#{accessToken},
		#{accessTokenExpires},
		now(),
		#{updateAt})
	</sql>

	<sql id="update-sub">
		<if test="siteId != null">
			siteId = #{siteId},
		</if>
		<if test="accountId != null">
			accountId = #{accountId},
		</if>
		<if test="cameraSiteId != null">
			cameraSiteId = #{cameraSiteId},
		</if>
		<if test="cameraSiteName != null">
			cameraSiteName = #{cameraSiteName},
		</if>
		<if test="cameraSiteMeasure != null">
			cameraSiteMeasure = #{cameraSiteMeasure},
		</if>
		<if test="cameraSiteDetailAddress != null">
			cameraSiteDetailAddress = #{cameraSiteDetailAddress},
		</if>
		<if test="addressProvince != null">
			addressProvince = #{addressProvince},
		</if>
		<if test="addressCity != null">
			addressCity = #{addressCity},
		</if>
		<if test="addressCounty != null">
			addressCounty = #{addressCounty},
		</if>
		<if test="addressDetail != null">
			addressDetail = #{addressDetail},
		</if>
		<if test="mangerName != null">
			mangerName = #{mangerName},
		</if>
		<if test="mangerTel != null">
			mangerTel = #{mangerTel},
		</if>
		<if test="cameraSiteTel != null">
			cameraSiteTel = #{cameraSiteTel},
		</if>
		<if test="cameraSiteNo != null">
			cameraSiteNo = #{cameraSiteNo},
		</if>
		<if test="cameraSiteLng != null">
			cameraSiteLng = #{cameraSiteLng},
		</if>
		<if test="cameraSiteLat != null">
			cameraSiteLat = #{cameraSiteLat},
		</if>
		<if test="cameraSiteRemark != null">
			cameraSiteRemark = #{cameraSiteRemark},
		</if>
		<if test="areaPath != null">
			areaPath = #{areaPath},
		</if>
		<if test="insertTime != null">
			insertTime = #{insertTime},
		</if>
		<if test="updateTime != null">
			updateTime = #{updateTime},
		</if>
		<if test="enable != null">
			enable = #{enable},
		</if>
		<if test="accessToken != null">
			accessToken = #{accessToken},
		</if>
		<if test="accessTokenExpires != null">
			accessTokenExpires = #{accessTokenExpires},
		</if>
		updateAt = now()
	</sql>

	<insert id="insertCameraSite" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.iot.model.camera.po.CameraSitePo">
		insert into t_camera_site
		<include refid="insert-sub"></include>
	</insert>

	<update id="updateCameraSite" parameterType="com.cdz360.iot.model.camera.po.CameraSitePo">
		update t_camera_site set
		<include refid="update-sub"></include>
		where id = #{id}
	</update>

	<update id="disableSiteByAccountId">
		update t_camera_site set
		enable = 0
		<where>
			accountId = #{accountId}
		</where>
	</update>

	<insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.cdz360.iot.model.camera.po.CameraSitePo"
			useGeneratedKeys="true">
		insert into t_camera_site
		<include refid="insert-sub"></include>
		on DUPLICATE key UPDATE
		<include refid="update-sub"></include>
	</insert>


</mapper>
