<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.PartsTypeRwMapper">



	<resultMap id="RESULT_PARTSTYPE_PO" type="com.cdz360.iot.model.parts.po.PartsTypePo">

		<id column="id" jdbcType="BIGINT" property="id" />

		<result column="code" jdbcType="VARCHAR" property="code" />

		<result column="name" jdbcType="VARCHAR" property="name" />

		<result column="fullModel" jdbcType="VARCHAR" property="fullModel" />

	</resultMap>



	<select id="getById"

			resultMap="RESULT_PARTSTYPE_PO">	
		select * from t_parts_type where id = #{id}

		<if test="lock == true">

			for update

		</if>

	</select>



	<insert id="insertPartsType" useGeneratedKeys="true" keyProperty="id"

		keyColumn="id" parameterType="com.cdz360.iot.model.parts.po.PartsTypePo">

		insert into t_parts_type (`code`,

			`name`,

			`fullModel`)

		values (#{code},

			#{name},

			#{fullModel})

	</insert>



	<update id="updatePartsType" parameterType="com.cdz360.iot.model.parts.po.PartsTypePo">

		update t_parts_type set

		<if test="code != null">

			code = #{code},

		</if>

		<if test="name != null">

			name = #{name},

		</if>

		<if test="fullModel != null">

			fullModel = #{fullModel},

		</if>

		where id = #{id}

	</update>



</mapper>

