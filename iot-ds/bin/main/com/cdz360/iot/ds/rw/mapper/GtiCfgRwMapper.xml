<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.GtiCfgRwMapper">



	<resultMap id="RESULT_GTICFG_PO" type="com.cdz360.iot.model.pv.po.GtiCfgPo">

		<id column="cfgId" jdbcType="BIGINT" property="cfgId" />

		<result column="samplingTime" jdbcType="INTEGER" property="samplingTime" />

		<result column="timeout" jdbcType="INTEGER" property="timeout" />

		<result column="bootVoltage" jdbcType="DECIMAL" property="bootVoltage" />

		<result column="minVoltage" jdbcType="DECIMAL" property="minVoltage" />

		<result column="maxVoltage" jdbcType="DECIMAL" property="maxVoltage" />

		<result column="minFrequency" jdbcType="DECIMAL" property="minFrequency" />

		<result column="maxFrequency" jdbcType="DECIMAL" property="maxFrequency" />

		<result column="gridMode" jdbcType="INTEGER" property="gridMode" />

	</resultMap>



	<select id="getById"
		resultMap="RESULT_GTICFG_PO">
		select * from t_gti_cfg where cfgId = #{cfgId}

		<if test="lock == true">

			for update

		</if>

	</select>



	<insert id="insertGtiCfg" useGeneratedKeys="true" keyProperty="id"

		keyColumn="id" parameterType="com.cdz360.iot.model.pv.po.GtiCfgPo">

		insert into t_gti_cfg (`cfgId`,

			`samplingTime`,

			`timeout`,

			`bootVoltage`,

			`minVoltage`,

			`maxVoltage`,

			`minFrequency`,

			`maxFrequency`,

			`gridMode`)

		values (

			#{cfgId},

			#{samplingTime},

			#{timeout},

			#{bootVoltage},

			#{minVoltage},

			#{maxVoltage},

			#{minFrequency},

			#{maxFrequency},

			#{gridMode})

	</insert>



	<update id="updateGtiCfg" parameterType="com.cdz360.iot.model.pv.po.GtiCfgPo">

		update t_gti_cfg
		<set>

			<if test="samplingTime != null">

				samplingTime = #{samplingTime},

			</if>

			<if test="timeout != null">

				timeout = #{timeout},

			</if>

			<if test="bootVoltage != null">

				bootVoltage = #{bootVoltage},

			</if>

			<if test="minVoltage != null">

				minVoltage = #{minVoltage},

			</if>

			<if test="maxVoltage != null">

				maxVoltage = #{maxVoltage},

			</if>

			<if test="minFrequency != null">

				minFrequency = #{minFrequency},

			</if>

			<if test="maxFrequency != null">

				maxFrequency = #{maxFrequency},

			</if>

			<if test="gridMode != null">

				gridMode = #{gridMode},

			</if>

		</set>
		where cfgId = #{cfgId}

	</update>



</mapper>

