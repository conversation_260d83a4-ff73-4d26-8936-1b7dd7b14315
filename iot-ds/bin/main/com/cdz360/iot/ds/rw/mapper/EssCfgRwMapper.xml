<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EssCfgRwMapper">

  <resultMap id="RESULT_ESSCFG_PO" type="com.cdz360.iot.model.ess.po.EssCfgPo">
    <result column="cfgId" jdbcType="BIGINT" property="cfgId"/>
    <result column="cfgNo" jdbcType="VARCHAR" property="cfgNo"/>
    <result column="strategy" property="strategy"/>
    <result column="samplingTime" jdbcType="INTEGER" property="samplingTime"/>
    <result column="bootMode" property="bootMode"/>
    <result column="switchCommand" property="switchCommand"/>
    <result column="strategyCfg" typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"
      property="strategyCfg"/>
    <result column="priceCfg" typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"
      property="priceCfg"/>
    <result column="dischargePriceCfg" typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"
      property="dischargePriceCfg"/>
    <result column="chargeStrategy" typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"
      property="chargeStrategy"/>
  </resultMap>

  <select id="getByCfgId" resultMap="RESULT_ESSCFG_PO">
    select * from t_ess_cfg where cfgId = #{cfgId}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertEssCfg" useGeneratedKeys="true" keyProperty="cfgId"
    keyColumn="cfgId" parameterType="com.cdz360.iot.model.ess.po.EssCfgPo">
    insert into t_ess_cfg (`cfgNo`,
    `strategy`,
    <if test="null != otherStrategy">
      otherStrategy,
    </if>
    <if test="null != repeatCycle">
      repeatCycle,
    </if>
    <if test="null != effectiveStartTime">
      effectiveStartTime,
    </if>
    <if test="null != effectiveEndTime">
      effectiveEndTime,
    </if>
    `samplingTime`,
    <if test="null != uploadDataTime">
      `uploadDataTime`,
    </if>
    <if test="null != uploadInfoTime">
      `uploadInfoTime`,
    </if>
    `bootMode`,
    `switchCommand`,
    `priceCfg`,
    <if test="null != dischargePriceCfg">
      `dischargePriceCfg`,
    </if>
    `chargeStrategy`,
    `strategyCfg`)
    values (#{cfgNo},
    #{strategy},
    <if test="null != otherStrategy">
      #{otherStrategy},
    </if>
    <if test="null != repeatCycle">
      #{repeatCycle},
    </if>
    <if test="null != effectiveStartTime">
      #{effectiveStartTime},
    </if>
    <if test="null != effectiveEndTime">
      #{effectiveEndTime},
    </if>
    #{samplingTime},
    <if test="null != uploadDataTime">
      #{uploadDataTime},
    </if>
    <if test="null != uploadInfoTime">
      #{uploadInfoTime},
    </if>
    #{bootMode},
    #{switchCommand},
    #{priceCfg, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
    <if test="null != dischargePriceCfg">
      #{dischargePriceCfg, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
    </if>
    #{chargeStrategy, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
    #{strategyCfg, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler})
    ON DUPLICATE KEY UPDATE
    cfgNo = #{cfgNo},
    <if test="null != samplingTime">
      samplingTime = #{samplingTime},
    </if>
    <if test="null != uploadDataTime">
      uploadDataTime = #{uploadDataTime},
    </if>
    <if test="null != uploadInfoTime">
      uploadInfoTime = #{uploadInfoTime},
    </if>
    <if test="null != bootMode">
      bootMode = #{bootMode},
    </if>
    <if test="null != switchCommand">
      switchCommand = #{switchCommand},
    </if>
    <if test="null != priceCfg">
      priceCfg = #{priceCfg, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
    </if>
    <if test="null != dischargePriceCfg">
      dischargePriceCfg =
      #{dischargePriceCfg, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
    </if>
    <if test="null != chargeStrategy">
      chargeStrategy = #{chargeStrategy, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
    </if>
    <if test="null != strategyCfg">
      strategyCfg = #{strategyCfg, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
    </if>
    <if test="null != otherStrategy">
      otherStrategy = #{otherStrategy},
    </if>
    <if test="null != repeatCycle">
      repeatCycle = #{repeatCycle},
    </if>
    <if test="null != effectiveStartTime">
      effectiveStartTime = #{effectiveStartTime},
    </if>
    <if test="null != effectiveEndTime">
      effectiveEndTime = #{effectiveEndTime},
    </if>
    strategy = #{strategy}
  </insert>

  <insert id="insertBeforeSelect">
    insert into t_ess_cfg (cfgId,strategy,samplingTime,bootMode,switchCommand)
    select #{newId}, strategy,samplingTime,bootMode,switchCommand from t_ess_cfg where cfgId=
    #{oldId};
  </insert>

</mapper>

