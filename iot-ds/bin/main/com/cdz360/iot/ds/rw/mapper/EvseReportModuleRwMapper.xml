<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvseReportModuleRwMapper">

    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.cdz360.iot.model.evse.po.EvseReportModulePo"
            useGeneratedKeys="true">
        insert into t_evse_report_module
            (evseNo, moduleType, `number`)
        values
            (#{evseNo}, #{moduleType}, #{number})
        on DUPLICATE key UPDATE
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( moduleType )">
            moduleType = #{moduleType},
        </if>
        <if test="number != null">
            number = #{number},
        </if>
        updateTime = now()
    </insert>

    <update id="updateNumber" parameterType="com.cdz360.iot.model.evse.po.EvseReportModulePo">
        update
            t_evse_report_module
        set
            `number` = #{number},
            `updateTime` = now()
        where
            evseNo = #{evseNo}
    </update>

</mapper>