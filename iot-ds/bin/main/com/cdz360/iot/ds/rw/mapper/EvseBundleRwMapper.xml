<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvseBundleRwMapper">
    <update id="updateEnableById" parameterType="java.lang.Long">
        update t_evse_bundle
        set enable = false,updateTime = now()
        where id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from t_evse_bundle
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cdz360.iot.model.evse.EvseBundle"
            useGeneratedKeys="true">
        insert into t_evse_bundle (version, fileName, releaseNote, opId, opName, protocol, context, createTime,
        `enable`)
        values (#{version}, #{fileName,jdbcType=VARCHAR}, #{releaseNote,jdbcType=LONGVARCHAR},
        #{opId,jdbcType=BIGINT}, #{opName,jdbcType=VARCHAR}, #{protocol,jdbcType=INTEGER},
        #{context,jdbcType=LONGVARCHAR}, now(), #{enable,jdbcType=BOOLEAN})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cdz360.iot.model.evse.EvseBundle"
            useGeneratedKeys="true">
        insert into t_evse_bundle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">
                type,
            </if>
            <if test="vendor != null">
                vendor,
            </if>
            <if test="bundleSize != null">
                bundleSize,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="fileName != null">
                fileName,
            </if>
            <if test="releaseNote != null">
                releaseNote,
            </if>
            <if test="opId != null">
                opId,
            </if>
            <if test="opName != null">
                opName,
            </if>
            <if test="protocol != null">
                protocol,
            </if>
            <if test="context != null">
                context,
            </if>
            <if test="enable != null">
                `enable`,
            </if>
            createTime, updateTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">
                #{type},
            </if>
            <if test="vendor != null">
                #{vendor},
            </if>
            <if test="bundleSize != null">
                #{bundleSize},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="releaseNote != null">
                #{releaseNote,jdbcType=LONGVARCHAR},
            </if>
            <if test="opId != null">
                #{opId,jdbcType=BIGINT},
            </if>
            <if test="opName != null">
                #{opName,jdbcType=VARCHAR},
            </if>
            <if test="protocol != null">
                #{protocol,jdbcType=INTEGER},
            </if>
            <if test="context != null">
                #{context,jdbcType=LONGVARCHAR},
            </if>
            <if test="enable != null">
                #{enable,jdbcType=BOOLEAN},
            </if>
            now(), now()
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.cdz360.iot.model.evse.EvseBundle">
        update t_evse_bundle
        <set>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="fileName != null">
                fileName = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="vendor != null">
                vendor = #{vendor},
            </if>
            <if test="bundleSize != null">
                bundleSize = #{bundleSize,jdbcType=BIGINT},
            </if>
            <if test="releaseNote != null">
                releaseNote = #{releaseNote,jdbcType=LONGVARCHAR},
            </if>
            <if test="opId != null">
                opId = #{opId,jdbcType=BIGINT},
            </if>
            <if test="opName != null">
                opName = #{opName,jdbcType=VARCHAR},
            </if>
            <if test="protocol != null">
                protocol = #{protocol,jdbcType=INTEGER},
            </if>
            <if test="context != null">
                context = #{context,jdbcType=LONGVARCHAR},
            </if>
            <if test="enable != null">
                `enable` = #{enable,jdbcType=BOOLEAN},
            </if>
            updateTime = now()
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.cdz360.iot.model.evse.EvseBundle">
        update t_evse_bundle
        set version = #{version},
        type = #{type},
        bundleSize = #{bundleSize},
        fileName = #{fileName,jdbcType=VARCHAR},
        releaseNote = #{releaseNote,jdbcType=LONGVARCHAR},
        opId = #{opId,jdbcType=BIGINT},
        opName = #{opName,jdbcType=VARCHAR},
        protocol = #{protocol,jdbcType=INTEGER},
        context = #{context,jdbcType=LONGVARCHAR},
        `enable` = #{enable,jdbcType=BOOLEAN},
        updateTime = now()
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>