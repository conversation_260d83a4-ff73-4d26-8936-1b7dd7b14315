<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.StorageRwMapper">

	<resultMap id="RESULT_STORAGE_PO" type="com.cdz360.iot.model.parts.po.StoragePo">
		<result column="code" jdbcType="VARCHAR" property="code" />
		<result column="name" jdbcType="VARCHAR" property="name" />
		<result column="type" property="type" />
		<result column="uid" jdbcType="BIGINT" property="uid" />
	</resultMap>

	<insert id="insertStorage" parameterType="com.cdz360.iot.model.parts.po.StoragePo">
		insert into t_storage (`code`,
			`name`,
			`type`,
			`uid`)
		values (#{code},
			#{name},
			#{type},
			#{uid})
	</insert>

  <select id="getByUid" resultMap="RESULT_STORAGE_PO">
		select * from t_storage where uid = #{uid}
	</select>
  <select id="getOneByName" resultMap="RESULT_STORAGE_PO">
		select * from t_storage where name = #{name} limit 1
	</select>


</mapper>

