<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.ess.mapper.EssDtuRwMapper">

  <resultMap id="RESULT_ESS_DTU_PO" type="com.cdz360.iot.model.dtu.po.EssDtuPo">
    <id column="serialNo" jdbcType="VARCHAR" property="serialNo"/>
    <result column="gwno" jdbcType="VARCHAR" property="gwno"/>
    <result column="essDtuType" property="essDtuType"/>
    <result column="communicationWay" property="communicationWay"/>
    <result column="deviceName" jdbcType="VARCHAR" property="deviceName"/>
    <result column="deviceModel" jdbcType="VARCHAR" property="deviceModel"/>
    <result column="hardwareVer" jdbcType="VARCHAR" property="hardwareVer"/>
    <result column="softwareVer" jdbcType="VARCHAR" property="softwareVer"/>
    <result column="iccid" jdbcType="VARCHAR" property="iccid"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getBySerialNo"
    resultMap="RESULT_ESS_DTU_PO">
    select * from t_ess_dtu where serialNo = #{serialNo}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertEssDtu" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.dtu.po.EssDtuPo">
    insert into t_ess_dtu (`serialNo`,`gwno`,
    `essDtuType`,
    `communicationWay`,
    `deviceName`,
    `deviceModel`,
    `hardwareVer`,
    `softwareVer`,
    `iccid`,
    `ip`,
    `enable`,
    `updateTime`)
    values (#{serialNo},#{gwno},
    #{essDtuType},
    #{communicationWay},
    #{deviceName},
    #{deviceModel},
    #{hardwareVer},
    #{softwareVer},
    #{iccid},
    #{ip},
    true,
    now())
  </insert>

  <update id="updateEssDtu" parameterType="com.cdz360.iot.model.dtu.po.EssDtuPo">
    update t_ess_dtu set
    <if test="gwno != null">
      gwno = #{gwno},
    </if>
    <if test="essDtuType != null">
      essDtuType = #{essDtuType},
    </if>
    <if test="communicationWay != null">
      communicationWay = #{communicationWay},
    </if>
    <if test="deviceName != null">
      deviceName = #{deviceName},
    </if>
    <if test="deviceModel != null">
      deviceModel = #{deviceModel},
    </if>
    <if test="hardwareVer != null">
      hardwareVer = #{hardwareVer},
    </if>
    <if test="softwareVer != null">
      softwareVer = #{softwareVer},
    </if>
    <if test="iccid != null">
      iccid = #{iccid},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where serialNo = #{serialNo}
  </update>
</mapper>

