<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.ParkChannelRwMapper">



	<resultMap id="RESULT_PARKCHANNEL_PO" type="com.cdz360.iot.model.park.po.ParkChannelPo">

		<id column="id" jdbcType="BIGINT" property="id" />

		<result column="parkId" jdbcType="BIGINT" property="parkId" />

		<result column="channelId" jdbcType="BIGINT" property="channelId" />

		<result column="deviceId" jdbcType="VARCHAR" property="deviceId" />
		<result column="enable" property="enable" />

		<result column="passType" property="passType"
				typeHandler="com.cdz360.ds.type.EnumTypeHandler" />

		<result column="channelName" jdbcType="VARCHAR" property="channelName" />

		<result column="thirdRecId" jdbcType="VARCHAR" property="thirdRecId" />

		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />

		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />

	</resultMap>



	<select id="getById"

			resultMap="RESULT_PARKCHANNEL_PO">	
		select * from t_park_channel where id = #{id}

		<if test="lock == true">

			for update

		</if>

	</select>



	<insert id="insertParkChannel" useGeneratedKeys="true" keyProperty="id"

		keyColumn="id" parameterType="com.cdz360.iot.model.park.po.ParkChannelPo">

		insert into t_park_channel (`parkId`,

			`channelId`,

			`deviceId`,
<!--			`enable`,-->
			`passType`,

			`channelName`,

			`thirdRecId`,

			`createTime`,

			`updateTime`)

		values (#{parkId},

			#{channelId},

			#{deviceId},
<!--			#{enable},-->

			#{passType, typeHandler=com.cdz360.ds.type.EnumTypeHandler},

			#{channelName},

			#{thirdRecId},

			now(),

			now())

	</insert>



	<update id="updateParkChannel" parameterType="com.cdz360.iot.model.park.po.ParkChannelPo">

		update t_park_channel set

		<if test="enable != null">

			enable = #{enable},

		</if>

		<if test="passType != null">

			passType = #{passType, typeHandler=com.cdz360.ds.type.EnumTypeHandler},

		</if>

		<if test="channelName != null">

			channelName = #{channelName},

		</if>

		<if test="thirdRecId != null">

			thirdRecId = #{thirdRecId},

		</if>

		updateTime = now()

		where parkId = #{parkId} and channelId = #{channelId} and deviceId = #{deviceId}

	</update>



</mapper>

