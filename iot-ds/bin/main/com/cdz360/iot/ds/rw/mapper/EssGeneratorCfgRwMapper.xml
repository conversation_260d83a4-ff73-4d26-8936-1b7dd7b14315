<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.EssGeneratorCfgRwMapper">



	<resultMap id="RESULT_ESSGENERATORCFG_PO" type="com.cdz360.iot.model.ess.po.EssGeneratorCfgPo">

		<result column="cfgId" jdbcType="BIGINT" property="cfgId" />

		<result column="ctrlEnable" jdbcType="BOOLEAN" property="ctrlEnable" />

		<result column="stopMode" property="stopMode" />

		<result column="startSoc" jdbcType="INTEGER" property="startSoc" />

		<result column="stopSoc" jdbcType="INTEGER" property="stopSoc" />

		<result column="startTime" jdbcType="INTEGER" property="startTime" />

		<result column="stopTime" jdbcType="INTEGER" property="stopTime" />

		<result column="powerOutMode" property="powerOutMode" />

		<result column="chargePower" jdbcType="DECIMAL" property="chargePower" />

		<result column="ratedPower" jdbcType="DECIMAL" property="ratedPower" />

	</resultMap>



	<insert id="insertEssGeneratorCfg" useGeneratedKeys="true" keyProperty="id"

		keyColumn="id" parameterType="com.cdz360.iot.model.ess.po.EssGeneratorCfgPo">

		insert into t_ess_generator_cfg (`cfgId`,

			`ctrlEnable`,

			`stopMode`,

			`startSoc`,

			`stopSoc`,

			`startTime`,

			`stopTime`,

			`powerOutMode`,

			`chargePower`,

			`ratedPower`)

		values (#{cfgId},

			#{ctrlEnable},

			#{stopMode},

			#{startSoc},

			#{stopSoc},

			#{startTime},

			#{stopTime},

			#{powerOutMode},

			#{chargePower},

			#{ratedPower})

		ON DUPLICATE KEY UPDATE
			ctrlEnable = #{ctrlEnable},

			stopMode = #{stopMode},

			startSoc = #{startSoc},

			stopSoc = #{stopSoc},

			startTime = #{startTime},

			stopTime = #{stopTime},

			powerOutMode = #{powerOutMode},

			chargePower = #{chargePower},

			ratedPower = #{ratedPower}


	</insert>
    <insert id="insertBeforeSelect">
		insert into t_ess_generator_cfg(cfgId,ctrlEnable,stopMode,startSoc,stopSoc,startTime,stopTime,powerOutMode,chargePower,ratedPower)
		select #{newId}, ctrlEnable,stopMode,startSoc,stopSoc,startTime,stopTime,powerOutMode,chargePower,ratedPower from t_ess_generator_cfg where cfgId = #{oldId};
	</insert>


</mapper>

