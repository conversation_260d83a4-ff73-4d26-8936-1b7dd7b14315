<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.UpgradeTaskDetailRwMapper">
    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.iot.model.evse.upgrade.UpgradeTaskDetailVo">
        insert into t_upgrade_task_detail(
        taskId,
        evseId,
        pc01Ver,
        pc02Ver,
        pc03Ver,
        status,
        failReason,
        updateTime
        ) values (
        #{taskId},
        #{evseId},
        #{pc01Ver},
        #{pc02Ver},
        #{pc03Ver},
        #{status},
        #{failReason},
        now()
        )

    </insert>

    <update id="update" parameterType="com.cdz360.iot.model.evse.upgrade.UpgradeTaskDetailVo">
        update t_upgrade_task_detail
        <set>
            <if test="upgradeTaskDetailVo.taskId != null">
                taskId=#{upgradeTaskDetailVo.taskId},
            </if>
            <if test="upgradeTaskDetailVo.pc01Ver != null">
                pc01Ver=#{upgradeTaskDetailVo.pc01Ver},
            </if>
            <if test="upgradeTaskDetailVo.pc02Ver != null">
                pc02Ver=#{upgradeTaskDetailVo.pc02Ver},
            </if>
            <if test="upgradeTaskDetailVo.pc03Ver != null">
                pc03Ver=#{upgradeTaskDetailVo.pc03Ver},
            </if>
            <if test="upgradeTaskDetailVo.status != null">
                status=#{upgradeTaskDetailVo.status},
            </if>
            <if test="upgradeTaskDetailVo.failReason != null">
                failReason=#{upgradeTaskDetailVo.failReason},
            </if>
            <if test="upgradeTaskDetailVo.updateTime != null">
                updateTime=#{upgradeTaskDetailVo.updateTime},
            </if>
        </set>
        <where>
            id = #{upgradeTaskDetailVo.id}
        </where>
    </update>
</mapper>
