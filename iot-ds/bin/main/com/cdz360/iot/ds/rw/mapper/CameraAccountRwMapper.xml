<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.CameraAccountRwMapper">

	<resultMap id="RESULT_CAMERAACCOUNT_PO" type="com.cdz360.iot.model.camera.po.CameraAccountPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="commId" jdbcType="BIGINT" property="commId" />
		<result column="clientId" jdbcType="VARCHAR" property="clientId" />
		<result column="clientSecret" jdbcType="VARCHAR" property="clientSecret" />
		<result column="grantType" jdbcType="VARCHAR" property="grantType" />
		<result column="scope" jdbcType="VARCHAR" property="scope" />
		<result column="accessToken" jdbcType="VARCHAR" property="accessToken" />
		<result column="tokenType" jdbcType="VARCHAR" property="tokenType" />
		<result column="expiresIn" jdbcType="TIMESTAMP" property="expiresIn" />
		<result column="accountToken" jdbcType="VARCHAR" property="accountToken" />
		<result column="accountAppKey" jdbcType="VARCHAR" property="accountAppKey" />
		<result column="accountExpireTime" jdbcType="TIMESTAMP" property="accountExpireTime" />
		<result column="enable" jdbcType="BOOLEAN" property="enable" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_CAMERAACCOUNT_PO">	
		select * from t_camera_account where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertCameraAccount" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.iot.model.camera.po.CameraAccountPo">
		insert into t_camera_account (`commId`,
			`clientId`,
			`clientSecret`,
			`grantType`,
			`scope`,
			`accessToken`,
			`tokenType`,
			`expiresIn`,
			`accountToken`,
			`accountAppKey`,
			`accountExpireTime`,
			`enable`,
			`createTime`,
			`updateTime`)
		values (#{commId},
			#{clientId},
			#{clientSecret},
			#{grantType},
			#{scope},
			#{accessToken},
			#{tokenType},
			#{expiresIn},
			#{accountToken},
			#{accountAppKey},
			#{accountExpireTime},
			#{enable},
			now(),
			now())
	</insert>

	<update id="updateCameraAccount" parameterType="com.cdz360.iot.model.camera.po.CameraAccountPo">
		update t_camera_account set
		<if test="commId != null">
			commId = #{commId},
		</if>
		<if test="clientId != null">
			clientId = #{clientId},
		</if>
		<if test="clientSecret != null">
			clientSecret = #{clientSecret},
		</if>
		<if test="grantType != null">
			grantType = #{grantType},
		</if>
		<if test="scope != null">
			scope = #{scope},
		</if>
		<if test="accessToken != null">
			accessToken = #{accessToken},
		</if>
		<if test="tokenType != null">
			tokenType = #{tokenType},
		</if>
		<if test="expiresIn != null">
			expiresIn = #{expiresIn},
		</if>
		<if test="accountToken != null">
			accountToken = #{accountToken},
		</if>
		<if test="accountAppKey != null">
			accountAppKey = #{accountAppKey},
		</if>
		<if test="accountExpireTime != null">
			accountExpireTime = #{accountExpireTime},
		</if>
		<if test="enable != null">
			enable = #{enable},
		</if>
		updateTime = now()
		where id = #{id}
	</update>

</mapper>
