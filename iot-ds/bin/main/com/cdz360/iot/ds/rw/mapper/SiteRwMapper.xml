<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.SiteRwMapper">


  <insert id="insertOrUpdate" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.site.po.SitePo">
    insert into t_site(`name`, topCommId, commId, `dzType`,
    bizType, status, priceCode, dyPow,
    lon, `lat`, `timeZone`, `address`,
    phone, provinceCode, cityCode, areaCode, dzId, createTime, updateTime,siteNo
    )
    values (#{name}, #{topCommId}, #{commId}, #{type},
    #{bizType}, #{status.value},
    #{priceCode}, #{dyPow.code}, #{lon}, #{lat}, #{timeZone}, #{address},
    #{phone}, #{provinceCode}, #{cityCode}, #{areaCode}, #{siteId},
    now(), now(),#{siteNo}
    )
    on DUPLICATE key UPDATE
    `name` = #{name},
    `topCommId` = #{topCommId},
    `commId` = #{commId},
    `dzType` = #{type},
    `bizType` = #{bizType},
    `status` = #{status.value},
    `priceCode` = #{priceCode},
    `dyPow` = #{dyPow.code},
    `lon` = #{lon},
    `lat` = #{lat},
    <if test="timeZone != null">
      timeZone = #{timeZone},
    </if>
    `address` = #{address},
    `phone` = #{phone},
    `provinceCode` = #{provinceCode},
    `cityCode` = #{cityCode},
    `areaCode` = #{areaCode},
    `dzId` = #{siteId},
    `siteNo` = #{siteNo},
    updateTime = now()
  </insert>

  <insert id="insertByCondition">
    insert into t_site
    (
    <if test="name != null and name != ''">
      `name`
    </if>
    <if test="topCommId != null">
      , topCommId
    </if>
    <if test="commId != null">
      , commId
    </if>
    <if test="type != null">
      , dzType
    </if>
    <if test="bizType != null">
      , bizType
    </if>
    <if test="status != null">
      ,status
    </if>
    <if test="priceCode != null">
      ,priceCode
    </if>
    <if test="lon != null">
      ,lon
    </if>
    <if test="lat != null">
      ,lat
    </if>
    <if test="timeZone != null">
      ,timeZone
    </if>
    <if test="address != null and address != ''">
      ,address
    </if>
    <if test="phone != null and phone != ''">
      ,phone
    </if>
    <if test="provinceCode != null and provinceCode != ''">
      ,provinceCode
    </if>
    <if test="cityCode != null and cityCode != ''">
      ,cityCode
    </if>
    <if test="areaCode != null and areaCode != ''">
      ,areaCode
    </if>
    <if test="siteId != null">
      ,dzId
    </if>
    ,createTime,updateTime
    )
    values
    (
    <if test="name != null and name != ''">
      #{name}
    </if>
    <if test="topCommId != null">
      , #{topCommId}
    </if>
    <if test="commId != null">
      , #{commId}
    </if>
    <if test="type != null">
      , #{type}
    </if>
    <if test="bizType != null">
      , #{bizType}
    </if>
    <if test="status != null">
      ,#{status.value}
    </if>
    <if test="priceCode != null">
      ,#{priceCode}
    </if>
    <if test="lon != null and lon != ''">
      ,#{lon}
    </if>
    <if test="lat != null and lat != ''">
      ,#{lat}
    </if>
    <if test="timeZone != null">
      ,#{timeZone}
    </if>
    <if test="address != null">
      ,#{address}
    </if>
    <if test="phone != null">
      ,#{phone}
    </if>
    <if test="provinceCode != null and provinceCode != ''">
      ,#{provinceCode}
    </if>
    <if test="cityCode != null and cityCode != ''">
      ,#{cityCode}
    </if>
    <if test="areaCode != null and areaCode != ''">
      ,#{areaCode}
    </if>
    <if test="siteId != null">
      ,#{siteId}
    </if>
    ,now(), now()
    )
  </insert>

  <update id="update" parameterType="com.cdz360.iot.model.site.po.SitePo">
    update t_site set
    <if test="name!=null and ''!=name">
      name=#{name},
    </if>
    <if test="topCommId != null">
      topCommId=#{topCommId},
    </if>
    <if test="commId != null">
      commId=#{commId},
    </if>
    <if test="type!=null">
      `dzType`=#{type},
    </if>
    <if test="bizType!=null">
      `bizType`=#{bizType},
    </if>
    <if test="status!=null">
      status=#{status.value},
    </if>
    <if test="priceCode!=null">
      priceCode=#{priceCode},
    </if>
    <if test="lon!=null and ''!=lon">
      lon=#{lon},
    </if>
    <if test="lat!=null and ''!=lat">
      lat=#{lat},
    </if>
    <if test="timeZone != null">
      timeZone = #{timeZone},
    </if>
    <if test="address!=null and ''!=address">
      address=#{address},
    </if>
    <if test="phone!=null and ''!=phone">
      phone=#{phone},
    </if>
    <if test="provinceCode!=null and ''!=provinceCode">
      provinceCode=#{provinceCode},
    </if>
    <if test="dyPow!=null">
      dyPow=#{dyPow.code},
    </if>
    <if test="cityCode!=null and ''!=cityCode">
      cityCode=#{cityCode},
    </if>
    <if test="areaCode!=null and ''!=areaCode">
      areaCode=#{areaCode},
    </if>
    updateTime=now()

    where dzId=#{siteId}

  </update>


</mapper>