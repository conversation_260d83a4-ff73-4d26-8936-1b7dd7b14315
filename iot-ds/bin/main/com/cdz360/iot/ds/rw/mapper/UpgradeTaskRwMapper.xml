<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.UpgradeTaskRwMapper">
    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.iot.model.evse.upgrade.UpgradeTaskVo">
        insert into t_upgrade_task(siteId, bundleId, evseCount, opId, opName, createTime)
        values(#{siteId}, #{bundleId}, #{evseCount}, #{opId}, #{opName}, now())
    </insert>
</mapper>
