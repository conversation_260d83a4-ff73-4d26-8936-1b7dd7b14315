<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.OfflineCardLogRwMapper">

    <insert id="addOfflineCardLog" parameterType="com.cdz360.iot.model.evse.po.OfflineCardLogPo">
        insert into t_offline_card_log
        (cardNo, evseNo, amount, createTime)
        values( #{cardNo}, #{evseNo}, #{amount}, now())
    </insert>

</mapper>