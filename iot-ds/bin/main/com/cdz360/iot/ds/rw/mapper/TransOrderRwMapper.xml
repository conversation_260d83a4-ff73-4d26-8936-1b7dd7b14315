<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.TransOrderRwMapper">

  <resultMap id="RESULT_RANSORDER_PO" type="com.cdz360.iot.model.parts.po.TransOrderPo">
    <id column="orderNo" jdbcType="VARCHAR" property="orderNo"/>
    <result column="type" property="type"/>
    <result column="status" property="status"/>
    <result column="fromCode" jdbcType="VARCHAR" property="fromCode"/>
    <result column="toCode" jdbcType="VARCHAR" property="toCode"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="createBy" jdbcType="BIGINT" property="createBy"/>
  </resultMap>

  <select id="getByOrderNo"
    resultMap="RESULT_RANSORDER_PO">
    select * from t_trans_order where orderNo = #{orderNo}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTransOrder" parameterType="com.cdz360.iot.model.parts.po.TransOrderPo">
    insert into t_trans_order (
    `orderNo`,
    `type`,
    `status`,
    `fromCode`,
    `toCode`,
    `createTime`,
    `createBy`)
    values (
    #{orderNo},
    #{type},
    #{status},
    #{fromCode},
    #{toCode},
    now(),
    #{createBy})
  </insert>

  <update id="updateTransOrder" parameterType="com.cdz360.iot.model.parts.po.TransOrderPo">
    update t_trans_order set
    <if test="type != null">
      type = #{type},
    </if>
    <if test="status != null">
      status = #{status},
    </if>
    <if test="fromCode != null">
      fromCode = #{fromCode},
    </if>
    <if test="toCode != null">
      toCode = #{toCode},
    </if>
    <if test="createBy != null">
      createBy = #{createBy},
    </if>
    where orderNo = #{orderNo}
  </update>
  <update id="postPartsReceive">
    update t_trans_order o
    left join t_parts_trans_ref ref on ref.transOrderNo = o.orderNo
    set o.`status` = 'RECEIVED'
    where o.`status` = 'TRANSFERRING'
    and ref.partsCode = #{partsCode}
  </update>

</mapper>

