<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.GwInfoRwQueryMapper">
    <resultMap type="com.cdz360.iot.model.site.dto.GwInfoDto" id="gwCommonInfoDto">
        <id column="id" property="id"/>
        <result column="gwno" property="gwno"/>
        <result column="ip" property="ip"/>
        <result column="siteId" property="siteId"/>
        <result column="status" property="status" />
        <result column="mqType" property="mqType" jdbcType="INTEGER"
                typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
        <result column="ver" property="ver" />
        <result column="cityCode" property="cityCode"/>
        <result column="siteName" property="siteName"/>
        <result column="lon" property="lon"/>
        <result column="lat" property="lat"/>
        <result column="lanIp" property="lanIp"/>
        <result column="mac" property="mac"/>

        <result column="bootTime" property="bootTime"/>
        <result column="swVer" property="swVer"/>
        <result column="swVerCode" property="swVerCode"/>
        <result column="sourceCodeVer" property="sourceCodeVer"/>
        <result column="expectUpgradeLogId" property="expectUpgradeLogId"/>
        <result column="actualUpgradeLogId" property="actualUpgradeLogId"/>

        <result column="createTime" property="createTime"/>
        <result column="updateTime" property="updateTime"/>
    </resultMap>

    <resultMap type="com.cdz360.iot.model.site.dto.GwInfoDto" id="gwInfoMac">
        <id column="id" property="id"/>
        <result column="ip" property="ip"/>
        <result column="lon" property="lon"/>
        <result column="lat" property="lat"/>
        <result column="status" property="status"/>
        <result column="mqType" property="mqType" jdbcType="INTEGER"
                typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
        <result column="ver" property="ver" />

        <result column="cityCode" property="cityCode"/>
        <result column="createTime" property="createTime"/>
        <result column="updateTime" property="updateTime"/>
        <result column="lanIp" property="lanIp"/>
        <result column="mac" property="mac"/>

        <result column="bootTime" property="bootTime"/>
        <result column="swVer" property="swVer"/>
        <result column="swVerCode" property="swVerCode"/>
        <result column="sourceCodeVer" property="sourceCodeVer"/>
    </resultMap>


    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.iot.model.site.po.GwInfoPo">
        insert into
        t_gw_info(gwno, passcode, ip, mqType, `ver`, cityCode, enable,
        createTime, updateTime, lanIp, mac
        )
        values(#{gwno}, #{passcode}, #{ip}, #{mqType.code}, #{ver}, #{cityCode}, #{enable},
        now(), now(), #{lanIp}, #{mac}
        )
    </insert>
    <insert id="upset">
        insert into
        t_gw_info(gwno, name,
        <if test="passcode !=null">
            passcode,
        </if>
        mqType, `ver`, enable, createTime, updateTime)
        values(#{gwno}, #{name},
        <if test="passcode !=null">
            #{passcode},
        </if>
        #{mqType.code}, #{ver}, true, now(), now())
        on DUPLICATE key UPDATE
        name = #{name},
        <if test="swVer !=null">
            swVer = #{swVer},
        </if>
        <if test="swVerCode !=null">
            swVerCode = #{swVerCode},
        </if>
        <if test="sourceCodeVer !=null">
            sourceCodeVer = #{sourceCodeVer},
        </if>
        <if test="expectUpgradeLogId !=null">
            expectUpgradeLogId = #{expectUpgradeLogId},
        </if>
        <if test="actualUpgradeLogId !=null">
            actualUpgradeLogId = #{actualUpgradeLogId},
        </if>
        updateTime = now()
    </insert>

    <update id="update" parameterType="com.cdz360.iot.model.site.po.GwInfoPo">
        update t_gw_info set
        <if test="lanIp !=null">
            `lanIp` = #{lanIp},
        </if>
        <if test="mac !=null">
            `mac` = #{mac},
        </if>
        <if test="mqType !=null">
            `mqType` = #{mqType.code},
        </if>
        <if test="ver !=null">
            `ver` = #{ver},
        </if>
        <if test="status !=null">
            `status` = #{status},
        </if>
        <if test="bootTime !=null">
            `bootTime` = #{bootTime},
        </if>
        <if test="swVer !=null">
            `swVer` = #{swVer},
        </if>
        <if test="swVerCode !=null">
            `swVerCode` = #{swVerCode},
        </if>
        <if test="sourceCodeVer !=null">
            `sourceCodeVer` = #{sourceCodeVer},
        </if>
        <if test="expectUpgradeLogId !=null">
            expectUpgradeLogId = #{expectUpgradeLogId},
        </if>
        <if test="actualUpgradeLogId !=null">
            actualUpgradeLogId = #{actualUpgradeLogId},
        </if>
        <if test="cityCode !=null">
            cityCode=#{cityCode},
        </if>
        updateTime=now()
        where gwno=#{gwno}
    </update>

    <update id="updateStatus" parameterType="String">
        update t_gw_info set status=#{status}
        where gwno=#{gwno}
    </update>

    <update id="updateLoginTime" parameterType="String">
        update t_gw_info set loginTime=now()
        where gwno=#{gwno}
    </update>



    <select id="getByGwno" resultMap="gwCommonInfoDto">
        select * from t_gw_info where gwno=#{gwno}
        <if test="lock == true">
            for update
        </if>
    </select>

    <select id="getByGwnoAndEnable" resultMap="gwCommonInfoDto" resultType="com.cdz360.iot.model.site.po.GwInfoPo">
        select * from t_gw_info where gwno=#{gwno} and `enable`=TRUE
        <if test="lock == true">
            for update
        </if>
    </select>


    <select id="getGwList" resultMap="gwInfoMac" resultType="com.cdz360.iot.model.site.po.GwInfoPo">
        select * from t_gw_info where gwno in
        <foreach collection="gwnos" item="gwno" open="(" close=")" separator=",">
            #{gwno}
        </foreach>
        <if test="status != null">
            and status=#{status}
        </if>
    </select>

    <select id="getByMac" resultMap="gwInfoMac" resultType="com.cdz360.iot.model.site.po.GwInfoPo">
        select * from t_gw_info where mac=#{mac}
        <if test="lock == true">
            for update
        </if>
    </select>

    <update id="updateLocation" parameterType="String">
        update t_gw_info set
        cityCode=#{cityCode},
        lon=${lon},
        lat=${lat}
        where gwno=#{gwno}
    </update>

    <select id="listGw"  resultType="com.cdz360.iot.model.site.po.GwInfoPo">
        select * from t_gw_info where 1=1
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( statusList )">
            and `status` in
            <foreach collection="statusList" open="(" close=")"
                     separator="," item="status">
                #{status}
            </foreach>
        </if>
        order by id
        limit #{start},#{size}
    </select>

    <select id="getGwnosLoginTout" resultType="com.cdz360.iot.model.gw.GwTimeoutPo">
        select
            gwno,
<!--            siteId siteNum,-->
            loginTime
        from t_gw_info
        where timestampdiff(MINUTE,loginTime,now()) > #{timeout} OR `status` ='OFFLINE';
    </select>
</mapper>