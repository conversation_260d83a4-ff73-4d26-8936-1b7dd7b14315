<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.SiteGroupSiteRefRwMapper">

  <delete id="batchDelete">
    DELETE FROM t_r_site_group_site_ref where gid = #{gid}
    and siteId not in
    <foreach collection="exSiteIdList" item="item" index="index" separator="," open="(" close=")">
      #{item}
    </foreach>
  </delete>

  <delete id="deleteByGid">
    DELETE FROM t_r_site_group_site_ref where gid = #{gid}
  </delete>
  <delete id="deleteBySiteId">
    DELETE FROM t_r_site_group_site_ref where siteId = #{siteId}
  </delete>

  <insert id="batchInsert">
    INSERT ignore INTO t_r_site_group_site_ref
    (`gid`, `siteId`, `createTime`)
    VALUES
    <foreach collection="siteIdList" item="item" index="index" separator=",">
      (#{gid}, #{item}, now())
    </foreach>
  </insert>
</mapper>