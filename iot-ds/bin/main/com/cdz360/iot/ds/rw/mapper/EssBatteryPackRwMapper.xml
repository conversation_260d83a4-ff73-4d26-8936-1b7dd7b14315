<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.EssBatteryPackRwMapper">



	<resultMap id="RESULT_ESSBATTERYPACK_PO" type="com.cdz360.iot.model.ess.po.EssBatteryPackPo">

		<result column="essDno" jdbcType="VARCHAR" property="essDno" />

		<result column="stackEquipId" jdbcType="BIGINT" property="stackEquipId" />

		<result column="clusterEquipId" jdbcType="BIGINT" property="clusterEquipId" />

		<result column="lmuSn" jdbcType="BIGINT" property="lmuSn" />

		<result column="packNo" jdbcType="BIGINT" property="packNo" />

	</resultMap>



	<insert id="upsetEssBatteryPack" useGeneratedKeys="true" keyProperty="id"

			keyColumn="id" parameterType="com.cdz360.iot.model.ess.po.EssBatteryPackPo">

		insert into t_ess_battery_pack (`essDno`,

			`stackEquipId`,

			`clusterEquipId`,

			`lmuSn`,

			`packNo`)

		values (#{essDno},

			#{stackEquipId},

			#{clusterEquipId},

			#{lmuSn},

			#{packNo})

		ON DUPLICATE KEY UPDATE
		packNo = #{packNo}
	</insert>

	<insert id="batchUpsetEssBatteryPack" useGeneratedKeys="true" keyProperty="id"
			keyColumn="id" parameterType="com.cdz360.iot.model.ess.po.EssBatteryPackPo">
		insert into t_ess_battery_pack (`essDno`,
			`stackEquipId`,
			`clusterEquipId`,
			`lmuSn`,
			`packNo`)
		<foreach collection="poList" open="values" close=""
				 separator="," item="po">
			(#{po.essDno},
			#{po.stackEquipId},
			#{po.clusterEquipId},
			#{po.lmuSn},
			#{po.packNo})
		</foreach>
		ON DUPLICATE KEY UPDATE
		`packNo` = values(packNo)
	</insert>



</mapper>

