<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.TransformerRwMapper">

	<resultMap id="RESULT_TRANSFORMER_PO" type="com.cdz360.iot.model.transformer.po.TransformerPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="no" jdbcType="VARCHAR" property="no" />
		<result column="name" jdbcType="VARCHAR" property="name" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="capacity" jdbcType="DECIMAL" property="capacity" />
		<result column="orderlyCaps" property="orderlyCaps" typeHandler="com.cdz360.iot.ds.ListTypeHandler"/>
		<result column="assignableCap" jdbcType="DECIMAL" property="assignableCap" />
		<result column="inputVoltage" jdbcType="DECIMAL" property="inputVoltage" />
		<result column="outputVoltage" jdbcType="DECIMAL" property="outputVoltage" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_TRANSFORMER_PO">	
		select * from t_transformer where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertTransformer" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.iot.model.transformer.po.TransformerPo">
		insert into t_transformer (`no`,
			`name`,
			`siteId`,
			`capacity`,
			`orderlyCaps`,
			`assignableCap`,
			`inputVoltage`,
			`outputVoltage`,
			`createTime`,
			`updateTime`)
		values (#{no},
			#{name},
			#{siteId},
			#{capacity},
			#{orderlyCaps, typeHandler=com.cdz360.iot.ds.ListTypeHandler},
			#{assignableCap},
			#{inputVoltage},
			#{outputVoltage},
			now(),
			now())
	</insert>

	<update id="updateTransformer" parameterType="com.cdz360.iot.model.transformer.po.TransformerPo">
		update t_transformer set
		<if test="no != null">
			no = #{no},
		</if>
		<if test="name != null">
			name = #{name},
		</if>
		<if test="siteId != null">
			siteId = #{siteId},
		</if>
		<if test="capacity != null">
			capacity = #{capacity},
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(orderlyCaps)">
			orderlyCaps = #{orderlyCaps, typeHandler=com.cdz360.iot.ds.ListTypeHandler},
		</if>
<!--		<if test="assignableCap != null">-->
			assignableCap = #{assignableCap},
<!--		</if>-->
		<if test="inputVoltage != null">
			inputVoltage = #{inputVoltage},
		</if>
		<if test="outputVoltage != null">
			outputVoltage = #{outputVoltage},
		</if>
		updateTime = now()
		where id = #{id}
	</update>

	<delete id="delete">
		delete from t_transformer where id=#{id}
	</delete>

</mapper>
