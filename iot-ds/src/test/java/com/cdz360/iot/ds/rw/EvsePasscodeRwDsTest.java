package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.IotDsTestBase;
import com.cdz360.iot.model.evse.po.EvsePasscodePo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * * @ProjectName iotServer
 *
 * <AUTHOR>
 * @CreateDate 2019/11/13 17:00
 */
class EvsePasscodeRwDsTest extends IotDsTestBase {
    private static final String EVES_NO = "010203040506";
    @Autowired
    private EvsePasscodeRwDs evsePasscodeRwDs;

    @Test
    void updateEvsePasscode() {
        Long ver = evsePasscodeRwDs.updateEvsePasscode(EVES_NO, "0123456789ABCDEF0123456789AAAAAA");
        Assertions.assertTrue(ver != null);
    }

    @Test
    void getEvsePasscode() {
        evsePasscodeRwDs.updateEvsePasscode(EVES_NO, "0123456789ABCDEF0123456789FFFFFF");
        EvsePasscodePo evsePasscode = evsePasscodeRwDs.getEvsePasscode(EVES_NO, null, false);
        Assertions.assertNotNull(evsePasscode);
    }
}