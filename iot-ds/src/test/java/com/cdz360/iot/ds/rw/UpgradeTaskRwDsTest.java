package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.IotDsTestBase;
import com.cdz360.iot.ds.ro.UpgradeTaskQueryDs;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.Date;

public class UpgradeTaskRwDsTest extends IotDsTestBase {
    @Autowired
    private UpgradeTaskRwDs upgradeTaskRwDs;

    @Autowired
    private UpgradeTaskQueryDs upgradeTaskQueryDs;

    @Test
    public void test_insertOrUpdate() {
        UpgradeTaskVo upgradeTaskVo = new UpgradeTaskVo();
        upgradeTaskVo.setEvseCount(100);
        upgradeTaskVo.setOpName("unitest");
        upgradeTaskVo.setSiteId("999");
        upgradeTaskVo.setOpId(999L);
        upgradeTaskVo.setBundleId(1000L);
        upgradeTaskVo.setBundleName("bundle name");
        upgradeTaskVo.setCreateTime(new Date());
        Assert.isTrue(upgradeTaskRwDs.insert(upgradeTaskVo) == 1, "insert failed");

        Assert.isTrue(
                upgradeTaskQueryDs.selectById(upgradeTaskVo.getId()).getEvseCount() == upgradeTaskVo.getEvseCount(),
                "selectById failed");

        Assert.isTrue(upgradeTaskRwDs.insert(upgradeTaskVo) == 1, "insert failed");
        Assert.isTrue(upgradeTaskQueryDs.select(upgradeTaskVo.getSiteId(), null, null, 0, 100).size() == 2,
                "select failed");

        Assert.isTrue(upgradeTaskQueryDs.select(upgradeTaskVo.getSiteId(), upgradeTaskVo.getId(), null, 0, 100).size() == 1,
                "select failed");

        Assert.isTrue(upgradeTaskQueryDs.selectCount(upgradeTaskVo.getSiteId(), null, null) == 2, "selectCount failed");
        Assert.isTrue(upgradeTaskQueryDs.selectCount(upgradeTaskVo.getSiteId(), upgradeTaskVo.getId(), null) == 1, "selectCount failed");
    }
}