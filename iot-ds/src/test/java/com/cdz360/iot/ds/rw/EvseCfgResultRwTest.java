package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.IotDsTestBase;
import com.cdz360.iot.model.evse.po.EvseCfgResultPo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class EvseCfgResultRwTest extends IotDsTestBase {
    private static final String EVES_NO = "010203040506";
    @Autowired
    private EvseCfgResultRwDs evseCfgResultRwDs;

    @Test
    void test_insertOrUpdate() {
        EvseCfgResultPo o = new EvseCfgResultPo();
        o.setEvseNo(EVES_NO);
        this.evseCfgResultRwDs.insertOrUpdate(o);
        EvseCfgResultPo result = this.evseCfgResultRwDs.getByEvseNo(o.getEvseNo(), false);
        log.info("result = {}", result);
        this.evseCfgResultRwDs.insertOrUpdate(o);
        result = this.evseCfgResultRwDs.getByEvseNo(o.getEvseNo(), true);
        log.info("result = {}", result);
    }
}
