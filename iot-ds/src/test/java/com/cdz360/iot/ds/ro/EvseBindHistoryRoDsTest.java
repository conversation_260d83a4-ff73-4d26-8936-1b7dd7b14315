package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.IotDsTestBase;
import com.cdz360.iot.model.evse.EvseBindHistoryPo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Calendar;
import java.util.Date;

/**
 * @Classname EvseBindHistoryRoDsTest
 * @Description
 * @Date 3/28/2020 2:35 PM
 * @Created by Rafael
 */
@Slf4j
public class EvseBindHistoryRoDsTest extends IotDsTestBase {
    @Autowired
    private EvseBindHistoryRoDs evseBindHistoryRoDs;

    @Test
    public void selectByPrimaryKey() {
        EvseBindHistoryPo res = evseBindHistoryRoDs.selectPrevious(new Date(), "evseNo", "siteId");
        log.info("{}", res);
        Assertions.assertTrue(res != null);
    }

    @Test
    public void selectActiveTimeTest() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(2020, Calendar.MARCH, 17, 14, 0, 0);//2020-03-17 14:00:00
        calendar.clear(Calendar.MILLISECOND);
        Date startTime = calendar.getTime();
        calendar.add(Calendar.HOUR, 4);
        Date endTime = calendar.getTime();
        int sum = evseBindHistoryRoDs.selectActiveTime(startTime, endTime, "evseNo", "siteId");
        Assertions.assertTrue(sum == 7200);

        calendar.set(Calendar.HOUR, 11);
        calendar.set(Calendar.MINUTE, 15);
        calendar.set(Calendar.AM_PM, Calendar.PM);
        startTime = calendar.getTime();
        calendar.add(Calendar.MINUTE, 30);
        endTime = calendar.getTime();
        sum = evseBindHistoryRoDs.selectActiveTime(startTime, endTime, "evseNo", "siteId");
        Assertions.assertTrue(sum == 900);

    }
}