package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.IotDsTestBase;
import com.cdz360.iot.ds.ro.UpgradeTaskDetailQueryDs;
import com.cdz360.iot.model.evse.upgrade.UpdateTaskStatusEnum;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskDetailVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.List;

public class UpgradeTaskDetailRwDsTest extends IotDsTestBase {
    @Autowired
    private UpgradeTaskDetailRwDs writer;

    @Autowired
    private UpgradeTaskDetailQueryDs reader;

    @Test
    public void insertAndSelect() {
        UpgradeTaskDetailVo upgradeTaskDetailVo = new UpgradeTaskDetailVo();
        upgradeTaskDetailVo.setEvseId("2");
        upgradeTaskDetailVo.setFailReason("you r sucker");
        upgradeTaskDetailVo.setPc01Ver("pco1ver");
        upgradeTaskDetailVo.setPc02Ver("pc02Ver");
        upgradeTaskDetailVo.setPc03Ver("pc03Ver");
        upgradeTaskDetailVo.setStatus(UpdateTaskStatusEnum.UPDATING);
        upgradeTaskDetailVo.setTaskId(9999L);
        Assert.isTrue(writer.insert(upgradeTaskDetailVo) == 1, "insert failed");

        Assert.isTrue(reader.select(upgradeTaskDetailVo.getTaskId(), null, null).size() == 1, "select failed");
        List<UpdateTaskStatusEnum> statusEnumList = List.of(UpdateTaskStatusEnum.FAIL);
        Assert.isTrue(reader.select(upgradeTaskDetailVo.getTaskId(), null, statusEnumList).size() == 0, "select failed");
        upgradeTaskDetailVo.setTaskId(777L);
        Assert.isTrue(writer.update(upgradeTaskDetailVo) == 1, "update failed");

        Assert.isTrue(reader.select(upgradeTaskDetailVo.getTaskId(), null, null).size() == 1, "select failed");
        Assert.isTrue(reader.select(upgradeTaskDetailVo.getTaskId(), null, null)
                .get(0)
                .getTaskId().equals(upgradeTaskDetailVo.getTaskId()), "select failed");

        Assert.isTrue(writer.insert(upgradeTaskDetailVo) == 1, "insert failed");
        Assert.isTrue(reader.select(upgradeTaskDetailVo.getTaskId(), null, null).size() == 2, "select failed");

        //for sort
        // FIXME CANNOT RUN IN H2
//        UpgradeTaskDetailVo upgradeTaskDetailVo1 = new UpgradeTaskDetailVo();
//        upgradeTaskDetailVo1.setEvseId("1");
//        upgradeTaskDetailVo1.setFailReason("you r sucker");
//        upgradeTaskDetailVo1.setPc01Ver("pco1ver");
//        upgradeTaskDetailVo1.setPc02Ver("pc02Ver");
//        upgradeTaskDetailVo1.setPc03Ver("pc03Ver");
//        upgradeTaskDetailVo1.setStatus(UpdateTaskStatusEnum.UPDATED);
//        upgradeTaskDetailVo1.setTaskId(9999L);
//        Assert.isTrue(writer.insert(upgradeTaskDetailVo) == 1, "insert failed");
//
//        UpgradeTaskDetailVo primero =
//        reader.select(upgradeTaskDetailVo.getTaskId(), null, null, UpgradeTaskDetailSortEnum.EVSE_NO).get(0);
//        UpgradeTaskDetailVo segundo =
//        reader.select(upgradeTaskDetailVo.getTaskId(), null, null, UpgradeTaskDetailSortEnum.UPDATE_STATUS).get(1);
//
//        Assert.isTrue(primero.getId().equals(segundo.getId()), "sort failed");

    }
}