package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.iot.model.evse.ListPlugParam;
import com.cdz360.iot.model.evse.SiteAndPlugBiVo;
import com.cdz360.iot.model.evse.dto.SiteDeviceBiDto;
import com.cdz360.iot.model.evse.po.EvsePlugRecordPo;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.model.evse.vo.PlugStatusBiVo;
import com.cdz360.iot.model.evse.vo.PlugSupplyBiVo;
import com.cdz360.iot.model.site.param.SiteAndPlugBiParam;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PlugRoMapper {


    PlugPo getPlug(@Param("evseNo") String evseNo, @Param("plugIdx") int plugIdx);

    PlugVo getPlugByPlugNo(@Param("plugNo") String plugNo);

    List<PlugVo> getPlugList(ListPlugParam param);

    List<String> getPlugNos(ListPlugParam param);

    List<EvsePlugRecordPo> getPlugRecordInfo(@Param("siteId") String siteId);

    List<EvsePlugRecordPo> getUpgradeCleaningPlugInfo();

    List<PlugSupplyBiVo> getPlugSupplyBi(@Param("commIdChain") String commIdChain);

    List<PlugStatusBiVo> getPlugStatusBi(@Param("provinceCode") String provinceCode,
                                         @Param("cityCode") String cityCode,
                                         @Param("siteId") String siteId,
                                         @Param("commIdChain") String commIdChain);

    List<SiteDeviceBiDto> getSitePlugBiList(@Param("siteIdList") List<String> siteIdList);

    SiteAndPlugBiVo getSiteAndPlugStatus(SiteAndPlugBiParam param);

    List<String> getPlugNoList(@Param("evseNo") String evseNo);

    List<String> getPlugNoListByEvseNoList(@Param("evseNoList") List<String> evseNoList);

    List<PlugPo> getPlugPoList(@Param("evseNo") String evseNo);
}
