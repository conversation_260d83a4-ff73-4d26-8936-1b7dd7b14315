package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.ess.po.UserDeviceRefPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface UserDeviceRefRwMapper {

    UserDeviceRefPo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int batchInsert(List<UserDeviceRefPo> poList);

    int batchUpset(@Param("poList") List<UserDeviceRefPo> poList);
    
    int upsetUserDeviceRef(UserDeviceRefPo po);

    int insertUserDeviceRef(UserDeviceRefPo userDeviceRefPo);

    int updateUserDeviceRef(UserDeviceRefPo userDeviceRefPo);

    int resetNewMaster(@Param("dno") String dno);
}

