package com.cdz360.iot.ds.rw.mapper;


import com.cdz360.iot.model.park.po.ParkingLockEventLogPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface ParkingLockEventLogRwMapper {

    ParkingLockEventLogPo getById(@Param("id") Long id, @Param("lock") boolean lock);


    int insertParkingLockEventLog(ParkingLockEventLogPo parkingLockEventLogPo);


    int updateParkingLockEventLog(ParkingLockEventLogPo parkingLockEventLogPo);


}

