<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.PlugRoMapper">

  <resultMap id="PLUGVO2_MAP" type="com.cdz360.iot.model.evse.vo.PlugVo2">
    <result column="gwno" jdbcType="VARCHAR" property="gwno"/>
    <result column="evseNo" jdbcType="VARCHAR" property="evseNo"/>
    <result column="evseName" jdbcType="VARCHAR" property="evseName"/>
    <result column="bizStatus" property="bizStatus"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="plugNo" jdbcType="VARCHAR" property="plugNo"/>
    <result column="siteCommId" jdbcType="BIGINT" property="siteCommId"/>
    <result column="siteId" jdbcType="BIGINT" property="siteId"/>
    <result column="siteName" jdbcType="VARCHAR" property="siteName"/>
    <result column="idx" jdbcType="INTEGER" property="idx"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="status" jdbcType="VARCHAR" property="status"/>
    <result column="supply" jdbcType="VARCHAR" property="supply"/>
    <result column="orderNo" jdbcType="VARCHAR" property="orderNo"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="minVoltage" jdbcType="DECIMAL" property="minVoltage"/>
    <result column="maxVoltage" jdbcType="DECIMAL" property="maxVoltage"/>
    <result column="minCurrent" jdbcType="DECIMAL" property="minCurrent"/>
    <result column="maxCurrent" jdbcType="DECIMAL" property="maxCurrent"/>
    <result column="power" jdbcType="DECIMAL" property="power"/>
    <result column="parkNo" jdbcType="VARCHAR" property="parkNo"/>
    <result column="flags" property="flags"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getPlugByPlugNo"
    resultType="com.cdz360.base.model.iot.vo.PlugVo">
    SELECT
    e.gwno,e.evseId as evseNo,
    e.name as evseName,
    CONCAT(e.evseId, LPAD(p.plugId, 2, 0)) as plugNo,
    s.commId as siteCommId,s.dzId as siteId,
    s.`name` as siteName,p.plugId as idx,p.`name`,
    p.plugStatus as `status`,
    e.supply,p.orderNo,p.updateTime,p.voltageMin as minVoltage,
    p.voltageMax as maxVoltage,
    p.currentMin as minCurrent,
    p.currentMax as maxCurrent
    FROM
    t_plug p
    left JOIN t_evse e ON p.evseId = e.evseId
    left JOIN t_site s ON e.siteId = s.dzId
    <where>
      CONCAT(e.evseId, LPAD(p.plugId, 2, 0)) = #{plugNo}
    </where>
  </select>

  <select id="getPlug" resultType="com.cdz360.iot.model.evse.po.PlugPo">
    select * from t_plug where evseId = #{evseNo} and plugId = #{plugIdx}
  </select>


  <select id="getPlugList" parameterType="com.cdz360.iot.model.evse.ListPlugParam"
    resultMap="PLUGVO2_MAP">
    SELECT
    <!--    e.gwno,e.evseId as evseNo,-->
    <!--    e.name as evseName,-->
    <!--    e.bizStatus as bizStatus,-->
    <!--    CONCAT(e.evseId, LPAD(p.plugId, 2, 0)) as plugNo,-->
    <!--    s.commId as siteCommId,s.dzId as siteId,-->
    <!--    s.`name` as siteName,p.plugId as idx,p.`name`,-->
    <!--    p.plugStatus as `status`,-->
    <!--    e.supply,p.orderNo,p.updateTime,p.voltageMin as minVoltage,-->
    <!--    p.voltageMax as maxVoltage,-->
    <!--    p.currentMin as minCurrent,-->
    <!--    p.currentMax as maxCurrent,-->
    <!--    p.power as power,-->
    <!--    p.parkNo as parkNo,-->
    <!--    em.flags-->
    any_value(e.gwno) gwno,
    any_value(e.evseId) evseNo,
    any_value(e.`name`) evseName,
    any_value(e.bizStatus) AS bizStatus,
    CONCAT(e.evseId,LPAD( p.plugId, 2, 0 )) AS plugNo,
    any_value(s.commId) AS siteCommId,
    any_value(s.dzId) AS siteId,
    any_value(s.`name`) AS siteName,
    any_value(p.plugId) AS idx,
    any_value(p.`name`) name,
    any_value(p.plugStatus) AS `status`,
    any_value(e.supply) supply,
    any_value(p.orderNo) orderNo,
    any_value(p.updateTime) updateTime,
    any_value(p.voltageMin) AS minVoltage,
    any_value(p.voltageMax) AS maxVoltage,
    any_value(p.currentMin) AS minCurrent,
    any_value(p.currentMax) AS maxCurrent,
    any_value(p.power) AS power,
    any_value(p.parkNo) AS parkNo,
    any_value(p.updateTime) AS updateTime,
    any_value(em.flags) flag
    FROM
    t_plug p
    left JOIN t_evse e ON p.evseId = e.evseId
    left JOIN t_site s ON e.siteId = s.dzId
    left join t_evse_model em on e.modelId = em.id
    LEFT JOIN t_r_site_group_site_ref gsr ON gsr.siteId = s.dzId
    left join t_r_commercial trc on trc.id = s.commId
    <where>
      <!--            1=1-->
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
        e.evseId IN
        <foreach collection="evseNoList" index="index" item="item"
          open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( statusList )">
        and s.status IN
        <foreach collection="statusList" index="index" item="item"
                 open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( plugNoList )">
        and CONCAT(e.evseId, LPAD(p.plugId, 2, 0)) IN
        <foreach collection="plugNoList" index="index" item="plugNo"
          open="(" close=")" separator=",">
          #{plugNo}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( sk )">
        AND CONCAT(e.evseId, LPAD(p.plugId, 2, 0)) like CONCAT('%', #{sk}, '%')
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">
        AND s.dzId IN
        <foreach collection="siteIdList" index="index" item="item"
          open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <choose>
        <when test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( gidList )">
          AND gsr.gid IN
          <foreach collection="gidList" index="index" item="item"
            open="(" close=")" separator=",">
            #{item}
          </foreach>
        </when>
        <when test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
          and trc.idChain like CONCAT(#{commIdChain}, '%')
        </when>
      </choose>

      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( bizStatusList )">
        <foreach collection="bizStatusList" index="index" item="item"
          open="AND e.`bizStatus` IN (" close=")" separator=",">
          #{item.code}
        </foreach>
      </if>
      <if test="supplyType != null">
        and e.supply = #{supplyType}
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( plugStatusList )">
        AND p.`plugStatus` IN
        <foreach collection="plugStatusList" index="index" item="item"
          open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      GROUP BY plugNo
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          ${sort.columnsString} ${sort.order}
        </foreach>
      </if>
    </where>
    <if test="start != null and size != null">
      limit #{start},#{size}
    </if>
  </select>

  <select id="getPlugNos" parameterType="com.cdz360.iot.model.evse.ListPlugParam"
    resultType="java.lang.String">
    SELECT
    CONCAT(e.evseId, LPAD(p.plugId, 2, 0)) as plugNo
    FROM
    t_plug p
    left JOIN t_evse e ON p.evseId = e.evseId
    left JOIN t_site s ON e.siteId = s.dzId
    <where>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
        e.evseId IN
        <foreach collection="evseNoList" index="index" item="item"
          open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( bizStatusList )">
        <foreach collection="bizStatusList" index="index" item="item"
          open="AND e.`bizStatus` IN (" close=")" separator=",">
          #{item.code}
        </foreach>
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( plugNoList )">
        CONCAT(e.evseId, LPAD(p.plugId, 2, 0)) IN
        <foreach collection="plugNoList" index="index" item="plugNo"
          open="(" close=")" separator=",">
          #{plugNo}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( sk )">
        AND CONCAT(e.evseId, LPAD(p.plugId, 2, 0)) like CONCAT('%', #{sk}, '%')
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">
        AND s.dzId IN
        <foreach collection="siteIdList" index="index" item="item"
          open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="supplyType != null">
        and e.supply = #{supplyType}
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( plugStatusList )">
        AND p.`plugStatus` IN
        <foreach collection="plugStatusList" index="index" item="item"
          open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          ${sort.columnsString} ${sort.order}
        </foreach>
      </if>
    </where>
    <if test="start != null and size != null">
      limit #{start},#{size}
    </if>
  </select>

  <select id="getPlugRecordInfo" resultType="com.cdz360.iot.model.evse.po.EvsePlugRecordPo">
    select
    evse.supply,
    count(*) as plugTotalNum
    from
    t_plug plug
    inner join t_evse evse on
    plug.`evseId` = evse.`evseId`
    where
    evse.`siteId` = #{siteId}
    and evse.`evseStatus` != 'OFF'
    and evse.`bizStatus` = 1
    group by
    evse.supply
  </select>

  <select id="getUpgradeCleaningPlugInfo"
    resultType="com.cdz360.iot.model.evse.po.EvsePlugRecordPo">
    select
    evse.`siteId`,
    evse.supply,
    count(*) as plugTotalNum
    from
    t_plug plug
    inner join t_evse evse on
    plug.`evseId` = evse.`evseId`
    where
    evse.`siteId` is not null
    and evse.`evseStatus` != 'OFF'
    group by
    evse.`siteId`,
    evse.supply
  </select>

  <select id="getPlugSupplyBi" resultType="com.cdz360.iot.model.evse.vo.PlugSupplyBiVo">
    select e2.supply as supply,
    e2.evseNum,
    p2.plugNum
    from (
    select e.supply , count(e.evseId) as evseNum from t_evse e
    left join t_site s on e.siteId = s.dzId
    left join t_r_commercial comm on comm.id = s.commId
    where e.evseStatus in ("IDLE", "CONNECT", "BUSY", "ERROR", "OFFLINE")
    and e.bizStatus = 1
    and e.siteId is not null
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.idChain like CONCAT(#{commIdChain}, '%')
    </if>
    group by e.supply
    ) as e2
    left join (
    select e.supply, count(p.id) as plugNum from t_plug p
    left join t_evse e on p.evseId = e.evseId
    left join t_site s on e.siteId = s.dzId
    left join t_r_commercial comm on comm.id = s.commId
    where e.evseStatus in ("IDLE", "CONNECT", "BUSY", "ERROR", "OFFLINE")
    and e.bizStatus = 1
    and e.siteId is not null
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.idChain like CONCAT(#{commIdChain}, '%')
    </if>
    group by e.supply
    ) as p2 on e2.supply = p2.supply
  </select>


  <select id="getPlugStatusBi" resultType="com.cdz360.iot.model.evse.vo.PlugStatusBiVo">
    select e2.evseStatus as evseStatus,
    p2.plugStatus as plugStatus,
    e2.evseNum,
    p2.plugNum
    from (
    select e.evseStatus , count(e.evseId) as evseNum from t_evse e
    left join t_site s on e.siteId = s.dzId
    left join t_r_commercial comm on comm.id = s.commId
    where e.evseStatus in ("IDLE", "CONNECT", "BUSY", "ERROR", "OFFLINE")
    and e.bizStatus = 1
    and e.siteId is not null
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( provinceCode )">
      and s.provinceCode = #{provinceCode}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( cityCode )">
      and s.cityCode = #{cityCode}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and s.dzId = #{siteId}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.idChain like CONCAT(#{commIdChain}, '%')
    </if>
    group by e.evseStatus
    ) as e2
    right join (
    select p.plugStatus, count(p.id) as plugNum from t_plug p
    left join t_evse e on p.evseId = e.evseId
    left join t_site s on e.siteId = s.dzId
    left join t_r_commercial comm on comm.id = s.commId
    where e.evseStatus in ("IDLE", "CONNECT", "BUSY", "ERROR", "OFFLINE")
    and e.bizStatus = 1
    and e.siteId is not null
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( provinceCode )">
      and s.provinceCode = #{provinceCode}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( cityCode )">
      and s.cityCode = #{cityCode}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and s.dzId = #{siteId}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.idChain like CONCAT(#{commIdChain}, '%')
    </if>
    group by p.plugStatus
    ) as p2 on e2.evseStatus = p2.plugStatus
  </select>


  <select id="getSitePlugBiList" resultType="com.cdz360.iot.model.evse.dto.SiteDeviceBiDto">
    select * from (
    select e.siteId, count(p.id) as plugNum
    from t_plug p
    left join t_evse e on p.evseId = e.evseId
    <where>
      e.siteId in
      <foreach collection="siteIdList" index="index" item="siteId"
        open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </where>
    group by e.siteId
    ) as rec
    order by rec.plugNum desc
  </select>

  <select id="getSiteAndPlugStatus" resultType="com.cdz360.iot.model.evse.SiteAndPlugBiVo">
    select
    count(0) as plugCount,
    count(case when e.supply = 'AC' then 1 end) as acPlugCount,
    count(case when e.supply = 'DC' then 1 end) as dcPlugCount,
    count(case when p.plugStatus='BUSY' or p.plugStatus='JOIN' then 1 end) as plugChargingCount,
    count(case when p.plugStatus='RECHARGE_END' then 1 end) as plugChargeEndCount,
    count(case when p.plugStatus='CONNECT' then 1 end) as plugConnectedCount,
    count(case when p.plugStatus='IDLE' then 1 end) as plugIdleCount,
    count(case when p.plugStatus='OFFLINE' then 1 end) as plugOfflineCount,
    count(case when p.plugStatus='ERROR' then 1 end) as plugErrorCount
    from t_plug p
    left join t_evse e on e.evseId=p.evseId
    left join t_site s on s.dzId=e.siteId
    left join t_r_commercial c on c.id=s.commId
    where
    c.`enable` = 1
    and e.bizStatus = 1
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteStatusList )">
      and s.status in
      <foreach collection="siteStatusList" item="status"
        open="(" close=")" separator=",">
        #{status}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( bizTypeList )">
      and s.bizType in
      <foreach collection="bizTypeList" item="bizType"
        open="(" close=")" separator=",">
        #{bizType}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( idChain )">
      and c.idChain like CONCAT(#{idChain}, '%')
    </if>
    <if test="commId != null">
      and c.id = #{commId}
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gids )">
      and s.dzId in (
      select siteId from t_r_site_group_site_ref as sg
      where sg.gid in
      <foreach item="gid" index="index" collection="gids"
        open=" (" separator="," close=")">
        #{gid}
      </foreach>
      )
    </if>
  </select>


  <select id="getPlugNoList" resultType="java.lang.String">
    select
    CONCAT(evseId, LPAD(plugId, 2, 0)) as plugNo
    from t_plug
    where evseId = #{evseNo}
  </select>

  <select id="getPlugNoListByEvseNoList" resultType="java.lang.String">
    select
    CONCAT(evseId, LPAD(plugId, 2, 0)) as plugNo
    from t_plug
    <where>
      evseId in
      <foreach collection="evseNoList" index="index" item="evseId"
        open="(" close=")" separator=",">
        #{evseId}
      </foreach>
    </where>
  </select>

  <select id="getPlugPoList" resultType="com.cdz360.iot.model.evse.po.PlugPo">
    select
    plugId,
    name
    from
    t_plug
    where
    evseId = #{evseNo}
  </select>

</mapper>