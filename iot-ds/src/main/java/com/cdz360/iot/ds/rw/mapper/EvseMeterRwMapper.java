package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.meter.po.DeviceMeterPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EvseMeterRwMapper {
	DeviceMeterPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertEvseMeter(DeviceMeterPo deviceMeterPo);

	int updateEvseMeter(DeviceMeterPo deviceMeterPo);

    int batchInsert(List<DeviceMeterPo> deviceMeterPoList);

    int deleteEvseMeter(Long meterId);


}
