package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.ParkMsgRoMapper;
import com.cdz360.iot.model.park.po.ParkMsgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ParkMsgRoDs {

	@Autowired
	private ParkMsgRoMapper parkMsgRoMapper;

	public ParkMsgPo getById(Integer id) {
		return this.parkMsgRoMapper.getById(id);
	}
}
