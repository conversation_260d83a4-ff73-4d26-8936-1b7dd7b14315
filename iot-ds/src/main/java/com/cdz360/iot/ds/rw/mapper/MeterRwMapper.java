package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.meter.po.MeterPo;
import com.cdz360.iot.model.pv.po.GtiPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.Date;

@Mapper
public interface MeterRwMapper {
	MeterPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertMeter(MeterPo meterPo);

	int batchInsertMeter(@Param("poList") List<MeterPo> poList);

	int updateMeter(MeterPo meterPo);

	int refreshMeterStatus(@Param("ttl") int ttl);


}
