package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.SiteRwMapper;
import com.cdz360.iot.model.site.po.SitePo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * t_site表的读写服务类
 */
@Service
public class SiteRwDs {

    @Autowired
    private SiteRwMapper siteRwMapper;

    public int insertOrUpdate(SitePo site) {
        return this.siteRwMapper.insertOrUpdate(site);
    }

    public int insertByCondition(SitePo site) {
        return this.siteRwMapper.insertByCondition(site);
    }

    public int update(SitePo site) {
        return this.siteRwMapper.update(site);
    }


}
