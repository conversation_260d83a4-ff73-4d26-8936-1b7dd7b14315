<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.SimRwMapper">

    <insert id="addSimList" parameterType="com.cdz360.iot.model.sim.po.SimPo">
        INSERT INTO t_sim
            (iccid, msisdn, vendor, imsi, imei, slotStatus,
            status, activatedDate, `usage`, createTime)
        VALUES
        <foreach collection="poList" item="e" index="index" separator=",">
            (#{e.iccid}, #{e.msisdn}, #{e.vendor}, #{e.imsi}, #{e.imei}, #{e.slotStatus},
            #{e.status}, #{e.activatedDate}, #{e.usage}, now())
        </foreach>
    </insert>

    <update id="updateSimListByIccid" parameterType="com.cdz360.iot.model.sim.po.SimPo">
        <foreach collection="poList" item="e" open="" close="" separator=";">
            UPDATE t_sim
            <set>
                <if test="e.msisdn != null">
                    msisdn = #{e.msisdn},
                </if>
                <if test="e.vendor != null">
                    vendor = #{e.vendor},
                </if>
                <if test="e.imsi != null">
                    imsi = #{e.imsi},
                </if>
                <if test="e.imei != null">
                    imei = #{e.imei},
                </if>
                <if test="e.slotStatus != null">
                    slotStatus = #{e.slotStatus},
                </if>
                <if test="e.status != null">
                    status = #{e.status},
                </if>
                <if test="e.activatedDate != null">
                    activatedDate = #{e.activatedDate},
                </if>
                <if test="e.usage != null">
                    `usage` = #{e.usage},
                </if>
                <if test="e.siteId != null">
                    siteId = #{e.siteId},
                </if>
                <if test="e.deviceType != null">
                    deviceType = #{e.deviceType},
                </if>
                <if test="e.remark != null">
                    remark = #{e.remark},
                </if>
                updateTime = now()
            </set>
            WHERE iccid = #{e.iccid}
        </foreach>
    </update>

    <update id="updateSimListByMsisdn" parameterType="com.cdz360.iot.model.sim.po.SimPo">
        <foreach collection="poList" item="e" open="" close="" separator=";">
            UPDATE t_sim
            <set>
                <if test="e.iccid != null">
                    iccid = #{e.iccid},
                </if>
                <if test="e.vendor != null">
                    vendor = #{e.vendor},
                </if>
                <if test="e.imsi != null">
                    imsi = #{e.imsi},
                </if>
                <if test="e.imei != null">
                    imei = #{e.imei},
                </if>
                <if test="e.slotStatus != null">
                    slotStatus = #{e.slotStatus},
                </if>
                <if test="e.status != null">
                    status = #{e.status},
                </if>
                <if test="e.activatedDate != null">
                    activatedDate = #{e.activatedDate},
                </if>
                <if test="e.usage != null">
                    `usage` = #{e.usage},
                </if>
                <if test="e.siteId != null">
                    siteId = #{e.siteId},
                </if>
                <if test="e.deviceType != null">
                    deviceType = #{e.deviceType},
                </if>
                <if test="e.remark != null">
                    remark = #{e.remark},
                </if>
                updateTime = now()
            </set>
            WHERE msisdn = #{e.msisdn}
        </foreach>
    </update>

    <update id="updateByIccid" parameterType="com.cdz360.iot.model.sim.po.SimPo">
        UPDATE t_sim
        <set>
            <if test="msisdn != null">
                msisdn = #{msisdn},
            </if>
            <if test="vendor != null">
                vendor = #{vendor},
            </if>
            <if test="imsi != null">
                imsi = #{imsi},
            </if>
            <if test="imei != null">
                imei = #{imei},
            </if>
            <if test="slotStatus != null">
                slotStatus = #{slotStatus},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="activatedDate != null">
                activatedDate = #{activatedDate},
            </if>
            <if test="usage != null">
                `usage` = #{usage},
            </if>
            <if test="siteId != null">
                siteId = #{siteId},
            </if>
            <if test="deviceType != null">
                deviceType = #{deviceType},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            updateTime = now()
        </set>
        WHERE iccid = #{iccid}
    </update>

    <update id="resetDeviceTypeById">
        update
            t_sim
        set
            deviceType = #{deviceType}
        where
            id = #{simId}
    </update>

    <update id="resetRemark">
        update
            t_sim
        set
            remark = null
        where
            id = #{simId}
    </update>

</mapper>