package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.SimRwMapper;
import com.cdz360.iot.model.sim.po.SimPo;
import com.cdz360.iot.model.sim.type.NetworkVendor;
import com.cdz360.iot.model.sim.type.SimDeviceType;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SimRwDs {

    @Autowired
    private SimRwMapper simRwMapper;

    public boolean batchInsert(@NonNull List<SimPo> list) {
        return this.simRwMapper.addSimList(list) > 0;
    }

    public boolean batchUpdate(@NonNull List<SimPo> list, NetworkVendor vendor) {
        boolean boo = false;
        if (NetworkVendor.CU == vendor) {
            boo = this.simRwMapper.updateSimListByIccid(list) > 0;
        } else if (NetworkVendor.CM_CT == vendor || NetworkVendor.CM_PB == vendor) {
            boo = this.simRwMapper.updateSimListByMsisdn(list) > 0;
        }
        return boo;
    }

    public boolean updateByIccid(@NonNull SimPo po) {
        return this.simRwMapper.updateByIccid(po) > 0;
    }

    public boolean resetDeviceTypeById(Long simId, SimDeviceType deviceType) {
        return this.simRwMapper.resetDeviceTypeById(simId, deviceType) > 0;
    }

    public boolean resetRemark(Long simId) {
        return this.simRwMapper.resetRemark(simId) > 0;
    }

}
