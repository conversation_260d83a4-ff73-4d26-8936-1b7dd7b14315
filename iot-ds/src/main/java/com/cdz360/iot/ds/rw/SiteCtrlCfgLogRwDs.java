package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.SiteCtrlCfgLogRwMapper;
import com.cdz360.iot.model.order.type.SiteCtrlCfgStatusType;
import com.cdz360.iot.model.site.po.SiteCtrlCfgLogPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname SiteCtrlCfgLogRwDs
 * @Description
 * @Date 4/23/2020 2:05 PM
 * @Created by Rafael
 */
@Service
public class SiteCtrlCfgLogRwDs {
    @Autowired
    private SiteCtrlCfgLogRwMapper siteCtrlCfgLogRwMapper;

    public int insertOrUpdate(SiteCtrlCfgLogPo param) {
        return siteCtrlCfgLogRwMapper.insertOrUpdate(param);
    }

    public int checkTimeout(Integer bufferTime) {
        return siteCtrlCfgLogRwMapper.checkTimeout(bufferTime, SiteCtrlCfgStatusType.FAIL);
    }
}