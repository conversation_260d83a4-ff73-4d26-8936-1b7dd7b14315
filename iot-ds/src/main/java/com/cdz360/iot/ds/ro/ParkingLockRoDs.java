package com.cdz360.iot.ds.ro;


import com.cdz360.iot.ds.ro.mapper.ParkingLockRoMapper;
import com.cdz360.iot.model.park.param.ListParkingLockParam;
import com.cdz360.iot.model.park.po.ParkingLockPo;
import com.cdz360.iot.model.park.type.ParkingLockPartner;
import com.cdz360.iot.model.park.vo.ParkingLockVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class ParkingLockRoDs {


    @Autowired

    private ParkingLockRoMapper parkingLockRoMapper;


    public ParkingLockPo getById(Long id) {

        return this.parkingLockRoMapper.getById(id);

    }

    public ParkingLockPo getByUniqueKey(ParkingLockPartner partner, String serialNumber) {
        return this.parkingLockRoMapper.getByUniqueKey(partner, serialNumber);
    }

    public ParkingLockVo getVoById(Long id) {
        return this.parkingLockRoMapper.getVoById(id);
    }

    public List<ParkingLockVo> parkingLockList(ListParkingLockParam param) {
        return parkingLockRoMapper.parkingLockList(param);
    }

    public long count(ListParkingLockParam param) {
        return parkingLockRoMapper.count(param);
    }

    public ParkingLockPo getByEvseNoAndPlugId(String evseNo, Integer plugId) {
        return parkingLockRoMapper.getByEvseNoAndPlugId(evseNo, plugId);
    }
}

