<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssPeakShareCfgRoMapper">



	<resultMap id="RESULT_ESSPEAKSHARECFG_PO" type="com.cdz360.iot.model.ess.po.EssPeakShareCfgPo">

		<result column="cfgId" jdbcType="BIGINT" property="cfgId" />

		<result column="peakFillEnable" jdbcType="BOOLEAN" property="peakFillEnable" />

		<result column="peakTime"  typeHandler="com.cdz360.iot.ds.ListTypeHandler" property="peakTime" />

		<result column="peakPower" jdbcType="DECIMAL" property="peakPower" />

		<result column="valleyTime"  typeHandler="com.cdz360.iot.ds.ListTypeHandler" property="valleyTime" />

		<result column="valleyPower" jdbcType="DECIMAL" property="valleyPower" />

		<result column="smoothOutputEnable" jdbcType="BOOLEAN" property="smoothOutputEnable" />

		<result column="monitoringPeriod" jdbcType="INTEGER" property="monitoringPeriod" />

		<result column="amplitude" jdbcType="INTEGER" property="amplitude" />

		<result column="targetPowerRating" jdbcType="DECIMAL" property="targetPowerRating" />

	</resultMap>
    <select id="getById" resultType="com.cdz360.iot.model.ess.po.EssPeakShareCfgPo" resultMap="RESULT_ESSPEAKSHARECFG_PO">
		select * from t_ess_peak_share_cfg where cfgId = #{id}
	</select>


</mapper>

