package com.cdz360.iot.ds.ro.mapper;


import com.cdz360.iot.model.evse.dto.EvseInfoDto;
import com.cdz360.iot.model.evse.param.ListPackageParam;
import com.cdz360.iot.model.evse.vo.EvsePackageVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 海外平台升级包
 */
@Mapper
public interface EvsePackageRoMapper {
    List<EvsePackageVo> getList(ListPackageParam param);
    Long getCount(ListPackageParam param);
    List<String> getBrandList();

}