package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.DevCfgRwMapper;
import com.cdz360.iot.model.ess.po.DevCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j

@Service

public class DevCfgRwDs {


    @Autowired

    private DevCfgRwMapper devCfgRwMapper;


    public DevCfgPo getById(Long id, boolean lock) {

        return this.devCfgRwMapper.getById(id, lock);

    }


    public void insertDevCfg(DevCfgPo devCfgPo) {

        this.devCfgRwMapper.insertDevCfg(devCfgPo);

    }

    public boolean updateDevCfg(DevCfgPo devCfgPo) {

        return this.devCfgRwMapper.updateDevCfg(devCfgPo) > 0;

    }


}

