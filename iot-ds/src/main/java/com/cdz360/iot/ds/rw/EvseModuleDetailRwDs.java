package com.cdz360.iot.ds.rw;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.rw.mapper.EvseModuleDetailRwMapper;
import com.cdz360.iot.model.evse.po.EvseModuleDetailPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EvseModuleDetailRwDs {

    @Autowired
    private EvseModuleDetailRwMapper mapper;

    public boolean insert(List<EvseModuleDetailPo> poList) {
        poList.forEach(e -> {
            if (e.getModuleId() == null) {
                throw new DcServiceException("moduleId不能为空");
            }
            if (e.getIdx() == null) {
                throw new DcServiceException("idx不能为空");
            }
        });
        return mapper.insert(poList) > 0;
    }

    public boolean updateById(EvseModuleDetailPo evseModuleDetailPo) {
        if (evseModuleDetailPo.getId() == null) {
            throw new DcServiceException("id不能为空");
        }
        return mapper.updateById(evseModuleDetailPo) > 0;
    }

    public boolean insertOrUpdate(EvseModuleDetailPo evseModuleDetailPo) {
        IotAssert.isNotNull(evseModuleDetailPo.getModuleId(), "moduleId不能为空");
        IotAssert.isNotNull(evseModuleDetailPo.getIdx(), "idx不能为空");
        return mapper.insertOrUpdate(evseModuleDetailPo) > 0;
    }

    public boolean delete(List<Long> idList) {
        return mapper.delete(idList) > 0;
    }

    public boolean deleteByNumber(@NonNull Long moduleId,
                                  @NonNull Integer number) {
        return mapper.deleteByNumber(moduleId, number) > 0;
    }

}
