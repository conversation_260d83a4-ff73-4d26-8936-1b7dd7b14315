package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.SiteTopologyRefRwMapper;
import com.cdz360.iot.model.topology.po.SiteTopologyRefPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SiteTopologyRefRwDs {

	@Autowired
	private SiteTopologyRefRwMapper siteTopologyRefRwMapper;

	public SiteTopologyRefPo getById(Integer id, boolean lock) {
		return this.siteTopologyRefRwMapper.getById(id, lock);
	}

	public boolean insertSiteTopologyRef(SiteTopologyRefPo siteTopologyRefPo) {
		return this.siteTopologyRefRwMapper.insertSiteTopologyRef(siteTopologyRefPo) > 0;
	}

	public int batchInsertSiteTopologyRef(List<SiteTopologyRefPo> list) {
		return this.siteTopologyRefRwMapper.batchInsertSiteTopologyRef(list);
	}

	public boolean updateSiteTopologyRef(SiteTopologyRefPo siteTopologyRefPo) {
		return this.siteTopologyRefRwMapper.updateSiteTopologyRef(siteTopologyRefPo) > 0;
	}


	public int deleteAllDown(Long upId) {
		return this.siteTopologyRefRwMapper.deleteAllDown(upId);
	}
}
