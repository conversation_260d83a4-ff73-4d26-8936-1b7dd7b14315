<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvseCfgRwMapper">

    <insert id="insertEvseCfg" keyColumn="id" keyProperty="id"
            useGeneratedKeys="true"
            parameterType="com.cdz360.iot.model.evse.po.EvseCfgPo">
        insert into t_evse_cfg (evseNo, adminPassword, level2Password,
        dayVolume, nightVolume, qrUrl, whiteCardList,
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( internationalAgreement )">
        internationalAgreement,
        </if>
        heating, heatingVoltage,
        avgOrTurnCharge, batteryCheck,
        securityCheck,
        constantCharge, vinDiscover,
        orderPrivacySetting, accountDisplayType,
        isCombineCharge,
        isQueryChargeRecord, isTimedCharge, isNoCardCharge,
        isScanCharge, isVinCharge, isCardCharge,
        isQuotaEleCharge, isQuotaMoneyCharge, isQuotaTimeCharge,
        opUid,
        enable, createTime, updateTime)
        values (#{evseNo,jdbcType=VARCHAR}, #{adminPassword}, #{level2Password},
        #{dayVolume}, #{nightVolume}, #{qrUrl}, #{whiteCardList},
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( internationalAgreement )">
        #{internationalAgreement},
        </if>
        #{heating}, #{heatingVoltage},
        #{avgOrTurnCharge}, #{batteryCheck},
        #{securityCheck},
        #{constantCharge}, #{vinDiscover},
        #{orderPrivacySetting}, #{accountDisplayType},
        #{isCombineCharge},
        #{isQueryChargeRecord}, #{isTimedCharge}, #{isNoCardCharge},
        #{isScanCharge}, #{isVinCharge}, #{isCardCharge},
        #{isQuotaEleCharge}, #{isQuotaMoneyCharge}, #{isQuotaTimeCharge},
        #{opUid},
        #{enable,jdbcType=BOOLEAN}, now(), now())
    </insert>

    <update id="disableEvseCfg">
        update t_evse_cfg set `enable` = false where evseNo = #{evseNo} and `enable` = true
    </update>

    <update id="disableEvseCfgList" parameterType="java.util.List">
        update t_evse_cfg set `enable` = false where `enable` = true
        <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
            and `evseNo` in
            <foreach item="evseNo" collection="evseNoList"
                     open="(" close=")" separator=",">
                #{evseNo}
            </foreach>
        </if>
    </update>

    <select id="getEvseCfg" resultType="com.cdz360.iot.model.evse.po.EvseCfgPo">
        select * from t_evse_cfg where evseNo = #{evseNo} and enable = true
        order by id desc limit 1
        <if test="lock == true">
            for update
        </if>
    </select>


    <select id="getEvseCfgVoList" resultType="com.cdz360.iot.model.evse.vo.EvseCfgVo">
        select cfg.* , e.name as evseName,
        cfgStatus.priceCodeResult, cfgStatus.expectPriceCode,
        cfgStatus.actualPriceCode, cfgStatus.priceCodeEffectiveTime, cfgStatus.cfgResult,
        cfgStatus.expectCfgCode,  cfgStatus.actualCfgCode,
        cfgStatus.whiteCardResult,  cfgStatus.expectWhiteCardCode,
        cfgStatus.actualWhiteCardCode
        from t_evse_cfg cfg
        left join t_evse_cfg_result as cfgStatus on cfg.`evseNo` = cfgStatus.`evseNo`
        left join t_evse e on cfg.`evseNo` = e.`evseId`
        where cfg.enable = true
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
            and e.siteId = #{siteId}
        </if>
        <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
            and cfg.`evseNo` in
            <foreach item="evseNo" collection="evseNoList"
                     open="(" close=")" separator=",">
                #{evseNo}
            </foreach>
        </if>
        order by cfg.evseNo asc
        <choose>
            <when test="start != null and size != null">
                limit #{start},#{size}
            </when>
            <when test="size != null">
                limit #{size}
            </when>
        </choose>
    </select>

    <select id="getEvseCfgVoCount" resultType="java.lang.Long">
        select count(distinct cfg.id)
        from t_evse_cfg cfg
        left join t_evse_cfg_result as cfgStatus on cfg.`evseNo` = cfgStatus.`evseNo`
        left join t_evse e on cfg.`evseNo` = e.`evseId`
        where cfg.enable = true
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
            and e.siteId = #{siteId}
        </if>
    </select>

<!--    <select id="getEvseCfgList" resultType="com.cdz360.iot.model.evse.vo.EvseCfgVo">-->
<!--        select cfg.* , e.name as evseName,-->
<!--        cfgStatus.priceCodeResult, cfgStatus.expectPriceCode,-->
<!--        cfgStatus.actualPriceCode, cfgStatus.cfgResult,-->
<!--        cfgStatus.expectCfgCode, cfgStatus.actualCfgCode,-->
<!--        cfgStatus.whiteCardResult, cfgStatus.expectWhiteCardCode,-->
<!--        cfgStatus.actualWhiteCardCode-->
<!--        from t_evse_cfg cfg-->
<!--        left join t_evse_cfg_result as cfgStatus on cfg.`evseNo` = cfgStatus.`evseNo`-->
<!--        left join t_evse e on cfg.`evseNo` = e.`evseId`-->
<!--        where cfg.enable = true-->
<!--        order by cfg.createTime desc-->
<!--        <if test="size != null">-->
<!--            limit #{size}-->
<!--        </if>-->
<!--        for update-->
<!--    </select>-->

    <select id="resetEvseCfgSelect" resultType="java.lang.Long">
        SELECT id FROM t_evse_cfg cfg
        WHERE cfg.id = (
        SELECT id FROM t_evse_cfg c
        WHERE c.evseNo = cfg.evseNo AND c.`enable` = FALSE ORDER BY c.id DESC LIMIT 1,1
        )
        <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
            AND cfg.`evseNo` IN
            <foreach item="evseNo" collection="evseNoList"
                     open="(" close=")" separator=",">
                #{evseNo}
            </foreach>
        </if>
    </select>

    <update id="resetEvseCfgUpdate">
        UPDATE t_evse_cfg SET `enable` = TRUE WHERE id in
        <foreach item="id" collection="ids"
                 open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
</mapper>