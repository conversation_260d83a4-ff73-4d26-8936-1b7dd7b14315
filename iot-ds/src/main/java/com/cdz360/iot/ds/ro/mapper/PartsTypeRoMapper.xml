<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.mapper.PartsTypeRoMapper">


  <resultMap id="RESULT_PARTSTYPE_PO" type="com.cdz360.iot.model.parts.po.PartsTypePo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="code" jdbcType="VARCHAR" property="code"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="fullModel" jdbcType="VARCHAR" property="fullModel"/>
  </resultMap>

  <resultMap id="RESULT_PARTSTYPE_VO" extends="RESULT_PARTSTYPE_PO"
    type="com.cdz360.iot.model.parts.vo.PartsTypeVo">
  </resultMap>

  <sql id="FIND_PARTS_TYPE_WHERE_SQL">
    <where>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(codeLike)">
        and code like CONCAT('%', #{codeLike}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(nameLike)">
        and name like CONCAT('%', #{nameLike}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(modelLike)">
        and fullModel like CONCAT('%', #{modelLike}, '%')
      </if>
    </where>
  </sql>


  <select id="getOneByFields"
    resultMap="RESULT_PARTSTYPE_PO">
    select * from t_parts_type where
    name = #{name} and code = #{code} and fullModel = #{fullModel}
    limit 1
  </select>

  <select id="getById"
    resultMap="RESULT_PARTSTYPE_PO">
    select * from t_parts_type where id = #{id}
  </select>

  <select id="findPartsType"
    parameterType="com.cdz360.iot.model.parts.param.ListPartsTypeParam"
    resultMap="RESULT_PARTSTYPE_VO">
    select * from t_parts_type
    <include refid="FIND_PARTS_TYPE_WHERE_SQL"/>
  </select>

  <select id="countPartsType" resultType="java.lang.Long">
    select count(*) from t_parts_type
    <include refid="FIND_PARTS_TYPE_WHERE_SQL"/>
  </select>

  <select id="getByFields" parameterType="com.cdz360.iot.model.parts.param.PartsCheckParam"
    resultMap="RESULT_PARTSTYPE_PO">
    select
    *
    from
    t_parts_type
    <where>
      <foreach collection="paramList" item="item" open="(" close=")" separator="or">
        (name = #{item.typeName} and code = #{item.typeCode} and fullModel = #{item.typeFullModel})
      </foreach>
    </where>
  </select>

</mapper>

