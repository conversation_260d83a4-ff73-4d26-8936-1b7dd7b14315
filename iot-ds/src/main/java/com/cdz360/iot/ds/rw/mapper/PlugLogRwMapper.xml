<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.PlugLogRwMapper">

  <insert id="addPlugLog" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.evse.po.PlugLogPo">
    insert into t_plug_log(`siteId`, evseNo, plugNo,
    <if test="eventType != null">
      eventType,
    </if>
    `plugStatus`,
    <if test="errorCode != null">
      errorCode,
    </if>
    createTime
    )
    values (#{siteId}, #{evseNo}, #{plugNo},
    <if test="eventType != null">
      #{eventType.code},
    </if>
    #{plugStatus},
    <if test="errorCode != null">
      #{errorCode},
    </if>
    #{createTime}
    )
  </insert>
</mapper>