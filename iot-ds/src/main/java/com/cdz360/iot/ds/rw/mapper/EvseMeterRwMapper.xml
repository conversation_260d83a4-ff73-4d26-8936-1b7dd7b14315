<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvseMeterRwMapper">

	<resultMap id="RESULT_EVSEMETER_PO" type="com.cdz360.iot.model.meter.po.DeviceMeterPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="deviceId" jdbcType="VARCHAR" property="deviceId" />
		<result column="meterId" jdbcType="BIGINT" property="meterId" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_EVSEMETER_PO">	
		select * from t_device_meter where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertEvseMeter" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.iot.model.meter.po.DeviceMeterPo">
		insert into t_device_meter (`deviceId`,
			`meterId`,
			`createTime`)
		values (#{deviceId},
			#{meterId},
			now())
	</insert>

	<update id="updateEvseMeter" parameterType="com.cdz360.iot.model.meter.po.DeviceMeterPo">
		update t_device_meter set
		<if test="deviceId != null">
			deviceId = #{deviceId},
		</if>
		<if test="meterId != null">
			meterId = #{meterId},
		</if>
		where id = #{id}
	</update>

	<insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
		insert into t_device_meter (
		`estimateType`,
		`deviceId`,
		`meterId`,
		`pcsAcInput`,
		`pcsAcOutput`,
		`createTime`)
		values
		<foreach collection="list" item="item" separator=",">
			(#{item.estimateType,jdbcType=VARCHAR},
			#{item.deviceId,jdbcType=VARCHAR},
			#{item.meterId,jdbcType=BIGINT},
			#{item.pcsAcInput,jdbcType=VARCHAR},
			#{item.pcsAcOutput,jdbcType=VARCHAR},
			now())
		</foreach>
	</insert>

	<delete id="deleteEvseMeter">
		delete from t_device_meter
		where meterId=#{meterId}
	</delete>

</mapper>
