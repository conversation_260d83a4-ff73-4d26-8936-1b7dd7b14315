package com.cdz360.iot.ds.ro.ess.ds;

import com.cdz360.iot.ds.ro.ess.mapper.EssDailyRoMapper;
import com.cdz360.iot.model.ess.param.EssMapDataParam;
import com.cdz360.iot.model.ess.po.EssDailyPo;
import com.cdz360.iot.model.ess.vo.CommEssMapDataVo;
import com.cdz360.iot.model.ess.vo.DayEssDataBi;
import com.cdz360.iot.model.ess.vo.DaySiteEssRtDataBi;
import com.cdz360.iot.model.ess.vo.EquipSampleData;
import com.cdz360.iot.model.ess.vo.EssDataBi;
import com.cdz360.iot.model.pv.param.DataBiParam;
import com.cdz360.iot.model.pv.param.DayKwhParam;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssDailyRoDs {

    @Autowired
    private EssDailyRoMapper essDailyRoMapper;

    public EssDailyPo getById(Long id) {
        return this.essDailyRoMapper.getById(id);
    }

    public List<DayEssDataBi> siteRtDataOfDay(String siteId, Date fromDate, Date toDate) {
        return this.essDailyRoMapper.siteRtDataOfDay(siteId, fromDate, toDate);
    }

    public EssDataBi rtDataOfYesterday(DayKwhParam param) {
        EssDataBi bi = this.essDailyRoMapper.rtDataOfYesterday(param);
        return null != bi ? bi : new EssDataBi().setProfit(BigDecimal.ZERO)
            .setInExpend(BigDecimal.ZERO)
            .setInKwh(BigDecimal.ZERO)
            .setOutIncome(BigDecimal.ZERO)
            .setOutKwh(BigDecimal.ZERO);
    }

    public EssDataBi rtDataOfMonth(DayKwhParam param) {
        EssDataBi bi = this.essDailyRoMapper.rtDataOfMonth(param);
        return null != bi ? bi : new EssDataBi().setProfit(BigDecimal.ZERO)
            .setInExpend(BigDecimal.ZERO)
            .setInKwh(BigDecimal.ZERO)
            .setOutIncome(BigDecimal.ZERO)
            .setOutKwh(BigDecimal.ZERO);
    }

    public EssDataBi rtDataOfTotal(DayKwhParam param) {
        EssDataBi bi = this.essDailyRoMapper.rtDataOfTotal(param);
        return null != bi ? bi : new EssDataBi().setProfit(BigDecimal.ZERO)
            .setInExpend(BigDecimal.ZERO)
            .setInKwh(BigDecimal.ZERO)
            .setOutIncome(BigDecimal.ZERO)
            .setOutKwh(BigDecimal.ZERO);
    }

    public List<DaySiteEssRtDataBi> siteDayOfRangeKwh(DayKwhParam param) {
        return this.essDailyRoMapper.siteDayOfRangeKwh(param);
    }

    public List<EquipSampleData> equipRtDataSample(DataBiParam param) {
        return this.essDailyRoMapper.equipRtDataSample(param);
    }

    @Deprecated(since = "20240523")
    public CommEssMapDataVo commEssMapData(EssMapDataParam param) {
        return this.essDailyRoMapper.commEssMapData(param);
    }
}

