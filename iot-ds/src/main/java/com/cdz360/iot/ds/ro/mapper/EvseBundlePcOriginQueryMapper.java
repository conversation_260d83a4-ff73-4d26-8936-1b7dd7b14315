package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.evse.EvseBundlePcOrigin;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * @ClassName： EvseBundlePcOriginQueryMapper
 * @Description: 升级包支持的源版本信息数据访问层-QUERY
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/18 13:41
 */
@Mapper
public interface EvseBundlePcOriginQueryMapper {
    EvseBundlePcOrigin selectByPrimaryKey(Long id);
    List<EvseBundlePcOrigin> selectByBundleId(Long bundleId);
}