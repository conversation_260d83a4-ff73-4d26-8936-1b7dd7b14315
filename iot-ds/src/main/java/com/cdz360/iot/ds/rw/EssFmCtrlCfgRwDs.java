package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.EssFmCtrlCfgRwMapper;
import com.cdz360.iot.model.ess.po.EssFmCtrlCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class EssFmCtrlCfgRwDs {



	@Autowired

	private EssFmCtrlCfgRwMapper essFmCtrlCfgRwMapper;



	public boolean insertEssFmCtrlCfg(EssFmCtrlCfgPo essFmCtrlCfgPo) {

		return this.essFmCtrlCfgRwMapper.insertEssFmCtrlCfg(essFmCtrlCfgPo) > 0;

	}

	public void insertBeforeSelect(Long oldId, Long newId) {

		this.essFmCtrlCfgRwMapper.insertBeforeSelect(oldId,newId);

	}



	public boolean updateEssFmCtrlCfg(EssFmCtrlCfgPo essFmCtrlCfgPo) {

		return this.essFmCtrlCfgRwMapper.updateEssFmCtrlCfg(essFmCtrlCfgPo) > 0;

	}





}

