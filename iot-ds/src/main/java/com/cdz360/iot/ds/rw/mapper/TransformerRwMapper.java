package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.transformer.po.TransformerPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TransformerRwMapper {
	TransformerPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertTransformer(TransformerPo TransformerPo);

	int updateTransformer(TransformerPo TransformerPo);

	int delete(@Param("id") long id);
}
