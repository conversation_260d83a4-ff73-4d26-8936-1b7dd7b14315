package com.cdz360.iot.task.cfg;

import com.cdz360.data.sync.constant.DcMqConstants;
import com.cdz360.iot.common.base.IotConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class MqListenerConfig {

    //////////////////// 网关上行指令
    @Bean
    public Queue iotGwUpCmdQueue() {
        return new Queue(IotConstants.IOT_GW_UP_CMD_QUEUE_NAME, true, false, false);
    }

    @Bean
    public DirectExchange exchangeIotGwCmd() {
        return new DirectExchange(DcMqConstants.MQ_EXCHANGE_NAME_IOT_CMD, true, false);
    }

    @Bean
    public Binding bindingExchangeIotGwUpCmd(Queue iotGwUpCmdQueue, DirectExchange exchangeIotGwCmd) {

        return BindingBuilder.bind(iotGwUpCmdQueue).to(exchangeIotGwCmd).with(DcMqConstants.MQ_ROUTING_KEY_GW_CMD_UP);
    }

    //////////////////// 网关下行指令
    @Bean
    public Queue iotGwDownCmdQueue() {
        return new Queue(IotConstants.IOT_GW_DOWN_CMD_QUEUE_NAME, true, false, false);
    }

//    @Bean
//    DirectExchange exchangeIotGwDownCmd() {
//        return new DirectExchange(DcMqConstants.MQ_EXCHANGE_NAME_IOT_CMD, true, true);
//    }

    @Bean
    public Binding bindingExchangeIotGwDownCmd(Queue iotGwDownCmdQueue, DirectExchange exchangeIotGwCmd) {

        return BindingBuilder.bind(iotGwDownCmdQueue).to(exchangeIotGwCmd).with(DcMqConstants.MQ_ROUTING_KEY_GW_CMD_DOWN);
    }
}
