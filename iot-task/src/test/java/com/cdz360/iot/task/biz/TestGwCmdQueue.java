package com.cdz360.iot.task.biz;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.data.sync.model.iot.IotGwDownCmd;
import com.cdz360.iot.task.IotTaskTestBase;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class TestGwCmdQueue extends IotTaskTestBase {


    @Autowired
    private GwCmdQueue gwCmdQueue;


    @Test
    public void test_thread_safe() throws InterruptedException {
        Thread addTh = new Thread(() -> addQueueLoop());
        addTh.start();

        Thread.sleep(1000L);
        Thread removeTh = new Thread(() -> removeQueueLoop());
        removeTh.start();

        Thread loopTh = new Thread(() -> loopLoop());
        loopTh.start();

        Thread.sleep(10 * 1000L);
    }


    private void addQueueLoop() {
        boolean stop = false;
        long seq = 0L;
        while (!stop) {
            try {
                IotGwDownCmd cmd = new IotGwDownCmd();
                cmd.setSeq(String.valueOf(seq++))
                        .setGwno("abcd1234")
                        .setCmd(IotGwCmdType2.CE_CHARGE_START);
                gwCmdQueue.addCmd(cmd);
                Thread.sleep(10L);
            } catch (Exception e) {
                stop = true;
            }
        }
    }

    private void removeQueueLoop() {
        boolean stop = false;
        long seq = 0L;
        while (!stop) {
            try {
                IotGwDownCmd cmd = new IotGwDownCmd();
                cmd.setSeq(String.valueOf(seq++))
                        .setGwno("abcd1234")
                        .setCmd(IotGwCmdType2.CE_CHARGE_START);
                gwCmdQueue.removeCmd(cmd);
                Thread.sleep(10L);
            } catch (Exception e) {
                stop = true;
            }
        }
    }

    private void loopLoop() {
        boolean stop = false;
        while (!stop) {
            try {
                gwCmdQueue.expireExecution();

            } catch (Exception e) {
                stop = true;
            }
        }
    }
}
