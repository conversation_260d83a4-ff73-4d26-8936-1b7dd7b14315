package com.cdz360.iot.ess.south;

import io.netty.channel.Channel;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Modbus TCP 连接的管理类
 */
@Slf4j
@Component
public class ModbusTcpChannelMgm {

    private ConcurrentMap<String, Channel> channelCache = new ConcurrentHashMap<>();

    public ModbusTcpChannelMgm put(String key, Channel value) {
        channelCache.put(key, value);
        return this;
    }

    public Channel get(String key) {
        return channelCache.get(key);
    }

    public void remove(String key) {
        log.info("移除tcp链接: {}", key);
        this.channelCache.remove(key);
    }

    public int size() {
        return this.channelCache.size();
    }

    // 测试测试
    public Set<String> allChannelKey() {
        return channelCache.keySet();
    }
}
