package com.cdz360.iot.ess.biz.south;

import com.cdz360.base.utils.NumberUtils;
import com.cdz360.iot.ds.ro.ess.ds.EssBatteryBundleRoDs;
import com.cdz360.iot.model.bms.dto.BatteryBundleCfgDto;
import com.cdz360.iot.model.bms.dto.BatteryStackCfgDto;
import com.cdz360.iot.model.ess.param.ListEssEquipParam;
import com.cdz360.iot.model.ess.po.EssBatteryBundlePo;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class BatteryBizService {

    @Autowired
    private EssBatteryBundleRoDs batteryBundleRoDs;

    /**
     * 根据储能设备dno查询下属的电池堆/蔟信息，返回的电池蔟按序号做排序
     * @param essDno
     * @return key为bmsDno, 值为电池堆配置列表
     */
    @Transactional(readOnly = true)
    public Map<String, List<BatteryStackCfgDto>> getBatteryStackCfgsByEssDno(String essDno) {
        ListEssEquipParam listBundleParam = new ListEssEquipParam();
        listBundleParam.setEssDno(essDno)
            .setStart(0L)
            .setSize(100);
        List<EssBatteryBundlePo> batteryBundles = this.batteryBundleRoDs.getBatteryBundleList(
            listBundleParam);

        Map<String, BatteryStackCfgDto> stacks = new HashMap<>();   // 电池堆
        for (var bundlePo : batteryBundles) {
            if (!stacks.containsKey(bundlePo.getStackDno())) {
                BatteryStackCfgDto stack = new BatteryStackCfgDto();
                stack.setDno(bundlePo.getStackDno())
                    .setBmsDno(bundlePo.getBmsDno())
                    .setBundles(new ArrayList<>());
                stacks.put(stack.getDno(), stack);
            }
            var stack = stacks.get(bundlePo.getStackDno());
            stack.getBundles().add(new BatteryBundleCfgDto(bundlePo.getDno(), bundlePo.getIdx()));
        }
        Map<String, List<BatteryStackCfgDto>> stackListMap = new HashMap<>();
        for (var stack : stacks.values()) {
            if (!stackListMap.containsKey(stack.getBmsDno())) {
                stackListMap.put(stack.getBmsDno(), new ArrayList<>());
            }
            // 将电池蔟按序号排序
            stack.setBundles(stack.getBundles().stream()
                .sorted((r, l) -> NumberUtils.compareInteger(r.getIdx(), l.getIdx()))
                .collect(Collectors.toList()));
            stackListMap.get(stack.getBmsDno()).add(stack);
        }
        Map<String, List<BatteryStackCfgDto>> sortedResult = new HashMap<>();
        // 把电池堆列表重新做排序，确保每次下发的顺序一致
        for (String bmsDno : stackListMap.keySet()) {
            List<BatteryStackCfgDto> stackList = stackListMap.get(bmsDno);
            sortedResult.put(bmsDno, stackList.stream()
                .sorted(Comparator.comparing(BatteryStackCfgDto::getDno))
                .collect(Collectors.toList()));
        }
        return sortedResult;
    }

}
