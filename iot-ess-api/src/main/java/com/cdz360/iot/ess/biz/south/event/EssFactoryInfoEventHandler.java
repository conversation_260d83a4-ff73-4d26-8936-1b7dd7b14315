package com.cdz360.iot.ess.biz.south.event;

import com.cdz360.base.model.es.dto.EssFactoryDto;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.sync.event.ess.EssFactoryInfoEvent;
import com.cdz360.data.sync.event.ess.EssPushEventType;
import com.cdz360.iot.model.dtu.po.EssDtuEssRefPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.HashMap;
import java.util.List;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssFactoryInfoEventHandler extends AbstractEssGwPushEventHandler {

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(EssPushEventType.FACTORY_INFO, this);
    }

    @Override
    public Mono<Boolean> process(JsonNode json) {
        log.debug("处理设备上报厂家信息: {}", JsonUtils.toJsonString(json));
        EssFactoryInfoEvent event = JsonUtils.fromJson(json, EssFactoryInfoEvent.class);
        EssFactoryDto data = event.getData();
        if (null != data) {
            List<EssDtuEssRefPo> refList = essDtuEssRefRoDs.getBySerialNo(event.getSerialNo());
            if (CollectionUtils.isNotEmpty(refList)) {
                EssPo ess = essRoDs.getByDno(refList.get(0).getDno()); // 默认只有一个
                if (null != ess) {
                    ess.setSoftVersion(data.getProtocolVer());

                    // 其他版本信息
                    HashMap<String, String> verMap = new HashMap<>();
                    if (StringUtils.isNotBlank(data.getDsp1SoftwareVer())) {
                        verMap.put("DSP1", data.getDsp1SoftwareVer());
                    }
                    if (StringUtils.isNotBlank(data.getDsp2SoftwareVer())) {
                        verMap.put("DSP2", data.getDsp2SoftwareVer());
                    }
                    if (StringUtils.isNotBlank(data.getArmSoftwareVer())) {
                        verMap.put("ARM", data.getArmSoftwareVer());
                    }

                    if (verMap.keySet().size() > 0) {
                        ess.setOtherVersion(JsonUtils.toJsonString(verMap));
                    }

                    if (null != data.getType()) {
                        ess.setDeviceModel(data.getType().toString());
                    }
                    boolean b = essRwDs.upsetEss(ess);
                    if (!b) {
                        log.warn("ESS设备厂家信息更新异常: {}", ess);
                    }
                }
            }
        }
        return Mono.just(true);
    }
}
