package com.cdz360.iot.ess.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "户用储能设备中电池运行时数据")
@Data
@Accessors(chain = true)
public class HTEssBatteryData {

    @Schema(description = "Battery type")
    @JsonInclude(Include.NON_NULL)
    private Integer batteryType;

    @Schema(description = "Reserved")
    @JsonInclude(Include.NON_NULL)
    private String reserved;

    @Schema(description = "Battery capacity(Ah)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal ah;

    @Schema(description = "BMS firmware version")
    @JsonInclude(Include.NON_NULL)
    private String bmsVersion;

    @Schema(description = "BMS work status")
    @JsonInclude(Include.NON_NULL)
    private Integer bmsStatus;

    @Schema(description = "Battery real-time voltage(0.1V)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal volt;

    @Schema(description = "Battery real-time current(0.01A)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal cur;

    @Schema(description = "Battery real-time power(0.1W)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pBat;

    @Schema(description = "Battery Capacity Percent(%)")
    @JsonInclude(Include.NON_NULL)
    private Integer soc;

    @Schema(description = "Battery SOH(%)")
    @JsonInclude(Include.NON_NULL)
    private Integer soh;

    @Schema(description = "Battery real-time internal temperature(℃)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal temp;

    @Schema(description = "Battery cycles times")
    @JsonInclude(Include.NON_NULL)
    private Integer cyclesTimes;

    @Schema(description = "BMS warning(low word) & BMS protect code(high word)")
    @JsonInclude(Include.NON_NULL)
    private Long batErrorCode;

    @Schema(description = "The accumulated energy to charge battery in a day(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eDayBatChrg;

    @Schema(description = "The accumulated energy to charge battery in a month(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eMonBatChrg;

    @Schema(description = "The accumulated energy to charge battery in a year(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eYearBatChrg;

    @Schema(description = "The total accumulated energy to charge battery(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eTotalBatChrg;

    @Schema(description = "The accumulated energy of battery discharge in a day(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eDayBatDischrg;

    @Schema(description = "The accumulated energy to battery discharge  in a month(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eMonBatDischrg;

    @Schema(description = "The accumulated energy to battery discharge  in a year(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eYearBatDischrg;

    @Schema(description = "The total accumulated energy to battery discharge(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eTotalBatDischrg;
}
