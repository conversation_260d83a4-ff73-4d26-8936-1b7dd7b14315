package com.cdz360.iot.ess.utils;

import com.cdz360.base.model.es.dto.EssAlarmTinyDto;
import com.cdz360.base.model.es.dto.EssAlarms;
import com.cdz360.base.model.es.type.BmsAlarmCode;
import com.cdz360.base.model.es.type.DehAlarmCode;
import com.cdz360.base.model.es.type.EmuAlarmCode;
import com.cdz360.base.model.es.type.EssAlarmType;
import com.cdz360.base.model.es.type.EssBaseAlarmCode;
import com.cdz360.base.model.es.type.FfsAlarmCode;
import com.cdz360.base.model.es.type.LiquidAlarmCode;
import com.cdz360.base.model.es.type.MeterAlarmCode;
import com.cdz360.base.model.es.type.PcsAlarmCode;
import com.cdz360.base.model.es.type.UpsAlarmCode;
import com.cdz360.base.model.es.type.WarnDeviceType;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.iot.model.alarm.type.AlarmStatusEnum;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.vo.EssVo;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.stream.Collectors;

public class EssAlarmBuilder {

    /**
     * 构建 PCS 告警数据
     *
     * @param gw 工商储场站 gw 即 EMU 即 ESS
     */
    public static EssAlarms build4Pcs(GwInfoDto gw, Long ts, String tz, EssEquipPo pcsEquip,
        List<PcsAlarmCode> pcsAlarmsIn) {
        LocalDateTime ldt = LocalDateTime.ofEpochSecond(ts, 0, ZoneOffset.of(tz));
        return EssAlarmBuilder.build4EmuSubEquip(gw, ts, tz, pcsEquip,
            () -> buildAlarmList(ldt, pcsAlarmsIn));
    }


    /**
     * 构建 BMS 告警数据
     *
     * @param gw 工商储场站 gw 即 EMU 即 ESS
     */
    public static EssAlarms build4Bms(GwInfoDto gw, Long ts, String tz, EssEquipPo bmsEquip,
        List<BmsAlarmCode> bmsAlarmsIn) {
        LocalDateTime ldt = LocalDateTime.ofEpochSecond(ts, 0, ZoneOffset.of(tz));
        return EssAlarmBuilder.build4EmuSubEquip(gw, ts, tz, bmsEquip,
            () -> buildAlarmList(ldt, bmsAlarmsIn));
    }

    /**
     * 构建 电池堆 告警数据
     *
     * @param gw 工商储场站 gw 即 EMU 即 ESS
     */
    public static EssAlarms build4BatteryStack(GwInfoDto gw, Long ts, String tz,
        EssEquipPo bsEquip,
        List<BmsAlarmCode> bmsAlarmsIn) {
        LocalDateTime ldt = LocalDateTime.ofEpochSecond(ts, 0, ZoneOffset.of(tz));
        return EssAlarmBuilder.build4EmuSubEquip(gw, ts, tz, bsEquip,
            () -> buildAlarmList(ldt, bmsAlarmsIn));
    }

    /**
     * 构建 电池簇 告警数据
     *
     * @param gw 工商储场站 gw 即 EMU 即 ESS
     */
    public static EssAlarms build4BatteryBundle(GwInfoDto gw, Long ts, String tz,
        EssEquipPo batteryBundleEquip,
        List<BmsAlarmCode> bmsAlarmsIn) {
        LocalDateTime ldt = LocalDateTime.ofEpochSecond(ts, 0, ZoneOffset.of(tz));
        return EssAlarmBuilder.build4EmuSubEquip(gw, ts, tz, batteryBundleEquip,
            () -> buildAlarmList(ldt, bmsAlarmsIn));
    }


    /**
     * 构建 电表 告警数据
     *
     * @param gw 工商储场站 gw 即 EMU 即 ESS
     */
    public static EssAlarms build4Meter(GwInfoDto gw, Long ts, String tz, EssEquipPo meterEquip,
        List<MeterAlarmCode> meterAlarmsIn) {
        LocalDateTime ldt = LocalDateTime.ofEpochSecond(ts, 0, ZoneOffset.of(tz));
        return EssAlarmBuilder.build4EmuSubEquip(gw, ts, tz, meterEquip,
            () -> buildAlarmList(ldt, meterAlarmsIn));
    }


    /**
     * 构建 空调/除湿 告警数据
     *
     * @param gw 工商储场站 gw 即 EMU 即 ESS
     */
    public static EssAlarms build4Deh(GwInfoDto gw, Long ts, String tz, EssEquipPo dehEquip,
        List<DehAlarmCode> dehAlarmsIn) {
        LocalDateTime ldt = LocalDateTime.ofEpochSecond(ts, 0, ZoneOffset.of(tz));
        return EssAlarmBuilder.build4EmuSubEquip(gw, ts, tz, dehEquip,
            () -> buildAlarmList(ldt, dehAlarmsIn));
    }

    /**
     * 构建 消防 告警数据
     *
     * @param gw 工商储场站 gw 即 EMU 即 ESS
     */
    public static EssAlarms build4Ffs(GwInfoDto gw, Long ts, String tz, EssEquipPo ffsEquip,
        List<FfsAlarmCode> ffsAlarmsIn) {
        LocalDateTime ldt = LocalDateTime.ofEpochSecond(ts, 0, ZoneOffset.of(tz));
        return EssAlarmBuilder.build4EmuSubEquip(gw, ts, tz, ffsEquip,
            () -> buildAlarmList(ldt, ffsAlarmsIn));
    }


    /**
     * 构建 UPS 告警数据
     *
     * @param gw 工商储场站 gw 即 EMU 即 ESS
     */
    public static EssAlarms build4Ups(GwInfoDto gw, Long ts, String tz, EssEquipPo upsEquip,
        List<UpsAlarmCode> upsAlarmsIn) {
        LocalDateTime ldt = LocalDateTime.ofEpochSecond(ts, 0, ZoneOffset.of(tz));
        return EssAlarmBuilder.build4EmuSubEquip(gw, ts, tz, upsEquip,
            () -> buildAlarmList(ldt, upsAlarmsIn));
    }

    /**
     * 构建 液冷 告警数据
     *
     * @param gw 工商储场站 gw 即 EMU 即 ESS
     */
    public static EssAlarms build4Liquid(GwInfoDto gw, Long ts, String tz, EssEquipPo liquidEquip,
        List<LiquidAlarmCode> liquidAlarmsIn) {
        LocalDateTime ldt = LocalDateTime.ofEpochSecond(ts, 0, ZoneOffset.of(tz));
        return EssAlarmBuilder.build4EmuSubEquip(gw, ts, tz, liquidEquip,
            () -> buildAlarmList(ldt, liquidAlarmsIn));
    }

    /**
     * @param gw 工商储场站 gw 即 EMU 即 ESS
     */
    private static EssAlarms build4EmuSubEquip(GwInfoDto gw, Long ts, String tz,
        EssEquipPo subEquip, AlarmListBuilder alarmListBuilder) {
        EssAlarms alarm = new EssAlarms();
        alarm.setGwno(gw.getGwno())
            .setEquipDno(subEquip.getDno())
            .setEquipType(subEquip.getEquipType())
            .setEquipName(subEquip.getName())
            .setEssDno(gw.getGwno())
            .setEssName(gw.getName())
            .setSiteId(gw.getSiteId())
            .setSiteName(gw.getSiteName())
            .setEquipCategory(WarnDeviceType.COMM_ESS)
            .setTs(ts)
            .setTz(tz);
        List<EssAlarmTinyDto> alarms = alarmListBuilder.buildAlarmList();
        alarm.setAlarms(alarms);
        return alarm;
    }

    private static <ERROR_TYPE extends EssBaseAlarmCode> List<EssAlarmTinyDto> buildAlarmList(
        LocalDateTime ldt, List<ERROR_TYPE> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(c -> {
            EssAlarmTinyDto a = new EssAlarmTinyDto();
            a.setAlarmType(EssAlarmType.ALARM)
                .setCode(c.name())
                .setLevel(c.getLevel())
                .setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue())
                .setStartTime(ldt);
            return a;
        }).collect(Collectors.toList());
    }


    public static EssAlarms build4Emu(EssVo ess, Long ts, String tz,
        List<EmuAlarmCode> emuAlarmsIn) {
        LocalDateTime ldt = LocalDateTime.ofEpochSecond(ts, 0, ZoneOffset.of(tz));
        EssAlarms alarm = new EssAlarms();
        alarm.setGwno(ess.getGwno())
            .setEquipDno(ess.getDno())
            .setEquipType(EssEquipType.EMU)
            .setEquipName(ess.getName())
            .setEssDno(ess.getDno())
            .setEssName(ess.getName())
            .setSiteId(ess.getSiteId())
            .setSiteName(ess.getSiteName())
            .setEquipCategory(WarnDeviceType.COMM_ESS)
            .setTs(ts)
            .setTz(tz);
        alarm.setAlarms(EssAlarmBuilder.buildAlarmList(ldt, emuAlarmsIn));
        return alarm;
    }

//    public static EssAlarmDto build4EmuDi(EssVo ess, EmuDiDataDto emuDiDataIn, Integer errorCode) {
//        LocalDateTime ldt = LocalDateTime.ofEpochSecond(emuDiDataIn.getTs(), 0,
//            ZoneOffset.of(emuDiDataIn.getTz()));
//        EssAlarmDto alarm = new EssAlarmDto();
//        alarm.setGwno(ess.getGwno())
//            .setEquipDno(emuDiDataIn.getDno())
//            .setEquipType(EssEquipType.EMU)
//            .setEquipName(ess.getName())
//            .setEssDno(ess.getDno())
//            .setEssName(ess.getName())
//            .setSiteId(ess.getSiteId())
//            .setSiteName(ess.getSiteName())
//            .setAlarmType(EssAlarmType.ALARM)
//            .setEquipCategory(WarnDeviceType.COMM_ESS)
//            .setCode(EmuAlarmCode.valueOf(errorCode).name())
//            .setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue())
//            .setStartTime(ldt)
//            .setTz(emuDiDataIn.getTz());
//
//        return alarm;
//    }

    interface AlarmListBuilder {

        List<EssAlarmTinyDto> buildAlarmList();
    }


}
