package com.cdz360.iot.ess.rest.internal;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ess.biz.EssBizService;
import com.cdz360.iot.ess.biz.RedisEssEquipRtDataService;
import com.cdz360.iot.model.ess.po.EssEquipLangPo;
import com.cdz360.iot.model.ess.vo.EssVo;
import com.cdz360.iot.model.meter.dto.EssMeterCfg;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.LocalDate;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "储能相关接口", description = "储能服务")
@RequestMapping("/iot/biz/ess")
public class EssRest {

    @Autowired
    private EssBizService essBizService;

    @Autowired
    private RedisEssEquipRtDataService redisEssEquipRtDataService;

//    @Operation(summary = "发送获取逆变器信息指令")
//    @PostMapping(value = "/sendGetGtiInfoCmd")
//    public Mono<BaseResponse> sendGetGtiInfoCmd(@RequestParam String siteId, @RequestParam List<String> gtiNos) {
//        log.info("发送获取逆变器信息指令。siteId = {}", siteId);
//        return gtiBizService.getGtiInfo(siteId, gtiNos)
//                .map(res -> RestUtils.success());
//    }

    @Operation(summary = "设置ESS信息")
    @PostMapping(value = "/sendModifyEssCfgCmd")
    public Mono<BaseResponse> sendModifyEssCfgCmd(
        @Parameter(name = "ESS编号", required = true) @RequestParam String dno,
        @Parameter(name = "配置模板ID", required = true) @RequestParam Long cfgId) {
        log.info("下发逆变器配置信息。dno = {}, cfgId = {}", dno, cfgId);
        return essBizService.modifyEssCfg(dno, cfgId)
            .map(res -> RestUtils.success());
    }

    @Operation(summary = "设置ESS信息")
    @PostMapping(value = "/sendCfgByEquipId")
    public Mono<BaseResponse> sendCfgByEquipId(
        @Parameter(name = "ESS挂载设备ID", required = true) @RequestParam Long equipId) {
        log.info("下发逆变器配置信息。equipId = {}", equipId);
        return essBizService.sendCfgByEquipId(equipId)
            .map(res -> RestUtils.success());
    }

    @Operation(summary = "上传ESS数据文件到oss(用于手动补传数据)")
    @GetMapping(value = "/uploadEssDataFile")
    public Mono<BaseResponse> uploadEssDataFile(
        @Parameter(name = "场站ID", required = true) @RequestParam(value = "siteId") String siteId,
        @Parameter(name = "日期", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd")
        @RequestParam(value = "date", required = false) Date date) {
        log.info("手动上传ESS运行数据 siteId = {}, date = {}", siteId, date);
        return essBizService.uploadEssDataFile(siteId, date)
            .map(o -> RestUtils.success());
    }

    @Operation(summary = "ESS同步网元指令下发")
    @GetMapping(value = "/syncEssEquip")
    public Mono<BaseResponse> syncEssEquip(@RequestParam(value = "dno") String dno) {
        log.info("ESS同步网元指令下发 dno: {}", dno);
        return essBizService.syncEssEquip(dno)
            .map(e -> RestUtils.success());
    }

    @Operation(summary = "下发修改电表信息")
    @PostMapping(value = "/modifyEssMeter")
    public Mono<BaseResponse> modifyEssMeter(@RequestBody EssMeterCfg param) {
        log.info("ESS同步网元指令下发 param: {}", param);
        return essBizService.modifyEssMeter(param)
            .map(e -> RestUtils.success());
    }

    @Operation(summary = "同步储能日数据到Mongo", description = "定时任务凌晨3:00")
    @GetMapping(value = "/syncEssUserRtData2Mongo")
    public Mono<BaseResponse> syncEssUserRtData2Mongo(
        @Parameter(name = "指定同步数据日期")
        @RequestParam(value = "destDate", required = false)
        @JsonDeserialize(using = LocalDateDeserializer.class)
        @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate destDate) {
        log.info("同步储能日数据到Mongo: {}", destDate);
        return essBizService.syncEssUserRtData2Mongo(destDate)
            .doOnNext(i -> log.info("数据同步完成: i = {}", i))
            .map(res -> RestUtils.success());
    }

    @GetMapping(value = "/getEssVo")
    public Mono<ObjectResponse<EssVo>> getEssVo(@RequestParam(value = "dno") String dno) {
        return essBizService.getEssVo(dno)
            .map(RestUtils::buildObjectResponse);
    }


}
