package com.cdz360.iot.ess.biz;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.TimeZoneParser;
import com.cdz360.iot.model.ess.dto.BatteryCluster;
import com.cdz360.iot.model.ess.dto.BatteryPack;
import com.cdz360.iot.model.ess.dto.Pcs;
import com.cdz360.iot.model.ess.dto.RedisEquipRtData;
import com.cdz360.iot.model.ess.po.PriceItemPo;
import com.cdz360.iot.model.ess.vo.BatteryClusterRelation;
import com.cdz360.iot.model.ess.vo.BatteryPackRelation;
import com.cdz360.iot.model.ess.vo.BatteryStackRelation;
import com.cdz360.iot.model.ess.vo.EssDataBi;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryPackSimpleVo;
import com.cdz360.iot.model.ess.vo.EssEquipVo;
import com.cdz360.iot.model.pv.dto.PvRtDataDto;
import com.cdz360.iot.model.pv.vo.RedisPvRtData;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class RedisEssEquipRtDataService {

    private final static DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(
        "yyyyMMdd HH:mm:ss");

    private final static Calendar CALENDAR = Calendar.getInstance();

    private static final String PRE_PV_REDIS_KEY = "pv:";
    private static final String PRE_REDIS_KEY = "ess:";

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 今天光伏运行时数据压入
     *
     * @param dno  逆变器编号
     * @param data 数据
     */
    public void pushPvRtData(String dno, PvRtDataDto data) {
        String key = pvFormatKey(dno, LocalDate.now());

        redisTemplate.opsForList()
            .rightPush(key, JsonUtils.toJsonString(RedisPvRtData.convert(data)));
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
    }

    private static String pvFormatKey(String dno, LocalDate date) {
        return PRE_PV_REDIS_KEY + dno + ":" + date.format(DATE_FORMATTER);
    }

    /**
     * ESS挂载设备运行时数据压入
     *
     * @param essDno  ESS设备编号
     * @param equipId 设备ID
     * @param data    数据
     */
    public void pushRtData(String timeZone, String essDno, Long equipId, JsonNode data) {
        LocalDateTime now = TimeZoneParser.nowByTz(timeZone);
        String key = formatKey(essDno, equipId, now.toLocalDate());
        redisTemplate.opsForList()
            .rightPush(key, JsonUtils.toJsonString(new RedisEquipRtData<JsonNode>()
                .setData(data)
                .setTime(now)));
        redisTemplate.expire(key, 2, TimeUnit.DAYS); // 近2 * 24h可用
    }

    public void pushRtDataLt(String timeZone, String essDno, Long equipId, JsonNode data) {
        LocalDateTime now = TimeZoneParser.nowByTz(timeZone);
        String key = formatKey(essDno, equipId, now.toLocalDate());
        redisTemplate.opsForList()
            .rightPush(key, JsonUtils.toJsonString(new RedisEquipRtData<JsonNode>()
                .setData(data)
                .setTime(now)));
        redisTemplate.expire(key, 2, TimeUnit.DAYS); // 近2 * 24h可用
    }

    /**
     * 电池组运行时数据压入
     *
     * @param essDno  ESS设备编号
     * @param equipId 设备ID
     * @param lumSn   电池组SN
     * @param data    数据
     */
    public void pushBatteryPackRtData(String essDno, Long equipId, Long lumSn, BatteryPack data) {
        String key = formatBatteryPackKey(essDno, equipId, lumSn, LocalDate.now());

        redisTemplate.opsForList()
            .rightPush(key, JsonUtils.toJsonString(new RedisEquipRtData<BatteryPack>()
                .setData(data)
                .setTime(LocalDateTime.now())));
        redisTemplate.expire(key, 2, TimeUnit.DAYS); // 近2 * 24h可用
    }

    /**
     * 电池堆电池簇数据
     *
     * @param essDno ESS设备编号
     * @param data   数据
     */
    public void pushBatteryStackAndCluster(String essDno, Long stackEquipId, Long equipId,
        JsonNode data) {
        //ObjectNode objectNode = data.deepCopy();
        Set<String> batterySet = redisTemplate.opsForSet()
            .members(PRE_REDIS_KEY + essDno + ":battery");
        BatteryCluster needBatteryCluster = JsonUtils.fromJson(data, BatteryCluster.class);
        AtomicReference<BatteryStackRelation> batteryStackRelation = new AtomicReference<>();
        batterySet.forEach(o -> {
            BatteryStackRelation oBatteryStackRelation = JsonUtils.fromJson(o,
                BatteryStackRelation.class);
            Long batteryStackId = oBatteryStackRelation.getBatteryStackId();
            if (stackEquipId.equals(batteryStackId)) {
                batteryStackRelation.set(oBatteryStackRelation);

                List<BatteryClusterRelation> batteryClusterRelationList = oBatteryStackRelation.getBatteryClusterRelationList();
                AtomicBoolean clusterIdExistFlag = new AtomicBoolean(false);
                batteryClusterRelationList.forEach(c -> {
                    if (c.getBatteryClusterId().equals(equipId)) {
                        clusterIdExistFlag.set(true);
                    }
                });
                if (!clusterIdExistFlag.get()) {
                    BatteryClusterRelation batteryClusterRelation = new BatteryClusterRelation();
                    batteryClusterRelation.setBatteryClusterId(equipId);
                    batteryClusterRelation.setClusterNo(needBatteryCluster.getClusterNo());
                    batteryClusterRelation.setBatteryClusterTypeId(
                        EssEquipType.BATTERY_PACK.getCode());

                    List<BatteryPackRelation> batteryPackRelationList = new ArrayList<>();
                    List<BatteryPack> packList = needBatteryCluster.getPackList();
                    if (packList != null && packList.size() != 0) {
                        packList.forEach(p -> {
                            BatteryPackRelation batteryPackRelation = new BatteryPackRelation();
                            batteryPackRelation.setBatteryPackId(p.getPackNo());
                            batteryPackRelation.setBatteryPackTypeId(
                                EssEquipType.BATTERY_PACK.getCode());
                            batteryPackRelation.setBatteryPackSN(p.getLmuSn());
                            batteryPackRelation.setPackNo(p.getPackNo());
                            batteryPackRelationList.add(batteryPackRelation);
                        });
                    }
                    batteryClusterRelation.setBatteryPackRelationList(batteryPackRelationList);
                    batteryClusterRelationList.add(batteryClusterRelation);
                }

                batteryStackRelation.get().setBatteryStackId(stackEquipId);
                batteryStackRelation.get()
                    .setBatteryStackTypeId(EssEquipType.BATTERY_STACK.getCode());
                batteryStackRelation.get()
                    .setBatteryClusterRelationList(batteryClusterRelationList);

                redisTemplate.opsForSet().remove(PRE_REDIS_KEY + essDno + ":battery", o);
            }
        });
        if (batteryStackRelation.get() == null) {
            batteryStackRelation.set(new BatteryStackRelation());
            List<BatteryClusterRelation> batteryClusterRelationList = new ArrayList<>();

            BatteryClusterRelation batteryClusterRelation = new BatteryClusterRelation();
            batteryClusterRelation.setBatteryClusterId(equipId);
            batteryClusterRelation.setClusterNo(needBatteryCluster.getClusterNo());
            batteryClusterRelation.setBatteryClusterTypeId(EssEquipType.BATTERY_PACK.getCode());

            List<BatteryPackRelation> batteryPackRelationList = new ArrayList<>();
            List<BatteryPack> packList = needBatteryCluster.getPackList();
            if (packList != null && packList.size() != 0) {
                packList.forEach(p -> {
                    BatteryPackRelation batteryPackRelation = new BatteryPackRelation();
                    batteryPackRelation.setBatteryPackId(p.getPackNo());
                    batteryPackRelation.setBatteryPackTypeId(EssEquipType.BATTERY_PACK.getCode());
                    batteryPackRelation.setBatteryPackSN(p.getLmuSn());
                    batteryPackRelation.setPackNo(p.getPackNo());
                    batteryPackRelationList.add(batteryPackRelation);
                });
            }
            batteryClusterRelation.setBatteryPackRelationList(batteryPackRelationList);
            batteryClusterRelationList.add(batteryClusterRelation);

            batteryStackRelation.get().setBatteryStackId(stackEquipId);
            batteryStackRelation.get().setBatteryStackTypeId(EssEquipType.BATTERY_STACK.getCode());
            batteryStackRelation.get().setBatteryClusterRelationList(batteryClusterRelationList);
        }
        redisTemplate.opsForSet()
            .add(PRE_REDIS_KEY + essDno + ":battery", JsonUtils.toJsonString(batteryStackRelation));
    }

    public Mono<ListResponse<Long>> findBatteryClusterNosByBatteryStack(String essDno,
        Long batteryStackId) {
        Set<String> batterySet = findBatteryClustersByBatteryStack(essDno);
        List<Long> clusterNos = new ArrayList<Long>();
        batterySet.forEach(o -> {
            BatteryStackRelation batteryStackRelation = JsonUtils.fromJson(o,
                BatteryStackRelation.class);
            if (batteryStackRelation.getBatteryStackId().equals(batteryStackId)) {
                batteryStackRelation.getBatteryClusterRelationList().forEach(e -> {
                    clusterNos.add(e.getClusterNo());
                });
            }
        });
        return Mono.just(clusterNos).map(e -> {
            return RestUtils.buildListResponse(e);
        });
    }

    public Mono<ListResponse<EssEquipBatteryPackSimpleVo>> findBatteryPackNosByBatteryClusterNo(
        String essDno, Long batteryStackId, Long batteryClusterNo) {
        Set<String> batterySet = findBatteryClustersByBatteryStack(essDno);
        List<EssEquipBatteryPackSimpleVo> batteryPackList = new ArrayList<EssEquipBatteryPackSimpleVo>();
        batterySet.forEach(o -> {
            BatteryStackRelation batteryStackRelation = JsonUtils.fromJson(o,
                BatteryStackRelation.class);
            if (batteryStackRelation.getBatteryStackId().equals(batteryStackId)) {
                batteryStackRelation.getBatteryClusterRelationList().forEach(e -> {
                    if (e.getClusterNo().equals(batteryClusterNo)) {
                        e.getBatteryPackRelationList().forEach(f -> {
                            EssEquipBatteryPackSimpleVo essEquipBatteryPackSimpleVo = new EssEquipBatteryPackSimpleVo();
                            essEquipBatteryPackSimpleVo.setBatteryPackSN(f.getBatteryPackSN());
                            essEquipBatteryPackSimpleVo.setPackNo(f.getPackNo());
                            batteryPackList.add(essEquipBatteryPackSimpleVo);
                        });
                    }
                });
            }
        });
        return Mono.just(batteryPackList).map(e -> {
            return RestUtils.buildListResponse(e);
        });
    }

    public Set<String> findBatteryClustersByBatteryStack(String essDno) {
        String key = PRE_REDIS_KEY + essDno + ":battery";
        Set<String> batterySet = redisTemplate.opsForSet().members(key);
        return batterySet;
    }

    /**
     * 获取最近一条运行时数据
     *
     * @param essDno  ESS设备编号
     * @param equipId 设备ID
     * @return redis 中原始值，需自己转换。eg: RedisEquipRtData<Pcs> pcs = JsonUtils.fromJson("", new
     * TypeReference<>() {});
     */
    public String latestRtData(String essDno, Long equipId) {
        return this.dateRtData(essDno, equipId, LocalDate.now());
    }

    public <T> T latestRtData(String essDno, Long equipId, TypeReference<T> typeReference) {
        return this.latestRtData(essDno, equipId, LocalDate.now(), typeReference);
    }

    public <T> T latestRtData(String essDno, Long equipId, LocalDate date,
        TypeReference<T> typeReference) {
        String cache = this.dateRtData(essDno, equipId, date);
        if (StringUtils.isBlank(cache)) {
            return null;
        }
        return JsonUtils.fromJson(cache, typeReference);
    }

    public <T> T latestBatteryPackRtData(String essDno, Long equipId, Long lumSn,
        TypeReference<T> typeReference) {
        String key = formatBatteryPackKey(essDno, equipId, lumSn, LocalDate.now());
        String cache = this.dateRtData(key);
        if (StringUtils.isBlank(cache)) {
            return null;
        }
        return JsonUtils.fromJson(cache, typeReference);
    }

    /**
     * 获取最近一条运行时数据
     *
     * @param essDno  ESS设备编号
     * @param equipId 设备ID
     * @param date    日期
     * @return
     */
    public String dateRtData(String essDno, Long equipId, LocalDate date) {
        String key = formatKey(essDno, equipId, date);
        return this.dateRtData(key);
    }

    /**
     * 获取最近一条运行时数据
     *
     * @param key redis中key
     * @return
     */
    public String dateRtData(String key) {
        Long size = redisTemplate.opsForList().size(key);
        if (null == size || size == 0) {
            return null;
        }

        String value = redisTemplate.opsForList()
            .index(key, size - 1);
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return value;
    }

    private static String formatKey(String essDno, Long equipId, LocalDate date) {
        return PRE_REDIS_KEY + essDno + ":" + equipId + ":" + date.format(DATE_FORMATTER);
    }

    private static String formatBatteryPackKey(String essDno, Long equipId, Long lumSn,
        LocalDate date) {
        return PRE_REDIS_KEY + essDno + ":" + equipId + ":" + lumSn + ":" + date.format(
            DATE_FORMATTER);
    }

    public Mono<EssDataBi> gwAllPCSTotalDetail(List<EssEquipVo> equipVoList, LocalDate date) {
        return Flux.fromIterable(equipVoList)
            .map(equip -> {
                String json = this.dateRtData(equip.getEssDno(), equip.getEquipId(), date);
                if (null == json) {
                    return new EssDataBi();
                }

                RedisEquipRtData<Pcs> pcs = JsonUtils.fromJson(json, new TypeReference<>() {
                });
                // FIXME: 直流放电/充电
                return new EssDataBi()
                    .setInKwh(BigDecimal.valueOf(pcs.getData().getAccumulativeChargedEnergy()))
                    .setOutKwh(BigDecimal.valueOf(pcs.getData().getAccumulativeDischargedEnergy()));
            })
            .collectList()
            .map(list -> {
                BigDecimal inKwh = list.stream().map(EssDataBi::getInKwh)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal outKwh = list.stream().map(EssDataBi::getOutKwh)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                return new EssDataBi()
                    .setInKwh(inKwh)
                    .setOutKwh(outKwh);
            });
    }

    public Mono<EssDataBi> gwAllPCSDayBi(
        List<EssEquipVo> equipVoList, LocalDate date,
        List<PriceItemPo> inPriceItems, List<PriceItemPo> outPriceItems) {

        return Flux.fromIterable(equipVoList)
            .flatMap(equip -> {
                String key = formatKey(equip.getEssDno(), equip.getEquipId(), date);
                RedisEquipRtData<Pcs> pcs = this.latestRtData(
                    equip.getEssDno(), equip.getEquipId(), date, new TypeReference<>() {
                    });
                if (null == pcs) {
                    return Mono.just(new EssDataBi()
                        .setOutIncome(BigDecimal.ZERO)
                        .setInExpend(BigDecimal.ZERO)
                        .setProfit(BigDecimal.ZERO)
                        .setInKwh(BigDecimal.ZERO)
                        .setOutKwh(BigDecimal.ZERO));
                }

                return this.gtiDayProfit4RedisKey(key, inPriceItems, outPriceItems)
                    .map(detail -> new EssDataBi()
                        .setOutIncome(detail.getFirst())
                        .setInExpend(detail.getSecond())
                        .setProfit(detail.getFirst().subtract(detail.getSecond()))
                        .setInKwh(BigDecimal.valueOf(pcs.getData().getAcChargingElecToday()))
                        .setOutKwh(BigDecimal.valueOf(pcs.getData().getAcDischargeElecToday())));
            })
            .collectList()
            .map(list -> {
                BigDecimal profit = list.stream().map(EssDataBi::getProfit)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal inKwh = list.stream().map(EssDataBi::getInKwh)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal inExpend = list.stream().map(EssDataBi::getInExpend)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal outKwh = list.stream().map(EssDataBi::getOutKwh)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal outIncome = list.stream().map(EssDataBi::getOutIncome)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                return new EssDataBi()
                    .setProfit(profit)
                    .setInKwh(inKwh)
                    .setInExpend(inExpend)
                    .setOutKwh(outKwh)
                    .setOutIncome(outIncome);
            });
    }


    /**
     * 获取PCS运行时数据
     *
     * @param start
     * @param end
     * @return
     */
    public List<RedisEquipRtData<Pcs>> findPcsRtData(String key, long start, long end) {
        Long size = redisTemplate.opsForList().size(key);
        if (null == size) {
            return List.of();
        }

        if (end > size) {
            end = -1;
        }

        List<String> range = redisTemplate.opsForList()
            .range(key, start, end);
        if (CollectionUtils.isEmpty(range)) {
            return List.of();
        }
        return range.stream()
            .map(value -> JsonUtils.fromJson(value, new TypeReference<RedisEquipRtData<Pcs>>() {
            }))
            .collect(Collectors.toList());
    }

    public Mono<Pair<BigDecimal, BigDecimal>> gtiDayProfit4RedisKey(
        String key, List<PriceItemPo> inPriceItems, List<PriceItemPo> outPriceItems) {
        BigDecimal outIncome = BigDecimal.ZERO; // 放电收入
        BigDecimal inExpend = BigDecimal.ZERO; // 充电支出
        RedisEquipRtData<Pcs> last = null;
        try {
            int start = 0;
            List<RedisEquipRtData<Pcs>> dataList = this.findPcsRtData(key, start, start + 1000);
            if (!CollectionUtils.isEmpty(dataList)) {
                int size = dataList.size();
                int idx = 0;
                while (true) {
                    if (idx == size) {
                        dataList = this.findPcsRtData(key, start, start + 1000);
                        if (org.springframework.util.CollectionUtils.isEmpty(dataList)) {
                            break;
                        }

                        size = dataList.size();
                        idx = 0;
                    }

                    start++;
                    RedisEquipRtData<Pcs> rtData = dataList.get(idx++);
                    if (null != rtData) {
                        Integer inKwh = null;
                        Integer outKwh = null;
                        if (null == last) {
                            inKwh = rtData.getData().getAcChargingElecToday();
                            outKwh = rtData.getData().getAcDischargeElecToday();
                        } else {
                            inKwh = rtData.getData().getAcChargingElecToday() - last.getData()
                                .getAcChargingElecToday();
                            outKwh = rtData.getData().getAcDischargeElecToday() - last.getData()
                                .getAcDischargeElecToday();
                        }

                        if (null != inKwh) {
                            inExpend = inExpend.add(BigDecimal.valueOf(inKwh)
                                .multiply(getYuanPerKwh(rtData.getTime(), inPriceItems)));
                        }

                        if (null != outKwh) {
                            outIncome = outIncome.add(BigDecimal.valueOf(outKwh)
                                .multiply(getYuanPerKwh(rtData.getTime(), outPriceItems)));
                        }

                        last = rtData;
                        rtData = null;
                    }
                }
            }
        } catch (Exception e) {
            // nothing
            log.error("err = {}", e.getMessage(), e);
        }
        return Mono.just(Pair.of(outIncome, inExpend));
    }

    // FIXME: 这里可以返回找到的时段信息
    private static BigDecimal getYuanPerKwh(LocalDateTime time, List<PriceItemPo> temp) {
        if (CollectionUtils.isEmpty(temp)) {
            return BigDecimal.ZERO;
        }

        if (temp.size() == 1) {
            return temp.get(0).getPrice();
        }

        int minute = time.getMinute();
        Optional<PriceItemPo> target = temp.stream()
            .filter(i -> i.getStartTime() <= minute && minute < i.getStopTime())
            .findFirst();
        return target.isPresent() ? target.get().getPrice() : BigDecimal.ZERO;
    }

    public void recordStatus2Redis(String essDno, String data) {
        redisTemplate.opsForValue().set(statusRecordKey(essDno), data);
    }

    private static String statusRecordKey(String essDno) {
        return "equip:status:rec:" + essDno;
    }

    private static String realTimeCfgKey(String essDno) {
        return "ess:real-time:cfg:" + essDno;
    }

    public void essRealTimeCfg(String essDno, String data) {
        // 实时配置只保留2分钟
        redisTemplate.opsForValue().set(realTimeCfgKey(essDno), data, 2, TimeUnit.MINUTES);
    }

    public <T> T getStatusRecord(String essDno, TypeReference<T> typeReference) {
        String cache = this.getStatusRecord(essDno);
        if (StringUtils.isBlank(cache)) {
            return null;
        }
        return JsonUtils.fromJson(cache, typeReference);
    }

    public String getStatusRecord(String essDno) {
        return redisTemplate.opsForValue().get(statusRecordKey(essDno));
    }

}
