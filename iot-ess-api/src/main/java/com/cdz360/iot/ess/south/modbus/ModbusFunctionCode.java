package com.cdz360.iot.ess.south.modbus;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "MODBUS功能CODE(挑取部分CODE)")
public enum ModbusFunctionCode {
    READ_COILS(0x01, "读取线圈"),
    READ_DISCRETE_INPUTS(0x02, "读取离散输入"),
    READ_HOLDING_REGISTERS(0x03, "读取保持寄存器"),
    READ_INPUT_REGISTERS(0x04, "读取输入寄存器"),
    WRITE_SINGLE_COIL(0x05, "写单个线圈"),
    WRITE_SINGLE_REGISTER(0x06, "写单个寄存器"),
    WRITE_MULTIPLE_COILS(0x0F, "写多个线圈"),
    WRITE_MULTIPLE_REGISTERS(0x10, "写多个寄存器");
    private final int code;
    private final String desc;

    ModbusFunctionCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public short getCode() {
        return (short) code;
    }
}
