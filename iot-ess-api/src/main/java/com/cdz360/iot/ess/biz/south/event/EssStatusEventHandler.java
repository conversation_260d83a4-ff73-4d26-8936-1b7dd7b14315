package com.cdz360.iot.ess.biz.south.event;

import static java.time.temporal.ChronoField.DAY_OF_MONTH;
import static java.time.temporal.ChronoField.HOUR_OF_DAY;
import static java.time.temporal.ChronoField.MINUTE_OF_HOUR;
import static java.time.temporal.ChronoField.MONTH_OF_YEAR;
import static java.time.temporal.ChronoField.SECOND_OF_MINUTE;
import static java.time.temporal.ChronoField.YEAR;

import com.cdz360.base.model.es.dto.EssAlarmNotify;
import com.cdz360.base.model.es.type.DeviceWarnType;
import com.cdz360.base.model.es.type.WarnDeviceType;
import com.cdz360.base.model.es.type.hi.BatOpStatus;
import com.cdz360.base.model.es.type.hi.HybridInverterOpStatus;
import com.cdz360.base.model.es.type.hi.PvOpStatus;
import com.cdz360.base.model.es.type.hi.SinexcelErrorCode;
import com.cdz360.base.model.es.vo.hi.InverterRtData;
import com.cdz360.base.model.es.vo.hi.InverterRtInfo;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.sync.event.ess.EssPushEventType;
import com.cdz360.data.sync.event.ess.EssStatusAlarmEvent;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.common.utils.TimeZoneParser;
import com.cdz360.iot.ess.utils.RedisMqUtils;
import com.cdz360.iot.model.dtu.po.EssDtuEssRefPo;
import com.cdz360.iot.model.dtu.po.EssDtuPo;
import com.cdz360.iot.model.ess.dto.RedisEquipRtData;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.ess.po.UserDeviceRefPo;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.type.EquipAlertStatus;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssStatusEventHandler extends AbstractEssGwPushEventHandler {

    @Autowired
    private RedisMqUtils redisMqUtils;

    @Autowired
    private DcEventPublisher dcEventPublish;

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(EssPushEventType.STATUS_ALARM, this);
    }

    @Override
    public Mono<Boolean> process(JsonNode json) {
        log.debug("处理设备状态与告警: {}", JsonUtils.toJsonString(json));
        EssStatusAlarmEvent event = JsonUtils.fromJson(json, EssStatusAlarmEvent.class);
        InverterRtInfo data = event.getData();
        if (null != data) {
            EssDtuPo dtu = essDtuRoDs.getBySerialNo(event.getSerialNo());
            if (null == dtu) {
                log.warn("ESS透传设备不存在");
                return Mono.just(false);
            }

            List<EssDtuEssRefPo> refList = essDtuEssRefRoDs.getBySerialNo(event.getSerialNo());
            if (CollectionUtils.isNotEmpty(refList)) {
                // 逆变器状态有变更
                this.invStatusChange(refList.get(0).getDno(), data);

                // Redis中记录状态数据
                this.recordStatus2Redis(
                    refList.get(0).getDno(), JsonUtils.toJsonString(data)); // 默认一对一关系

                GwInfoDto gw = gwInfoRoDs.getByGwno(dtu.getGwno());
                UserDeviceRefPo bindRef = userDeviceRefRoDs.getMasterByDno(
                    refList.get(0).getDno());

                refList.forEach(ref -> {
                    EssPo ess = essRoDs.getByDno(ref.getDno());
                    if (null != ess) {
                        if (!EquipStatus.NORMAL.equals(ess.getStatus())) {
                            essRwDs.updateStatus(ess.getDno(), EquipStatus.NORMAL);
                        }

                        // ess 告警状态调整
                        essRwDs.updateAlertStatus(ess.getDno(),
                            CollectionUtils.isNotEmpty(data.getSinexcelInverterErrorList()) ||
                                CollectionUtils.isNotEmpty(data.getSinexcelRectifierErrorList()) ?
                                EquipAlertStatus.ABNORMAL : EquipAlertStatus.OK);

                        // ess 中逆变器告警状态处理
//                        LocalDateTime now = deviceLdt(ref.getDno());
                        String tz = ess.getTimeZone();
                        LocalDateTime now = TimeZoneParser.nowByTz(tz);
                        List<EssAlarmNotify.ErrorObj> errorObjs = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(data.getSinexcelInverterErrorList())) {
                            errorObjs.addAll(data.getSinexcelInverterErrorList().stream()
                                .map(x -> {
                                    EssAlarmNotify.ErrorObj errorObj = new EssAlarmNotify.ErrorObj();
                                    errorObj.setWarnType(DeviceWarnType.INVERTER)
                                        .setWarnCode((long) x.getCode())
                                        .setLts(now);
                                    return errorObj;
                                }).collect(Collectors.toList()));
                        }

                        // ess 中逆变器组件告警状态处理
                        if (CollectionUtils.isNotEmpty(data.getSinexcelMonitorErrorList())) {
                            errorObjs.addAll(data.getSinexcelMonitorErrorList().stream()
                                .map(x -> {
                                    EssAlarmNotify.ErrorObj errorObj = new EssAlarmNotify.ErrorObj();
                                    errorObj.setWarnType(DeviceWarnType.MONITOR)
                                        .setWarnCode((long) x.getCode())
                                        .setLts(now);
                                    return errorObj;
                                }).collect(Collectors.toList()));
                        }

                        // ess 中整流器告警状态处理
                        if (CollectionUtils.isNotEmpty(data.getSinexcelRectifierErrorList())) {
                            errorObjs.addAll(data.getSinexcelRectifierErrorList().stream()
                                .map(x -> {
                                    EssAlarmNotify.ErrorObj errorObj = new EssAlarmNotify.ErrorObj();
                                    errorObj.setWarnType(DeviceWarnType.RECTIFIER)
                                        .setWarnCode((long) x.getCode())
                                        .setLts(now);
                                    return errorObj;
                                }).collect(Collectors.toList()));
                        }
                        EssAlarmNotify alarmNotify = new EssAlarmNotify();
                        alarmNotify.setGwName(gw.getName())
                            .setGwno(gw.getGwno())
                            .setDno(ess.getDno())
                            .setDeviceType(WarnDeviceType.USER_ESS)
                            .setDeviceName(ess.getName())
                            .setVersion("#" + (StringUtils.isBlank(ess.getSoftVersion()) ?
                                "00" : ess.getSoftVersion()))
                            .setTz(tz)
                            .setErrorList(errorObjs); // 暂时默认版本

                        if (null != bindRef) {
                            alarmNotify.setCountryCode(bindRef.getCountryCode());
                        }

                        this.dcEventPublish.publishEssAlarm(alarmNotify);

                        // 电池状态处理
                        if (CollectionUtils.isNotEmpty(data.getBatOpStatus())) {
                            for (int i = 0; i < data.getBatOpStatus().size(); i++) {
                                BatOpStatus bs = data.getBatOpStatus().get(i);
                            }
                        }

                        // 光伏状态处理
                        if (CollectionUtils.isNotEmpty(data.getPvOpStatus())) {
                            for (int i = 0; i < data.getPvOpStatus().size(); i++) {
                                PvOpStatus pv = data.getPvOpStatus().get(i);
                            }
                        }

                        // 光伏状态处理
                        if (CollectionUtils.isNotEmpty(data.getSinexcelInverterErrorList())) {
                            // 0x1062 光伏1未接入
                            essEquipRwDs.updateEssEquipStatus(ref.getDno(),
                                List.of((long) EssEquipType.PV_INV.getCode()),
                                !data.getSinexcelInverterErrorList()
                                    .contains(SinexcelErrorCode.PV_NOT_CONNECTED_1)
                                    ? EquipStatus.NORMAL
                                    : EquipStatus.OFFLINE, null);

                            // 0x1065 光伏2未接入
                            essEquipRwDs.updateEssEquipStatus(ref.getDno(),
                                List.of((long) EssEquipType.PV_INV.getCode() + 1),
                                !data.getSinexcelInverterErrorList()
                                    .contains(SinexcelErrorCode.PV_NOT_CONNECTED_2)
                                    ? EquipStatus.NORMAL
                                    : EquipStatus.OFFLINE, null);
                        }
                    }
                });
            }
        }
        return Mono.just(true);
    }

    private LocalDateTime deviceLdt(String dno) {
        RedisEquipRtData<InverterRtData> cache = redisEssEquipRtDataService.latestRtData(
            dno, 0L, new TypeReference<RedisEquipRtData<InverterRtData>>() {
            });
        if (null == cache || null == cache.getData()) {
            return LocalDateTime.now();
        }
        
        boolean valid = null != cache.getData().getYear() &&
            YEAR.range().isValidValue(cache.getData().getYear());

        if (valid) {
            valid = null != cache.getData().getMonth() &&
                MONTH_OF_YEAR.range().isValidValue(cache.getData().getMonth());
        }

        if (valid) {
            valid = null != cache.getData().getDay() &&
                DAY_OF_MONTH.range().isValidValue(cache.getData().getDay());
        }

        if (valid) {
            valid = null != cache.getData().getHour() &&
                HOUR_OF_DAY.range().isValidValue(cache.getData().getHour());
        }
        if (valid) {
            valid = null != cache.getData().getMinute() &&
                MINUTE_OF_HOUR.range().isValidValue(cache.getData().getMinute());
        }
        if (valid) {
            valid = null != cache.getData().getSecond() &&
                SECOND_OF_MINUTE.range().isValidValue(cache.getData().getSecond());
        }

        LocalDateTime ldt = null;
        if (valid) {
            ldt = LocalDateTime.of(
                cache.getData().getYear(),
                cache.getData().getMonth(),
                cache.getData().getDay(),
                cache.getData().getHour(),
                cache.getData().getMinute(),
                cache.getData().getSecond());
        } else {
            ldt = LocalDateTime.now();
        }
        return ldt;
    }

    private void invStatusChange(String dno, InverterRtInfo data) {
        InverterRtInfo statusRecord = redisEssEquipRtDataService.getStatusRecord(dno,
            new TypeReference<InverterRtInfo>() {
            });
        if (null == statusRecord || null == statusRecord.getHybridInverterOpStatus()) {
            val node = new ObjectMapper().createObjectNode();
            node.put("dno", dno);
            redisMqUtils.publish(dno, JsonUtils.toJsonString(node));
        } else {
            HybridInverterOpStatus opStatus = statusRecord.getHybridInverterOpStatus();
            HybridInverterOpStatus newStatus = data.getHybridInverterOpStatus();
            if (!opStatus.equals(newStatus)) {
                val node = new ObjectMapper().createObjectNode();
                node.put("dno", dno);
                redisMqUtils.publish(dno, JsonUtils.toJsonString(node));
            }
        }
    }
}
