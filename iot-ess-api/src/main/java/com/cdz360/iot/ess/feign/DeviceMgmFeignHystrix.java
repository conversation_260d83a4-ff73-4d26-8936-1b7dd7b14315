package com.cdz360.iot.ess.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.evse.dto.UpgradeTaskDto;
import com.cdz360.iot.model.evse.param.StartUpgradeTaskParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class DeviceMgmFeignHystrix implements FallbackFactory<DeviceMgmFeignClient> {

    @Override
    public DeviceMgmFeignClient apply(Throwable throwable) {
        log.error("err = {}", throwable.getMessage(), throwable);
        return new DeviceMgmFeignClient() {

            @Override
            public Mono<ObjectResponse<UpgradeTaskDto>> startUpgradeTask(
                StartUpgradeTaskParam param) {
                log.error("服务[{}]接口熔断 - 升级包信息下发, param = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, param);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }
}
