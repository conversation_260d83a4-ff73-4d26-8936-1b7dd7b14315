package com.cdz360.iot.ess.south.mqtt;

import com.cdz360.base.model.es.vo.EmuDailyFeeFull;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.ess.biz.south.EssSouthDataService;
import com.cdz360.iot.ess.model.emu.dto.EmuDailyFeeFullDto;
import com.cdz360.iot.mqtt.IMqttMsgHandler;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * EMU 每日计费数据接收器
 */
@Slf4j
@Service
public class EmuMqttDailyFeeHandler implements IMqttMsgHandler {

    @Autowired
    private EssSouthDataService essSouthDataService;


    @PostConstruct
    public void init() {
        EmuMqttHandlerFactory.addHandler(EmuMqttHandlerName.EMU_DAILY_FEE, this);
    }

    @Override
    public boolean handleMessage(String handlerName, String msg) {
        log.info("收到 mqtt 消息 {}", msg);
        EmuDailyFeeFullDto emuDailyFee = JsonUtils.fromJson(msg, EmuDailyFeeFullDto.class);
        log.info("反序列化后 emuDailyFee = {}", emuDailyFee);

        essSouthDataService.saveDailyEmuData(emuDailyFee)
            .subscribe();

        return true;
    }


}
